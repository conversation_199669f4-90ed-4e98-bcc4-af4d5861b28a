/** @type {import('tailwindcss').Config} */
export default {
  darkMode: ["class"],
  content: [
    "./pages/**/*.{ts,tsx}",
    "./components/**/*.{ts,tsx}",
    "./app/**/*.{ts,tsx}",
    "./src/**/*.{ts,tsx}",
  ],
  prefix: "",
  theme: {
    container: {
      center: true,
      padding: "2rem",
      screens: {
        "2xl": "1400px",
      },
    },
    extend: {
      colors: {
        // CSS variables for shadcn/ui compatibility
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },

        // Brand colors
        brand: {
          primary: "#E40066",
          secondary: "#03CEA4",
          accent: "#9747FF",
        },

        // Semantic colors
        success: "#03CEA4",
        warning: "#FFB800",
        error: "#E40066",
        info: "#9747FF",
      },

      backgroundImage: {
        "gradient-primary": "linear-gradient(135deg, #E40066 0%, #9747FF 100%)",
        "gradient-secondary":
          "linear-gradient(135deg, #03CEA4 0%, #9747FF 100%)",
        "gradient-hero":
          "linear-gradient(135deg, #E40066 0%, #03CEA4 50%, #9747FF 100%)",
      },

      boxShadow: {
        primary:
          "0 10px 15px -3px rgb(228 0 102 / 0.3), 0 4px 6px -4px rgb(228 0 102 / 0.1)",
        secondary:
          "0 10px 15px -3px rgb(3 206 164 / 0.3), 0 4px 6px -4px rgb(3 206 164 / 0.1)",
        accent:
          "0 10px 15px -3px rgb(151 71 255 / 0.3), 0 4px 6px -4px rgb(151 71 255 / 0.1)",
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
      keyframes: {
        "accordion-down": {
          from: { height: "0" },
          to: { height: "var(--radix-accordion-content-height)" },
        },
        "accordion-up": {
          from: { height: "var(--radix-accordion-content-height)" },
          to: { height: "0" },
        },
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
      },
    },
  },
  plugins: [require("tailwindcss-animate")],
};
