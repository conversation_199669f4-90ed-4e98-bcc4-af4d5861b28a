# Component Documentation

## CarouselHeroBanner Component

A highly customizable carousel hero banner component with auto-play, navigation, and pagination features.

### Features
- **Auto-play**: Automatic slide transitions with customizable intervals
- **Navigation**: Previous/Next arrow buttons
- **Pagination**: Dot indicators for slide navigation
- **Responsive**: Works on both desktop and mobile
- **Customizable**: Multiple height options, badges, action buttons

### Props
```typescript
interface CarouselHeroBannerProps {
  slides: HeroSlide[];
  height?: "sm" | "md" | "lg" | "xl";
  autoPlay?: boolean;
  autoPlayInterval?: number;
  showNavigation?: boolean;
  showPagination?: boolean;
  className?: string;
  onSlideChange?: (slideIndex: number) => void;
}
```

### Usage Example
```tsx
import { CarouselHeroBanner } from "../components/common/CarouselHeroBanner";

const heroSlides = [
  {
    id: "slide-1",
    title: "One Piece",
    description: "Epic adventure story...",
    backgroundImage: "/images/hero-bg.jpg",
    badge: { text: "Out now", icon: "🎉", variant: "default" },
    categoryBadge: { text: "Manga", icon: "📖", variant: "manga" },
    chapterInfo: { text: "Chapter 1012", onClick: () => {} },
    actionButton: { text: "READ", variant: "primary", onClick: () => {} },
  },
];

<CarouselHeroBanner
  slides={heroSlides}
  height="lg"
  autoPlay={true}
  autoPlayInterval={5000}
  showNavigation={true}
  showPagination={true}
/>
```

## Categories Component

A flexible and reusable categories component with multiple variants and layouts.

### Features
- **Multiple Variants**: Pills, rounded, default styles
- **Flexible Layout**: Grid or flex layouts
- **Customizable**: Sizes, colors, styling options
- **Interactive**: Click handlers and selected states
- **Preset Components**: HotCategories, PopularCategories, GenreCategories

### Props
```typescript
interface CategoriesProps {
  title?: string;
  titleHighlight?: string;
  categories: Category[];
  selectedCategory?: string;
  onCategorySelect?: (categoryId: string) => void;
  variant?: "default" | "rounded" | "pills";
  size?: "sm" | "md" | "lg";
  layout?: "grid" | "flex";
  showCount?: boolean;
  className?: string;
  // ... more styling props
}
```

### Usage Examples

#### Basic Categories
```tsx
import { Categories } from "../components/common/Categories";

const categories = [
  { id: "action", name: "Action", count: 1250 },
  { id: "romance", name: "Romance", count: 890 },
];

<Categories
  title="Hot"
  titleHighlight="Categories"
  categories={categories}
  selectedCategory={selectedCategory}
  onCategorySelect={handleCategorySelect}
  variant="pills"
  size="md"
/>
```

#### Preset Components
```tsx
import { HotCategories, PopularCategories, GenreCategories } from "../components/common/Categories";

// Hot Categories (pills variant)
<HotCategories
  categories={categories}
  selectedCategory={selectedCategory}
  onCategorySelect={handleCategorySelect}
/>

// Popular Categories (rounded variant)
<PopularCategories
  categories={categories}
  selectedCategory={selectedCategory}
  onCategorySelect={handleCategorySelect}
/>

// Genre Categories (grid layout with counts)
<GenreCategories
  categories={categories}
  selectedCategory={selectedCategory}
  onCategorySelect={handleCategorySelect}
/>
```

## Integration in HomePage

The HomePage has been updated to use both new components:

```tsx
import { CarouselHeroBanner } from "../components/common/CarouselHeroBanner";
import { HotCategories } from "../components/common/Categories";

export const HomePage: React.FC = () => {
  const [selectedCategory, setSelectedCategory] = useState<string>("all-category");

  return (
    <div className="bg-gray-50">
      {/* Hero Section with Carousel */}
      <CarouselHeroBanner
        slides={heroSlides}
        height="lg"
        autoPlay={true}
        autoPlayInterval={5000}
        showNavigation={true}
        showPagination={true}
        onSlideChange={handleSlideChange}
      />

      {/* Categories Section */}
      <div className="p-4 lg:p-6">
        <HotCategories
          categories={categories}
          selectedCategory={selectedCategory}
          onCategorySelect={handleCategorySelect}
        />
      </div>
    </div>
  );
};
```

## Styling and Theme

Both components follow the project's theme system:
- **Primary Colors**: E40066 (pink), 03CEA4 (teal), 9747FF (purple)
- **Accent Color**: F4B333 (yellow) for active states
- **Responsive Design**: Mobile-first approach
- **Tailwind CSS**: Utility-first styling

## Component Reusability

These components are designed to be highly reusable:
- Use them in different pages (HomePage, CategoryPage, etc.)
- Customize appearance with props
- Extend functionality with custom handlers
- Maintain consistent styling across the app

## Testing

To test the components, you can use the demo component:
```tsx
import { ComponentDemo } from "../components/demo/ComponentDemo";
```

This provides examples of all component variants and configurations.
