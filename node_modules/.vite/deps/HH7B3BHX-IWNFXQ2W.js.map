{"version": 3, "sources": ["../../@tanstack/query-devtools/build/DevtoolsComponent/HH7B3BHX.js"], "sourcesContent": ["import { createLocalStorage, THEME_PREFERENCE, QueryDevtoolsContext, PiPProvider, ThemeContext, Devtools } from '../chunk/L7Z3HDK6.js';\nimport { getPreferredColorScheme, createMemo, createComponent } from '../chunk/V5T5VJKG.js';\n\n// src/DevtoolsComponent.tsx\nvar DevtoolsComponent = (props) => {\n  const [localStore, setLocalStore] = createLocalStorage({\n    prefix: \"TanstackQueryDevtools\"\n  });\n  const colorScheme = getPreferredColorScheme();\n  const theme = createMemo(() => {\n    const preference = localStore.theme_preference || THEME_PREFERENCE;\n    if (preference !== \"system\") return preference;\n    return colorScheme();\n  });\n  return createComponent(QueryDevtoolsContext.Provider, {\n    value: props,\n    get children() {\n      return createComponent(PiPProvider, {\n        localStore,\n        setLocalStore,\n        get children() {\n          return createComponent(ThemeContext.Provider, {\n            value: theme,\n            get children() {\n              return createComponent(Devtools, {\n                localStore,\n                setLocalStore\n              });\n            }\n          });\n        }\n      });\n    }\n  });\n};\nvar DevtoolsComponent_default = DevtoolsComponent;\n\nexport { DevtoolsComponent_default as default };\n"], "mappings": ";;;;;;;;;;;;;;;;AAIA,IAAI,oBAAoB,CAAC,UAAU;AACjC,QAAM,CAAC,YAAY,aAAa,IAAI,mBAAmB;AAAA,IACrD,QAAQ;AAAA,EACV,CAAC;AACD,QAAM,cAAc,wBAAwB;AAC5C,QAAM,QAAQ,WAAW,MAAM;AAC7B,UAAM,aAAa,WAAW,oBAAoB;AAClD,QAAI,eAAe,SAAU,QAAO;AACpC,WAAO,YAAY;AAAA,EACrB,CAAC;AACD,SAAO,gBAAgB,qBAAqB,UAAU;AAAA,IACpD,OAAO;AAAA,IACP,IAAI,WAAW;AACb,aAAO,gBAAgB,aAAa;AAAA,QAClC;AAAA,QACA;AAAA,QACA,IAAI,WAAW;AACb,iBAAO,gBAAgB,aAAa,UAAU;AAAA,YAC5C,OAAO;AAAA,YACP,IAAI,WAAW;AACb,qBAAO,gBAAgB,UAAU;AAAA,gBAC/B;AAAA,gBACA;AAAA,cACF,CAAC;AAAA,YACH;AAAA,UACF,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF,CAAC;AACH;AACA,IAAI,4BAA4B;", "names": []}