import React from 'react';
import { BrandShowcase } from '../components/common/BrandShowcase';
import { ThemeToggle } from '../components/common/ThemeToggle';
import { Button } from '../components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card';

export const ThemePage: React.FC = () => {
  return (
    <div className="container py-8">
      {/* Header */}
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-4xl font-bold mb-2">BlogTruyen Theme System</h1>
          <p className="text-muted-foreground">
            Hệ thống màu sắc và theme cho BlogTruyen
          </p>
        </div>
        <ThemeToggle />
      </div>

      {/* Hero Section with Gradient */}
      <section className="mb-12">
        <Card className="relative overflow-hidden">
          <div 
            className="absolute inset-0 opacity-20"
            style={{ background: 'linear-gradient(135deg, #E40066 0%, #03CEA4 50%, #9747FF 100%)' }}
          ></div>
          <CardContent className="relative z-10 p-8 text-center">
            <h2 className="text-3xl font-bold mb-4 bg-gradient-to-r from-brand-primary via-brand-secondary to-brand-accent bg-clip-text text-transparent">
              Brand Colors & Design System
            </h2>
            <p className="text-lg text-muted-foreground mb-6">
              Khám phá hệ thống màu sắc và thiết kế của BlogTruyen
            </p>
            <div className="flex justify-center gap-4">
              <Button className="bg-brand-primary hover:bg-brand-primary/90">
                Primary Action
              </Button>
              <Button variant="outline" className="border-brand-secondary text-brand-secondary hover:bg-brand-secondary hover:text-white">
                Secondary Action
              </Button>
            </div>
          </CardContent>
        </Card>
      </section>

      {/* Brand Showcase */}
      <BrandShowcase />

      {/* Interactive Examples */}
      <section className="mt-12">
        <Card>
          <CardHeader>
            <CardTitle>Interactive Examples</CardTitle>
            <CardDescription>
              Các ví dụ tương tác với theme colors
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {/* Buttons */}
              <div>
                <h4 className="font-semibold mb-3">Buttons</h4>
                <div className="flex flex-wrap gap-2">
                  <Button>Default</Button>
                  <Button variant="secondary">Secondary</Button>
                  <Button variant="outline">Outline</Button>
                  <Button variant="ghost">Ghost</Button>
                  <Button variant="destructive">Destructive</Button>
                  <Button className="bg-brand-primary hover:bg-brand-primary/90">Brand Primary</Button>
                  <Button className="bg-brand-secondary hover:bg-brand-secondary/90">Brand Secondary</Button>
                  <Button className="bg-brand-accent hover:bg-brand-accent/90">Brand Accent</Button>
                </div>
              </div>

              {/* Status Cards */}
              <div>
                <h4 className="font-semibold mb-3">Status Cards</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  <div className="p-4 border border-success bg-success/10 rounded-lg">
                    <div className="flex items-center gap-2 mb-2">
                      <div className="w-3 h-3 bg-success rounded-full"></div>
                      <span className="font-semibold text-success">Success</span>
                    </div>
                    <p className="text-sm">Operation completed successfully</p>
                  </div>
                  
                  <div className="p-4 border border-warning bg-warning/10 rounded-lg">
                    <div className="flex items-center gap-2 mb-2">
                      <div className="w-3 h-3 bg-warning rounded-full"></div>
                      <span className="font-semibold text-warning">Warning</span>
                    </div>
                    <p className="text-sm">Please review this action</p>
                  </div>
                  
                  <div className="p-4 border border-error bg-error/10 rounded-lg">
                    <div className="flex items-center gap-2 mb-2">
                      <div className="w-3 h-3 bg-error rounded-full"></div>
                      <span className="font-semibold text-error">Error</span>
                    </div>
                    <p className="text-sm">Something went wrong</p>
                  </div>
                  
                  <div className="p-4 border border-info bg-info/10 rounded-lg">
                    <div className="flex items-center gap-2 mb-2">
                      <div className="w-3 h-3 bg-info rounded-full"></div>
                      <span className="font-semibold text-info">Info</span>
                    </div>
                    <p className="text-sm">Additional information</p>
                  </div>
                </div>
              </div>

              {/* Gradient Examples */}
              <div>
                <h4 className="font-semibold mb-3">Gradient Backgrounds</h4>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="h-24 bg-gradient-primary rounded-lg flex items-center justify-center text-white font-semibold">
                    Primary Gradient
                  </div>
                  <div className="h-24 bg-gradient-secondary rounded-lg flex items-center justify-center text-white font-semibold">
                    Secondary Gradient
                  </div>
                  <div className="h-24 bg-gradient-hero rounded-lg flex items-center justify-center text-white font-semibold">
                    Hero Gradient
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </section>
    </div>
  );
};
