import React from "react";
import { Logo } from "../components/common/Logo";
import { LogoIcon } from "../components/common/LogoIcon";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "../components/ui/card";

export const LogoPage: React.FC = () => {
  return (
    <div className="container py-8">
      <div className="mb-8">
        <h1 className="text-4xl font-bold mb-2">BlogTruyen Logo System</h1>
        <p className="text-muted-foreground">
          Logo và branding elements cho BlogTruyen
        </p>
      </div>

      {/* Logo Variants */}
      <section className="mb-12">
        <Card>
          <CardHeader>
            <CardTitle>Logo Variants</CardTitle>
            <CardDescription>
              Các biến thể logo cho different contexts
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {/* Default Variant */}
              <div className="space-y-4">
                <h3 className="font-semibold">Default</h3>
                <div className="p-6 border rounded-lg bg-background">
                  <Logo variant="default" size="lg" />
                </div>
                <p className="text-sm text-muted-foreground">
                  Sử dụng cho general purposes, follows current text color
                </p>
              </div>

              {/* Brand Variant */}
              <div className="space-y-4">
                <h3 className="font-semibold">Brand</h3>
                <div className="p-6 border rounded-lg bg-background">
                  <Logo variant="brand" size="lg" />
                </div>
                <p className="text-sm text-muted-foreground">
                  Sử dụng brand colors (#E40066), ideal cho marketing materials
                </p>
              </div>

              {/* White Variant */}
              <div className="space-y-4">
                <h3 className="font-semibold">White</h3>
                <div className="p-6 border rounded-lg bg-gray-900">
                  <Logo variant="white" size="lg" />
                </div>
                <p className="text-sm text-muted-foreground">
                  Cho dark backgrounds, all white colors
                </p>
              </div>

              {/* Dark Variant */}
              <div className="space-y-4">
                <h3 className="font-semibold">Dark</h3>
                <div className="p-6 border rounded-lg bg-gray-100">
                  <Logo variant="dark" size="lg" />
                </div>
                <p className="text-sm text-muted-foreground">
                  Cho light backgrounds, dark colors
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </section>

      {/* Logo Sizes */}
      <section className="mb-12">
        <Card>
          <CardHeader>
            <CardTitle>Logo Sizes</CardTitle>
            <CardDescription>Các kích thước logo khác nhau</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              <div className="flex items-center gap-4">
                <span className="w-16 text-sm font-medium">Small:</span>
                <Logo variant="brand" size="sm" />
              </div>
              <div className="flex items-center gap-4">
                <span className="w-16 text-sm font-medium">Medium:</span>
                <Logo variant="brand" size="md" />
              </div>
              <div className="flex items-center gap-4">
                <span className="w-16 text-sm font-medium">Large:</span>
                <Logo variant="brand" size="lg" />
              </div>
              <div className="flex items-center gap-4">
                <span className="w-16 text-sm font-medium">XLarge:</span>
                <Logo variant="brand" size="xl" />
              </div>
            </div>
          </CardContent>
        </Card>
      </section>

      {/* Logo Icon Only */}
      <section className="mb-12">
        <Card>
          <CardHeader>
            <CardTitle>Logo Icon</CardTitle>
            <CardDescription>
              Icon-only version cho compact spaces
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
              <div className="text-center space-y-2">
                <div className="p-4 border rounded-lg bg-background flex justify-center">
                  <LogoIcon variant="default" size={32} />
                </div>
                <p className="text-xs text-muted-foreground">Default</p>
              </div>

              <div className="text-center space-y-2">
                <div className="p-4 border rounded-lg bg-background flex justify-center">
                  <LogoIcon variant="brand" size={32} />
                </div>
                <p className="text-xs text-muted-foreground">Brand</p>
              </div>

              <div className="text-center space-y-2">
                <div className="p-4 border rounded-lg bg-gray-900 flex justify-center">
                  <LogoIcon variant="white" size={32} />
                </div>
                <p className="text-xs text-muted-foreground">White</p>
              </div>

              <div className="text-center space-y-2">
                <div className="p-4 border rounded-lg bg-gray-100 flex justify-center">
                  <LogoIcon variant="dark" size={32} />
                </div>
                <p className="text-xs text-muted-foreground">Dark</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </section>

      {/* Usage Guidelines */}
      <section className="mb-12">
        <Card>
          <CardHeader>
            <CardTitle>Usage Guidelines</CardTitle>
            <CardDescription>Hướng dẫn sử dụng logo đúng cách</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              <div>
                <h4 className="font-semibold mb-2">✅ Do's</h4>
                <ul className="space-y-1 text-sm text-muted-foreground">
                  <li>• Sử dụng brand variant cho marketing materials</li>
                  <li>• Maintain adequate clear space around logo</li>
                  <li>• Sử dụng appropriate size cho context</li>
                  <li>• Sử dụng white variant trên dark backgrounds</li>
                  <li>• Keep logo proportions intact</li>
                </ul>
              </div>

              <div>
                <h4 className="font-semibold mb-2">❌ Don'ts</h4>
                <ul className="space-y-1 text-sm text-muted-foreground">
                  <li>• Không stretch hoặc distort logo</li>
                  <li>• Không thay đổi colors ngoài provided variants</li>
                  <li>• Không place logo trên busy backgrounds</li>
                  <li>• Không sử dụng logo quá nhỏ (minimum 24px height)</li>
                  <li>• Không rotate hoặc skew logo</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </section>

      {/* Code Examples */}
      <section>
        <Card>
          <CardHeader>
            <CardTitle>Code Examples</CardTitle>
            <CardDescription>
              Cách sử dụng logo components trong code
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <h4 className="font-semibold mb-2">Basic Usage</h4>
                <pre className="p-4 bg-muted rounded-lg text-sm overflow-x-auto">
                  {`// Default logo
<Logo />

// Brand variant with large size
<Logo variant="brand" size="lg" />

// Icon only
<LogoIcon variant="brand" size={32} />

// Without text
<Logo showText={false} />`}
                </pre>
              </div>

              <div>
                <h4 className="font-semibold mb-2">Props</h4>
                <div className="overflow-x-auto">
                  <table className="w-full text-sm">
                    <thead>
                      <tr className="border-b">
                        <th className="text-left p-2">Prop</th>
                        <th className="text-left p-2">Type</th>
                        <th className="text-left p-2">Default</th>
                        <th className="text-left p-2">Description</th>
                      </tr>
                    </thead>
                    <tbody className="text-muted-foreground">
                      <tr className="border-b">
                        <td className="p-2">variant</td>
                        <td className="p-2">
                          'default' | 'brand' | 'white' | 'dark'
                        </td>
                        <td className="p-2">'default'</td>
                        <td className="p-2">Color variant</td>
                      </tr>
                      <tr className="border-b">
                        <td className="p-2">size</td>
                        <td className="p-2">'sm' | 'md' | 'lg' | 'xl'</td>
                        <td className="p-2">'md'</td>
                        <td className="p-2">Logo size</td>
                      </tr>
                      <tr className="border-b">
                        <td className="p-2">showText</td>
                        <td className="p-2">boolean</td>
                        <td className="p-2">true</td>
                        <td className="p-2">Show text label</td>
                      </tr>
                      <tr>
                        <td className="p-2">className</td>
                        <td className="p-2">string</td>
                        <td className="p-2">-</td>
                        <td className="p-2">Additional CSS classes</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </section>
    </div>
  );
};
