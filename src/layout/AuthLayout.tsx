import React from "react";
import { Navigate, Outlet, useLocation } from "react-router-dom";
import { useAuthStore } from "../../stores/authStore";

interface AuthLayoutProps {
  requireAuth?: boolean;
  redirectTo?: string;
}

export const AuthLayout: React.FC<AuthLayoutProps> = ({
  requireAuth = false,
  redirectTo = "/",
}) => {
  const { isAuthenticated } = useAuthStore();
  const location = useLocation();

  // If authentication is required but user is not authenticated
  if (requireAuth && !isAuthenticated) {
    return <Navigate to="/auth/login" replace />;
  }

  // If user is authenticated but trying to access auth pages (login/register)
  if (
    !requireAuth &&
    isAuthenticated &&
    (location.pathname === "/auth/login" ||
      location.pathname === "/auth/register")
  ) {
    return <Navigate to={redirectTo} replace />;
  }

  return <Outlet />;
};
