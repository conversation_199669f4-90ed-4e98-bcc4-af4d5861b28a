import React from "react";
import { useTranslation } from "react-i18next";
import { Github, Twitter, Mail } from "lucide-react";
import { Logo } from "../components/common/Logo";

export const Footer: React.FC = () => {
  const { t } = useTranslation("common");

  return (
    <footer className="border-t bg-background">
      <div className="container py-8">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Logo & Description */}
          <div className="space-y-4">
            <Logo size="md" variant="default" />
            <p className="text-sm text-muted-foreground">
              Nền tảng đọc truyện tranh online hiện đại với trải nghiệm đọc
              tuyệt vời.
            </p>
          </div>

          {/* Quick Links */}
          <div className="space-y-4">
            <h3 className="font-semibold"><PERSON><PERSON><PERSON> kế<PERSON> n<PERSON></h3>
            <ul className="space-y-2 text-sm">
              <li>
                <a
                  href="/"
                  className="text-muted-foreground hover:text-primary transition-colors"
                >
                  {t("navigation.home")}
                </a>
              </li>
              <li>
                <a
                  href="/browse"
                  className="text-muted-foreground hover:text-primary transition-colors"
                >
                  {t("navigation.browse")}
                </a>
              </li>
              <li>
                <a
                  href="/about"
                  className="text-muted-foreground hover:text-primary transition-colors"
                >
                  Về chúng tôi
                </a>
              </li>
              <li>
                <a
                  href="/contact"
                  className="text-muted-foreground hover:text-primary transition-colors"
                >
                  Liên hệ
                </a>
              </li>
              <li>
                <a
                  href="/theme"
                  className="text-muted-foreground hover:text-primary transition-colors"
                >
                  Theme
                </a>
              </li>
              <li>
                <a
                  href="/logo"
                  className="text-muted-foreground hover:text-primary transition-colors"
                >
                  Logo
                </a>
              </li>
            </ul>
          </div>

          {/* Support */}
          <div className="space-y-4">
            <h3 className="font-semibold">Hỗ trợ</h3>
            <ul className="space-y-2 text-sm">
              <li>
                <a
                  href="/help"
                  className="text-muted-foreground hover:text-primary transition-colors"
                >
                  Trợ giúp
                </a>
              </li>
              <li>
                <a
                  href="/faq"
                  className="text-muted-foreground hover:text-primary transition-colors"
                >
                  FAQ
                </a>
              </li>
              <li>
                <a
                  href="/privacy"
                  className="text-muted-foreground hover:text-primary transition-colors"
                >
                  Chính sách bảo mật
                </a>
              </li>
              <li>
                <a
                  href="/terms"
                  className="text-muted-foreground hover:text-primary transition-colors"
                >
                  Điều khoản sử dụng
                </a>
              </li>
            </ul>
          </div>

          {/* Social Links */}
          <div className="space-y-4">
            <h3 className="font-semibold">Kết nối</h3>
            <div className="flex space-x-4">
              <a
                href="#"
                className="text-muted-foreground hover:text-primary transition-colors"
              >
                <Github className="h-5 w-5" />
              </a>
              <a
                href="#"
                className="text-muted-foreground hover:text-primary transition-colors"
              >
                <Twitter className="h-5 w-5" />
              </a>
              <a
                href="#"
                className="text-muted-foreground hover:text-primary transition-colors"
              >
                <Mail className="h-5 w-5" />
              </a>
            </div>
          </div>
        </div>

        <div className="border-t mt-8 pt-8 text-center text-sm text-muted-foreground">
          <p>&copy; 2024 BlogTruyen. Tất cả quyền được bảo lưu.</p>
        </div>
      </div>
    </footer>
  );
};
