// Theme configuration for BlogTruyen
export const theme = {
  colors: {
    // Brand colors
    primary: '#E40066',      // Pink/Magenta - Primary brand color
    secondary: '#03CEA4',    // Teal/Cyan - Secondary brand color  
    accent: '#9747FF',       // Purple - Accent color
    
    // Semantic colors
    success: '#03CEA4',      // Using secondary color for success
    warning: '#FFB800',      // Orange for warnings
    error: '#E40066',        // Using primary color for errors
    info: '#9747FF',         // Using accent color for info
    
    // Neutral colors
    background: {
      primary: '#FFFFFF',
      secondary: '#F8FAFC',
      tertiary: '#F1F5F9',
    },
    
    foreground: {
      primary: '#0F172A',
      secondary: '#475569',
      tertiary: '#64748B',
      muted: '#94A3B8',
    },
    
    border: {
      primary: '#E2E8F0',
      secondary: '#CBD5E1',
      accent: '#E40066',
    },
    
    // Dark mode colors
    dark: {
      background: {
        primary: '#0F172A',
        secondary: '#1E293B',
        tertiary: '#334155',
      },
      foreground: {
        primary: '#F8FAFC',
        secondary: '#E2E8F0',
        tertiary: '#CBD5E1',
        muted: '#64748B',
      },
      border: {
        primary: '#334155',
        secondary: '#475569',
        accent: '#E40066',
      },
    },
  },
  
  gradients: {
    primary: 'linear-gradient(135deg, #E40066 0%, #9747FF 100%)',
    secondary: 'linear-gradient(135deg, #03CEA4 0%, #9747FF 100%)',
    accent: 'linear-gradient(135deg, #9747FF 0%, #E40066 100%)',
    hero: 'linear-gradient(135deg, #E40066 0%, #03CEA4 50%, #9747FF 100%)',
  },
  
  shadows: {
    sm: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
    md: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
    lg: '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)',
    xl: '0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)',
    primary: '0 10px 15px -3px rgb(228 0 102 / 0.3), 0 4px 6px -4px rgb(228 0 102 / 0.1)',
    secondary: '0 10px 15px -3px rgb(3 206 164 / 0.3), 0 4px 6px -4px rgb(3 206 164 / 0.1)',
    accent: '0 10px 15px -3px rgb(151 71 255 / 0.3), 0 4px 6px -4px rgb(151 71 255 / 0.1)',
  },
  
  spacing: {
    xs: '0.25rem',    // 4px
    sm: '0.5rem',     // 8px
    md: '1rem',       // 16px
    lg: '1.5rem',     // 24px
    xl: '2rem',       // 32px
    '2xl': '3rem',    // 48px
    '3xl': '4rem',    // 64px
    '4xl': '6rem',    // 96px
  },
  
  borderRadius: {
    sm: '0.25rem',    // 4px
    md: '0.375rem',   // 6px
    lg: '0.5rem',     // 8px
    xl: '0.75rem',    // 12px
    '2xl': '1rem',    // 16px
    full: '9999px',
  },
  
  typography: {
    fontFamily: {
      sans: ['Inter', 'system-ui', 'sans-serif'],
      mono: ['JetBrains Mono', 'Consolas', 'monospace'],
    },
    fontSize: {
      xs: '0.75rem',    // 12px
      sm: '0.875rem',   // 14px
      base: '1rem',     // 16px
      lg: '1.125rem',   // 18px
      xl: '1.25rem',    // 20px
      '2xl': '1.5rem',  // 24px
      '3xl': '1.875rem', // 30px
      '4xl': '2.25rem', // 36px
      '5xl': '3rem',    // 48px
    },
    fontWeight: {
      normal: '400',
      medium: '500',
      semibold: '600',
      bold: '700',
    },
  },
  
  breakpoints: {
    sm: '640px',
    md: '768px',
    lg: '1024px',
    xl: '1280px',
    '2xl': '1536px',
  },
  
  zIndex: {
    dropdown: 1000,
    sticky: 1020,
    fixed: 1030,
    modal: 1040,
    popover: 1050,
    tooltip: 1060,
    toast: 1070,
  },
} as const;

export type Theme = typeof theme;
