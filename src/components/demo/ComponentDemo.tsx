import React, { useState } from "react";
import { CarouselHeroBanner } from "../common/CarouselHeroBanner";
import { Categories, HotCategories, PopularCategories, GenreCategories } from "../common/Categories";
import type { Category } from "../common/Categories";

export const ComponentDemo: React.FC = () => {
  const [selectedCategory, setSelectedCategory] = useState<string>("all-category");

  // Demo hero slides
  const demoSlides = [
    {
      id: "one-piece",
      title: "One Piece",
      description: "Kid so focused on building a bird out of scrap metal, he doesn't realize his head got turned into a bird's nest.",
      backgroundImage: "/api/placeholder/1200/320",
      badge: {
        text: "Out now",
        icon: "🎉",
        variant: "default" as const,
      },
      categoryBadge: {
        text: "Manga",
        icon: "📖",
        variant: "manga" as const,
      },
      chapterInfo: {
        text: "Chapter 1012",
        onClick: () => console.log("Navigate to chapter"),
      },
      actionButton: {
        text: "READ",
        variant: "primary" as const,
        onClick: () => console.log("Start reading"),
      },
    },
    {
      id: "solo-leveling",
      title: "Solo Leveling",
      description: "Sung Jin-Woo's journey from the weakest hunter to the strongest shadow monarch continues.",
      backgroundImage: "/api/placeholder/1200/320",
      badge: {
        text: "Popular",
        icon: "🔥",
        variant: "warning" as const,
      },
      categoryBadge: {
        text: "Manhwa",
        icon: "📚",
        variant: "manhwa" as const,
      },
      chapterInfo: {
        text: "Chapter 154",
        onClick: () => console.log("Navigate to chapter"),
      },
      actionButton: {
        text: "READ",
        variant: "primary" as const,
        onClick: () => console.log("Start reading"),
      },
    },
    {
      id: "berserk",
      title: "Berserk",
      description: "The dark fantasy epic that redefined manga storytelling continues its legendary journey.",
      backgroundImage: "/api/placeholder/1200/320",
      badge: {
        text: "Classic",
        icon: "⭐",
        variant: "success" as const,
      },
      categoryBadge: {
        text: "Manga",
        icon: "📖",
        variant: "manga" as const,
      },
      chapterInfo: {
        text: "Chapter 368",
        onClick: () => console.log("Navigate to chapter"),
      },
      actionButton: {
        text: "READ",
        variant: "secondary" as const,
        onClick: () => console.log("Start reading"),
      },
    },
  ];

  // Demo categories
  const demoCategories: Category[] = [
    { id: "all-category", name: "All category" },
    { id: "shonen", name: "Shonen", count: 1250 },
    { id: "shojo", name: "Shojo", count: 890 },
    { id: "seinen", name: "Seinen", count: 567 },
    { id: "josei", name: "Josei", count: 234 },
    { id: "kodomomuke", name: "Kodomomuke", count: 123 },
    { id: "one-shot", name: "One Shot", count: 456 },
    { id: "action", name: "Action", count: 2100 },
    { id: "adventure", name: "Adventure", count: 1800 },
    { id: "fantasy", name: "Fantasy", count: 1650 },
    { id: "dark-fantasy", name: "Dark Fantasy", count: 340 },
    { id: "ecchi", name: "Ecchi", count: 780 },
    { id: "romance", name: "Romance", count: 1420 },
    { id: "horror", name: "Horror", count: 560 },
    { id: "parody", name: "Parody", count: 290 },
    { id: "mystery", name: "Mystery", count: 670 },
  ];

  const handleCategorySelect = (categoryId: string) => {
    setSelectedCategory(categoryId);
    console.log("Selected category:", categoryId);
  };

  const handleSlideChange = (slideIndex: number) => {
    console.log("Current slide:", slideIndex);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Carousel Hero Banner Demo */}
      <section className="mb-8">
        <h2 className="text-2xl font-bold p-4">Carousel Hero Banner</h2>
        <CarouselHeroBanner
          slides={demoSlides}
          height="lg"
          autoPlay={true}
          autoPlayInterval={4000}
          showNavigation={true}
          showPagination={true}
          onSlideChange={handleSlideChange}
        />
      </section>

      <div className="p-4 lg:p-6 space-y-8">
        {/* Hot Categories Demo */}
        <section>
          <h2 className="text-2xl font-bold mb-4">Hot Categories Component</h2>
          <HotCategories
            categories={demoCategories}
            selectedCategory={selectedCategory}
            onCategorySelect={handleCategorySelect}
          />
        </section>

        {/* Popular Categories Demo */}
        <section>
          <h2 className="text-2xl font-bold mb-4">Popular Categories Component</h2>
          <PopularCategories
            categories={demoCategories.slice(0, 8)}
            selectedCategory={selectedCategory}
            onCategorySelect={handleCategorySelect}
          />
        </section>

        {/* Genre Categories Demo */}
        <section>
          <h2 className="text-2xl font-bold mb-4">Genre Categories Component</h2>
          <GenreCategories
            categories={demoCategories}
            selectedCategory={selectedCategory}
            onCategorySelect={handleCategorySelect}
          />
        </section>

        {/* Custom Categories Demo */}
        <section>
          <h2 className="text-2xl font-bold mb-4">Custom Categories Component</h2>
          <Categories
            title="Custom"
            titleHighlight="Categories"
            categories={demoCategories.slice(0, 6)}
            selectedCategory={selectedCategory}
            onCategorySelect={handleCategorySelect}
            variant="rounded"
            size="lg"
            layout="grid"
            showCount={true}
            className="bg-white p-4 rounded-lg shadow-sm"
          />
        </section>
      </div>
    </div>
  );
};
