import React, { useState, useEffect } from "react";
import { Button } from "../ui/button";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { cn } from "../../utils/cn";
import { SpacedButton } from "./SpacedButton";

interface HeroSlide {
  id: string;
  title: string;
  description: string;
  backgroundImage?: string;
  gradientFrom?: string;
  gradientTo?: string;
  badge?: {
    text: string;
    icon?: string;
    variant?: "default" | "success" | "warning" | "error";
  };
  categoryBadge?: {
    text: string;
    icon?: string;
    variant?: "default" | "manga" | "manhwa" | "manhua";
  };
  chapterInfo?: {
    text: string;
    onClick?: () => void;
  };
  actionButton?: {
    text: string;
    onClick?: () => void;
    variant?: "default" | "primary" | "secondary";
  };
}

interface CarouselHeroBannerProps {
  slides: HeroSlide[];
  height?: "sm" | "md" | "lg" | "xl";
  autoPlay?: boolean;
  autoPlayInterval?: number;
  showNavigation?: boolean;
  showPagination?: boolean;
  className?: string;
  onSlideChange?: (slideIndex: number) => void;
  fixedBadge?: {
    text: string;
    icon?: string;
    variant?: "default" | "success" | "warning" | "error";
  };
}

const heightClasses = {
  sm: "h-64",
  md: "h-80",
  lg: "h-96",
  xl: "h-[32rem]",
};

const badgeVariants = {
  default: "bg-white text-gray-800 shadow-md",
  success: "bg-[#03CEA4] text-white",
  warning: "bg-[#F4B333] text-white",
  error: "bg-[#E40066] text-white",
};

const categoryBadgeVariants = {
  default: "bg-gray-600 text-white",
  manga: "bg-[#E40066] text-white",
  manhwa: "bg-[#03CEA4] text-white",
  manhua: "bg-[#9747FF] text-white",
};

const actionButtonVariants = {
  default: "bg-white text-gray-800 hover:bg-gray-100",
  primary: "bg-[#03CEA4] hover:bg-[#03CEA4]/90 text-white",
  secondary: "bg-gray-600 hover:bg-gray-700 text-white",
};

export const CarouselHeroBanner: React.FC<CarouselHeroBannerProps> = ({
  slides,
  height = "md",
  autoPlay = true,
  autoPlayInterval = 5000,
  showNavigation = true,
  className,
  onSlideChange,
  fixedBadge,
}) => {
  const [currentSlide, setCurrentSlide] = useState(0);

  // Auto play functionality
  useEffect(() => {
    if (!autoPlay || slides.length <= 1) return;

    const interval = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % slides.length);
    }, autoPlayInterval);

    return () => clearInterval(interval);
  }, [autoPlay, autoPlayInterval, slides.length]);

  // Notify parent of slide changes
  useEffect(() => {
    onSlideChange?.(currentSlide);
  }, [currentSlide, onSlideChange]);

  const goToSlide = (index: number) => {
    setCurrentSlide(index);
  };

  const goToPrevious = () => {
    setCurrentSlide((prev) => (prev - 1 + slides.length) % slides.length);
  };

  const goToNext = () => {
    setCurrentSlide((prev) => (prev + 1) % slides.length);
  };

  if (!slides.length) return null;

  const currentSlideData = slides[currentSlide];

  const backgroundStyle = currentSlideData.backgroundImage
    ? {
        backgroundImage: `url(${currentSlideData.backgroundImage})`,
        backgroundSize: "cover",
        backgroundPosition: "center",
      }
    : {};

  return (
    <div className={cn("w-full", className)}>
      {/* Top section with badge and navigation - OUTSIDE hero */}
      <div className="bg-white">
        <div className="flex justify-between items-center p-2">
          {/* Badge (Out now, etc.) - Fixed badge, doesn't change with slides */}
          <div className="flex items-center">
            {fixedBadge && (
              <div
                className={cn(
                  "px-4 py-2 shadow-sm flex items-center gap-2",
                  badgeVariants[fixedBadge.variant || "default"]
                )}
              >
                <span className="text-sm font-medium">{fixedBadge.text}</span>
                {fixedBadge.icon && (
                  <span className="text-lg">{fixedBadge.icon}</span>
                )}
              </div>
            )}
          </div>

          {/* Navigation arrows */}
          {showNavigation && slides.length > 1 && (
            <div className="flex gap-2">
              <Button
                size="sm"
                variant="secondary"
                className="rounded-full bg-gray-100 hover:bg-gray-200 text-gray-700"
                onClick={goToPrevious}
              >
                <ChevronLeft className="w-4 h-4" />
              </Button>
              <Button
                size="sm"
                variant="secondary"
                className="rounded-full bg-gray-100 hover:bg-gray-200 text-gray-700"
                onClick={goToNext}
              >
                <ChevronRight className="w-4 h-4" />
              </Button>
            </div>
          )}
        </div>
      </div>

      {/* Hero Banner with container padding */}
      <div className="p-4 lg:p-6">
        <div
          className={cn(
            "relative text-white transition-all duration-500 rounded-lg overflow-hidden",
            heightClasses[height],
            currentSlideData.backgroundImage
              ? ""
              : `bg-gradient-to-r ${
                  currentSlideData.gradientFrom || "from-blue-600"
                } ${currentSlideData.gradientTo || "to-purple-600"}`
          )}
          style={backgroundStyle}
        >
          {/* Dark overlay for better text readability */}
          <div className="absolute inset-0 bg-black/40"></div>

          {/* Category badge - top right inside hero */}
          {/* {currentSlideData.categoryBadge && (
            <div
              className={cn(
                "absolute top-4 right-4 px-3 py-1 rounded text-sm font-medium",
                categoryBadgeVariants[
                  currentSlideData.categoryBadge.variant || "default"
                ]
              )}
            >
              {currentSlideData.categoryBadge.icon && (
                <span className="mr-1">
                  {currentSlideData.categoryBadge.icon}
                </span>
              )}
              {currentSlideData.categoryBadge.text}
            </div>
          )} */}
          <SpacedButton className="absolute top-4 right-4" variant="secondary">
            Managa
          </SpacedButton>

          {/* Main content - center */}
          <div className="relative z-10 h-full flex items-center justify-center">
            <div className="text-center max-w-2xl px-6">
              <h1 className="text-3xl md:text-5xl font-bold mb-4 tracking-wider">
                {currentSlideData.title}
              </h1>
              <p className="text-sm md:text-lg mb-6 opacity-90">
                {currentSlideData.description}
              </p>
              {currentSlideData.actionButton && (
                <Button
                  className={cn(
                    "px-6 md:px-8 py-2 md:py-3 text-base md:text-lg rounded-lg font-semibold tracking-wider",
                    actionButtonVariants[
                      currentSlideData.actionButton.variant || "primary"
                    ]
                  )}
                  onClick={currentSlideData.actionButton.onClick}
                >
                  {currentSlideData.actionButton.text}
                </Button>
              )}
            </div>
          </div>

          {/* Chapter indicator - bottom left inside hero */}
          {currentSlideData.chapterInfo && (
            <Button
              variant="outline"
              className="absolute bottom-4 left-4 bg-white text-black border-black hover:bg-gray-100 tracking-[0.3em] uppercase font-mono text-sm"
              onClick={currentSlideData.chapterInfo.onClick}
            >
              {currentSlideData.chapterInfo.text}
            </Button>
          )}
        </div>
      </div>
    </div>
  );
};
