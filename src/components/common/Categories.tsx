import React from "react";
import { cn } from "../../utils/cn";
import { NavigationButton } from "./SpacedButton";

export interface Category {
  id: string;
  name: string;
  count?: number;
  color?: string;
}

interface CategoriesProps {
  title?: string;
  titleHighlight?: string;
  categories: Category[];
  selectedCategory?: string;
  onCategorySelect?: (categoryId: string) => void;
  variant?: "default" | "rounded" | "pills";
  size?: "sm" | "md" | "lg";
  layout?: "grid" | "flex";
  maxRows?: number;
  className?: string;
  titleClassName?: string;
  containerClassName?: string;
  buttonClassName?: string;
  selectedButtonClassName?: string;
  showCount?: boolean;
}

const sizeClasses = {
  sm: "text-xs px-3 py-1.5",
  md: "text-sm px-4 py-2",
  lg: "text-base px-6 py-3",
};

const variantClasses = {
  default: "rounded-md",
  rounded: "rounded-lg",
  pills: "rounded-full",
};

export const Categories: React.FC<CategoriesProps> = ({
  title = "Categories",
  titleHighlight,
  categories,
  selectedCategory,
  onCategorySelect,
  variant = "pills",
  size = "md",
  layout = "flex",
  className = "",
  titleClassName = "",
  containerClassName = "",
  buttonClassName = "",
  selectedButtonClassName = "",
  showCount = false,
}) => {
  const handleCategoryClick = (categoryId: string) => {
    onCategorySelect?.(categoryId);
  };

  const renderTitle = () => {
    if (!title && !titleHighlight) return null;

    return (
      <h2 className={cn("text-xl lg:text-2xl font-bold mb-4", titleClassName)}>
        {title && <span>{title}</span>}
        {titleHighlight && (
          <span className="text-red-500 ml-1">{titleHighlight}</span>
        )}
      </h2>
    );
  };

  // Calculate items distribution for 2-row layout
  const getRowDistribution = () => {
    const totalItems = categories.length;
    const topRowCount = Math.ceil(totalItems / 2);
    const bottomRowCount = totalItems - topRowCount;

    return {
      topRow: categories.slice(0, topRowCount),
      bottomRow: categories.slice(topRowCount),
      topRowCount,
      bottomRowCount,
    };
  };

  const getLayoutClasses = () => {
    if (layout === "grid") {
      return `grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 xl:grid-cols-8 gap-2`;
    }
    return "flex flex-wrap gap-2";
  };

  const getButtonClasses = (isSelected: boolean) => {
    const baseClasses = cn(
      "transition-all duration-200 font-medium",
      sizeClasses[size],
      variantClasses[variant],
      buttonClassName
    );

    if (isSelected) {
      return cn(
        baseClasses,
        "bg-gray-800 text-white hover:bg-gray-700 border-gray-800",
        selectedButtonClassName
      );
    }

    return cn(
      baseClasses,
      "bg-white text-black border-gray-300 hover:bg-gray-50 hover:border-gray-400"
    );
  };

  const { topRow, bottomRow } = getRowDistribution();

  // Render categories in 2-row layout with yellow background
  const renderTwoRowLayout = () => {
    return (
      <div className="bg-[#F4B333] p-4 rounded-lg">
        {/* Top row */}
        <div className="flex flex-wrap justify-center gap-2 mb-3">
          {topRow.map((category) => {
            const isSelected = selectedCategory === category.id;
            return (
              <NavigationButton
                key={category.id}
                variant="outline"
                size="sm"
                onClick={() => handleCategoryClick(category.id)}
                className={getButtonClasses(isSelected)}
              >
                {showCount && category.count
                  ? `${category.name} (${category.count})`
                  : category.name}
              </NavigationButton>
            );
          })}
        </div>

        {/* Bottom row - centered */}
        <div className="flex flex-wrap justify-center gap-2">
          {bottomRow.map((category) => {
            const isSelected = selectedCategory === category.id;
            return (
              <NavigationButton
                key={category.id}
                variant="outline"
                size="sm"
                onClick={() => handleCategoryClick(category.id)}
                className={getButtonClasses(isSelected)}
              >
                {showCount && category.count
                  ? `${category.name} (${category.count})`
                  : category.name}
              </NavigationButton>
            );
          })}
        </div>
      </div>
    );
  };

  // Render original layout for backward compatibility
  const renderOriginalLayout = () => {
    return (
      <div className={cn(getLayoutClasses(), containerClassName)}>
        {categories.map((category) => {
          const isSelected = selectedCategory === category.id;

          return (
            <NavigationButton
              key={category.id}
              variant="outline"
              size="sm"
              onClick={() => handleCategoryClick(category.id)}
              className={getButtonClasses(isSelected)}
            >
              {showCount && category.count
                ? `${category.name} (${category.count})`
                : category.name}
            </NavigationButton>
          );
        })}
      </div>
    );
  };

  return (
    <div className={cn("w-full", className)}>
      {renderTitle()}

      {/* Use 2-row layout by default, fallback to original for grid layout */}
      {layout === "grid" ? renderOriginalLayout() : renderTwoRowLayout()}
    </div>
  );
};

// Preset variants for common use cases
export const HotCategories: React.FC<
  Omit<CategoriesProps, "title" | "titleHighlight">
> = (props) => (
  <Categories
    title="Hot"
    titleHighlight="Categories"
    variant="rounded"
    size="md"
    {...props}
  />
);

export const PopularCategories: React.FC<
  Omit<CategoriesProps, "title" | "titleHighlight">
> = (props) => (
  <Categories
    title="Popular"
    titleHighlight="Categories"
    variant="rounded"
    size="sm"
    {...props}
  />
);

export const GenreCategories: React.FC<Omit<CategoriesProps, "title">> = (
  props
) => (
  <Categories
    title="Genres"
    variant="default"
    layout="grid"
    size="sm"
    showCount={true}
    {...props}
  />
);
