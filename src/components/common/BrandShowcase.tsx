import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';

export const BrandShowcase: React.FC = () => {
  return (
    <div className="space-y-8">
      {/* Brand Colors */}
      <Card>
        <CardHeader>
          <CardTitle>Brand Colors</CardTitle>
          <CardDescription>
            <PERSON><PERSON><PERSON> sắc ch<PERSON>h của BlogTruyen
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Primary Color */}
            <div className="space-y-2">
              <div className="h-20 bg-brand-primary rounded-lg shadow-primary"></div>
              <div className="text-center">
                <p className="font-semibold">Primary</p>
                <p className="text-sm text-muted-foreground">#E40066</p>
                <p className="text-xs text-muted-foreground">Pink/Magenta</p>
              </div>
            </div>

            {/* Secondary Color */}
            <div className="space-y-2">
              <div className="h-20 bg-brand-secondary rounded-lg shadow-secondary"></div>
              <div className="text-center">
                <p className="font-semibold">Secondary</p>
                <p className="text-sm text-muted-foreground">#03CEA4</p>
                <p className="text-xs text-muted-foreground">Teal/Cyan</p>
              </div>
            </div>

            {/* Accent Color */}
            <div className="space-y-2">
              <div className="h-20 bg-brand-accent rounded-lg shadow-accent"></div>
              <div className="text-center">
                <p className="font-semibold">Accent</p>
                <p className="text-sm text-muted-foreground">#9747FF</p>
                <p className="text-xs text-muted-foreground">Purple</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Gradients */}
      <Card>
        <CardHeader>
          <CardTitle>Brand Gradients</CardTitle>
          <CardDescription>
            Gradient combinations cho UI elements
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Primary Gradient */}
            <div className="space-y-2">
              <div 
                className="h-20 rounded-lg"
                style={{ background: 'linear-gradient(135deg, #E40066 0%, #9747FF 100%)' }}
              ></div>
              <div className="text-center">
                <p className="font-semibold">Primary Gradient</p>
                <p className="text-xs text-muted-foreground">Pink → Purple</p>
              </div>
            </div>

            {/* Secondary Gradient */}
            <div className="space-y-2">
              <div 
                className="h-20 rounded-lg"
                style={{ background: 'linear-gradient(135deg, #03CEA4 0%, #9747FF 100%)' }}
              ></div>
              <div className="text-center">
                <p className="font-semibold">Secondary Gradient</p>
                <p className="text-xs text-muted-foreground">Teal → Purple</p>
              </div>
            </div>

            {/* Hero Gradient */}
            <div className="space-y-2 md:col-span-2">
              <div 
                className="h-20 rounded-lg"
                style={{ background: 'linear-gradient(135deg, #E40066 0%, #03CEA4 50%, #9747FF 100%)' }}
              ></div>
              <div className="text-center">
                <p className="font-semibold">Hero Gradient</p>
                <p className="text-xs text-muted-foreground">Pink → Teal → Purple</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Semantic Colors */}
      <Card>
        <CardHeader>
          <CardTitle>Semantic Colors</CardTitle>
          <CardDescription>
            Màu sắc cho các trạng thái và thông báo
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="space-y-2">
              <div className="h-16 bg-success rounded-lg"></div>
              <div className="text-center">
                <p className="font-semibold text-sm">Success</p>
                <p className="text-xs text-muted-foreground">#03CEA4</p>
              </div>
            </div>

            <div className="space-y-2">
              <div className="h-16 bg-warning rounded-lg"></div>
              <div className="text-center">
                <p className="font-semibold text-sm">Warning</p>
                <p className="text-xs text-muted-foreground">#FFB800</p>
              </div>
            </div>

            <div className="space-y-2">
              <div className="h-16 bg-error rounded-lg"></div>
              <div className="text-center">
                <p className="font-semibold text-sm">Error</p>
                <p className="text-xs text-muted-foreground">#E40066</p>
              </div>
            </div>

            <div className="space-y-2">
              <div className="h-16 bg-info rounded-lg"></div>
              <div className="text-center">
                <p className="font-semibold text-sm">Info</p>
                <p className="text-xs text-muted-foreground">#9747FF</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Component Examples */}
      <Card>
        <CardHeader>
          <CardTitle>Component Examples</CardTitle>
          <CardDescription>
            Ví dụ sử dụng theme trong components
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Buttons */}
            <div className="flex flex-wrap gap-2">
              <button className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors">
                Primary Button
              </button>
              <button className="px-4 py-2 bg-secondary text-secondary-foreground rounded-md hover:bg-secondary/90 transition-colors">
                Secondary Button
              </button>
              <button className="px-4 py-2 bg-accent text-accent-foreground rounded-md hover:bg-accent/90 transition-colors">
                Accent Button
              </button>
            </div>

            {/* Cards with brand colors */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="p-4 border-l-4 border-brand-primary bg-card rounded-lg">
                <h4 className="font-semibold text-brand-primary">Primary Card</h4>
                <p className="text-sm text-muted-foreground">Card với accent primary color</p>
              </div>
              <div className="p-4 border-l-4 border-brand-secondary bg-card rounded-lg">
                <h4 className="font-semibold text-brand-secondary">Secondary Card</h4>
                <p className="text-sm text-muted-foreground">Card với accent secondary color</p>
              </div>
              <div className="p-4 border-l-4 border-brand-accent bg-card rounded-lg">
                <h4 className="font-semibold text-brand-accent">Accent Card</h4>
                <p className="text-sm text-muted-foreground">Card với accent color</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
