import React from "react";
import { Button } from "../ui/button";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { cn } from "../../utils/cn";

interface HeroBannerProps {
  title: string;
  description: string;
  backgroundImage?: string;
  gradientFrom?: string;
  gradientTo?: string;
  height?: "sm" | "md" | "lg" | "xl";
  badge?: {
    text: string;
    icon?: string;
    variant?: "default" | "success" | "warning" | "error";
  };
  categoryBadge?: {
    text: string;
    icon?: string;
    variant?: "default" | "manga" | "manhwa" | "manhua";
  };
  chapterInfo?: {
    text: string;
    onClick?: () => void;
  };
  actionButton?: {
    text: string;
    onClick?: () => void;
    variant?: "default" | "primary" | "secondary";
  };
  navigation?: {
    onPrevious?: () => void;
    onNext?: () => void;
    showNavigation?: boolean;
  };
  className?: string;
}

const heightClasses = {
  sm: "h-64",
  md: "h-80", 
  lg: "h-96",
  xl: "h-[32rem]",
};

const badgeVariants = {
  default: "bg-white text-gray-800",
  success: "bg-green-500 text-white",
  warning: "bg-yellow-500 text-white", 
  error: "bg-red-500 text-white",
};

const categoryBadgeVariants = {
  default: "bg-gray-600 text-white",
  manga: "bg-red-600 text-white",
  manhwa: "bg-blue-600 text-white",
  manhua: "bg-purple-600 text-white",
};

const actionButtonVariants = {
  default: "bg-white text-gray-800 hover:bg-gray-100",
  primary: "bg-blue-600 hover:bg-blue-700 text-white",
  secondary: "bg-gray-600 hover:bg-gray-700 text-white",
};

export const HeroBanner: React.FC<HeroBannerProps> = ({
  title,
  description,
  backgroundImage,
  gradientFrom = "from-blue-600",
  gradientTo = "to-purple-600",
  height = "md",
  badge,
  categoryBadge,
  chapterInfo,
  actionButton,
  navigation,
  className,
}) => {
  const backgroundStyle = backgroundImage
    ? {
        backgroundImage: `url(${backgroundImage})`,
        backgroundSize: "cover",
        backgroundPosition: "center",
      }
    : {};

  return (
    <div className={cn("relative", className)}>
      {/* Badge (Out now, etc.) */}
      {badge && (
        <div className="absolute top-4 left-4 z-10">
          <div className={cn(
            "rounded-full px-4 py-2 shadow-sm flex items-center gap-2",
            badgeVariants[badge.variant || "default"]
          )}>
            <span className="text-sm font-medium">{badge.text}</span>
            {badge.icon && <span className="text-lg">{badge.icon}</span>}
          </div>
        </div>
      )}

      {/* Navigation arrows */}
      {navigation?.showNavigation && (
        <div className="absolute top-4 right-4 z-10 flex gap-2">
          <Button
            size="icon"
            variant="secondary"
            className="rounded-full bg-white/80 hover:bg-white"
            onClick={navigation.onPrevious}
          >
            <ChevronLeft className="w-4 h-4" />
          </Button>
          <Button
            size="icon"
            variant="secondary"
            className="rounded-full bg-white/80 hover:bg-white"
            onClick={navigation.onNext}
          >
            <ChevronRight className="w-4 h-4" />
          </Button>
        </div>
      )}

      {/* Hero Banner */}
      <div
        className={cn(
          "relative flex items-center justify-center text-white",
          heightClasses[height],
          backgroundImage ? "" : `bg-gradient-to-r ${gradientFrom} ${gradientTo}`
        )}
        style={backgroundStyle}
      >
        <div className="absolute inset-0 bg-black/30"></div>
        <div className="relative z-10 text-center max-w-2xl px-6">
          <h1 className="text-5xl font-bold mb-4 tracking-wider">
            {title}
          </h1>
          <p className="text-lg mb-6 opacity-90">
            {description}
          </p>
          {actionButton && (
            <Button 
              className={cn(
                "px-8 py-3 text-lg rounded-lg",
                actionButtonVariants[actionButton.variant || "primary"]
              )}
              onClick={actionButton.onClick}
            >
              {actionButton.text}
            </Button>
          )}
        </div>

        {/* Category badge */}
        {categoryBadge && (
          <div className={cn(
            "absolute top-4 right-16 px-3 py-1 rounded text-sm font-medium",
            categoryBadgeVariants[categoryBadge.variant || "default"]
          )}>
            {categoryBadge.icon && <span className="mr-1">{categoryBadge.icon}</span>}
            {categoryBadge.text}
          </div>
        )}

        {/* Chapter indicator */}
        {chapterInfo && (
          <Button
            variant="outline"
            className="absolute bottom-4 left-4 bg-white text-black border-black hover:bg-gray-100 tracking-[0.3em] uppercase font-mono text-sm"
            onClick={chapterInfo.onClick}
          >
            {chapterInfo.text}
          </Button>
        )}
      </div>
    </div>
  );
};

// Preset variants for common use cases
export const MangaHeroBanner: React.FC<Omit<HeroBannerProps, "categoryBadge">> = (props) => (
  <HeroBanner
    categoryBadge={{
      text: "Manga",
      icon: "📖",
      variant: "manga"
    }}
    {...props}
  />
);

export const ManhwaHeroBanner: React.FC<Omit<HeroBannerProps, "categoryBadge">> = (props) => (
  <HeroBanner
    categoryBadge={{
      text: "Manhwa", 
      icon: "📱",
      variant: "manhwa"
    }}
    {...props}
  />
);

export const ManhuaHeroBanner: React.FC<Omit<HeroBannerProps, "categoryBadge">> = (props) => (
  <HeroBanner
    categoryBadge={{
      text: "Manhua",
      icon: "🎨", 
      variant: "manhua"
    }}
    {...props}
  />
);
