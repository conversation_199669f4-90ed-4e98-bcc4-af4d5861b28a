import React from "react";
import { 
  HeroBanner, 
  MangaHeroBanner, 
  ManhwaHeroBanner, 
  ManhuaHeroBanner 
} from "../components/common/HeroBanner";

export const HeroBannerExamples: React.FC = () => {
  return (
    <div className="space-y-8">
      {/* Basic Hero Banner */}
      <div>
        <h2 className="text-2xl font-bold mb-4">Basic Hero Banner</h2>
        <HeroBanner
          title="Welcome to BlogTruyen"
          description="Discover amazing manga, manhwa, and manhua from around the world"
          height="sm"
          gradientFrom="from-purple-600"
          gradientTo="to-pink-600"
          actionButton={{
            text: "Get Started",
            variant: "primary",
            onClick: () => console.log("Get started clicked")
          }}
        />
      </div>

      {/* Manga Hero Banner */}
      <div>
        <h2 className="text-2xl font-bold mb-4">Manga Hero Banner</h2>
        <MangaHeroBanner
          title="Attack on Titan"
          description="Humanity fights for survival against giant humanoid Titans"
          backgroundImage="/api/placeholder/1200/320"
          height="md"
          badge={{
            text: "Completed",
            icon: "✅",
            variant: "success"
          }}
          chapterInfo={{
            text: "Chapter 139",
            onClick: () => console.log("Final chapter")
          }}
          actionButton={{
            text: "READ NOW",
            variant: "primary"
          }}
        />
      </div>

      {/* Manhwa Hero Banner */}
      <div>
        <h2 className="text-2xl font-bold mb-4">Manhwa Hero Banner</h2>
        <ManhwaHeroBanner
          title="Solo Leveling"
          description="The weakest hunter becomes the strongest through a mysterious system"
          backgroundImage="/api/placeholder/1200/320"
          height="lg"
          badge={{
            text: "Hot",
            icon: "🔥",
            variant: "error"
          }}
          chapterInfo={{
            text: "Chapter 179",
            onClick: () => console.log("Latest chapter")
          }}
          actionButton={{
            text: "CONTINUE READING",
            variant: "secondary"
          }}
          navigation={{
            showNavigation: true,
            onPrevious: () => console.log("Previous"),
            onNext: () => console.log("Next")
          }}
        />
      </div>

      {/* Manhua Hero Banner */}
      <div>
        <h2 className="text-2xl font-bold mb-4">Manhua Hero Banner</h2>
        <ManhuaHeroBanner
          title="Tales of Demons and Gods"
          description="Nie Li returns to his youth with knowledge of the future"
          backgroundImage="/api/placeholder/1200/320"
          height="xl"
          badge={{
            text: "Updated",
            icon: "🆕",
            variant: "warning"
          }}
          chapterInfo={{
            text: "Chapter 456",
            onClick: () => console.log("New chapter")
          }}
          actionButton={{
            text: "START READING",
            variant: "default"
          }}
        />
      </div>

      {/* Custom Hero Banner with all features */}
      <div>
        <h2 className="text-2xl font-bold mb-4">Custom Hero Banner</h2>
        <HeroBanner
          title="Featured Collection"
          description="Explore our curated selection of the best stories"
          height="md"
          gradientFrom="from-green-600"
          gradientTo="to-blue-600"
          badge={{
            text: "Editor's Choice",
            icon: "⭐",
            variant: "default"
          }}
          categoryBadge={{
            text: "Mixed",
            icon: "📚",
            variant: "default"
          }}
          actionButton={{
            text: "EXPLORE",
            variant: "primary",
            onClick: () => console.log("Explore collection")
          }}
          navigation={{
            showNavigation: true,
            onPrevious: () => console.log("Previous collection"),
            onNext: () => console.log("Next collection")
          }}
          className="border-4 border-yellow-400"
        />
      </div>

      {/* Minimal Hero Banner */}
      <div>
        <h2 className="text-2xl font-bold mb-4">Minimal Hero Banner</h2>
        <HeroBanner
          title="Simple & Clean"
          description="Sometimes less is more"
          height="sm"
          gradientFrom="from-gray-600"
          gradientTo="to-gray-800"
        />
      </div>
    </div>
  );
};
