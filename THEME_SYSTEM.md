# BlogTruyen Theme System

## 🎨 Tổng quan

Hệ thống theme của BlogTruyen được thiết kế với màu sắc chính:
- **Primary**: `#E40066` (Pink/Magenta) 
- **Secondary**: `#03CEA4` (<PERSON><PERSON>/<PERSON><PERSON>)
- **Accent**: `#9747FF` (Purple)

## 🏗️ Cấu trúc Theme

### 1. Theme Configuration
**File**: `src/styles/theme.ts`

Chứa toàn bộ cấu hình theme bao gồm:
- Brand colors
- Semantic colors  
- Gradients
- Shadows
- Typography
- Spacing & Border radius
- Breakpoints & Z-index

### 2. CSS Variables
**File**: `src/index.css`

CSS variables được định nghĩa cho:
- Light theme
- Dark theme
- Tương thích với shadcn/ui

### 3. Tailwind Configuration
**File**: `tailwind.config.js`

Extend Tailwind với:
- Brand colors
- Custom gradients
- Box shadows
- Background images

## 🎯 Màu sắc chính

### Brand Colors
```css
--primary: #E40066    /* Pink/Magenta */
--secondary: #03CEA4  /* Teal/<PERSON>an */
--accent: #9747FF     /* Purple */
```

### Semantic Colors
```css
--success: #03CEA4    /* Sử dụng secondary color */
--warning: #FFB800    /* Orange */
--error: #E40066      /* Sử dụng primary color */
--info: #9747FF       /* Sử dụng accent color */
```

### Gradients
```css
/* Primary Gradient: Pink → Purple */
background: linear-gradient(135deg, #E40066 0%, #9747FF 100%);

/* Secondary Gradient: Teal → Purple */
background: linear-gradient(135deg, #03CEA4 0%, #9747FF 100%);

/* Hero Gradient: Pink → Teal → Purple */
background: linear-gradient(135deg, #E40066 0%, #03CEA4 50%, #9747FF 100%);
```

## 🛠️ Sử dụng Theme

### 1. Tailwind Classes
```jsx
// Brand colors
<div className="bg-brand-primary text-white">Primary</div>
<div className="bg-brand-secondary text-white">Secondary</div>
<div className="bg-brand-accent text-white">Accent</div>

// Gradients
<div className="bg-gradient-primary">Primary Gradient</div>
<div className="bg-gradient-secondary">Secondary Gradient</div>
<div className="bg-gradient-hero">Hero Gradient</div>

// Shadows
<div className="shadow-primary">Primary Shadow</div>
<div className="shadow-secondary">Secondary Shadow</div>
<div className="shadow-accent">Accent Shadow</div>
```

### 2. CSS Variables
```jsx
// Sử dụng CSS variables
<div style={{ backgroundColor: 'hsl(var(--primary))' }}>
  Primary Background
</div>

// Inline styles với theme colors
<div style={{ background: 'linear-gradient(135deg, #E40066 0%, #9747FF 100%)' }}>
  Custom Gradient
</div>
```

### 3. Theme Hook
```jsx
import { useTheme } from '../hooks/useTheme';

function MyComponent() {
  const { theme, actualTheme, setTheme, toggleTheme } = useTheme();
  
  return (
    <div>
      <p>Current theme: {theme}</p>
      <p>Actual theme: {actualTheme}</p>
      <button onClick={toggleTheme}>Toggle Theme</button>
      <button onClick={() => setTheme('dark')}>Dark Mode</button>
    </div>
  );
}
```

## 🌓 Dark Mode

### Theme Toggle Component
```jsx
import { ThemeToggle } from '../components/common/ThemeToggle';

// Sử dụng trong Header hoặc bất kỳ đâu
<ThemeToggle />
```

### Theme States
- `light`: Light mode
- `dark`: Dark mode  
- `system`: Theo system preference

### Auto Detection
- Tự động detect system theme preference
- Listen for system theme changes
- Persist user preference trong localStorage

## 📱 Responsive Design

### Breakpoints
```css
sm: '640px'   /* Mobile landscape */
md: '768px'   /* Tablet */
lg: '1024px'  /* Desktop */
xl: '1280px'  /* Large desktop */
2xl: '1536px' /* Extra large */
```

### Mobile-first Approach
```jsx
<div className="text-sm md:text-base lg:text-lg">
  Responsive text
</div>
```

## 🎨 Component Examples

### Buttons
```jsx
// Brand buttons
<Button className="bg-brand-primary hover:bg-brand-primary/90">
  Primary Action
</Button>

<Button className="bg-brand-secondary hover:bg-brand-secondary/90">
  Secondary Action
</Button>

<Button className="bg-brand-accent hover:bg-brand-accent/90">
  Accent Action
</Button>
```

### Cards với Brand Colors
```jsx
<Card className="border-l-4 border-brand-primary">
  <CardHeader>
    <CardTitle className="text-brand-primary">Primary Card</CardTitle>
  </CardHeader>
</Card>
```

### Status Indicators
```jsx
// Success
<div className="p-4 bg-success/10 border border-success rounded-lg">
  <span className="text-success">Success message</span>
</div>

// Warning  
<div className="p-4 bg-warning/10 border border-warning rounded-lg">
  <span className="text-warning">Warning message</span>
</div>

// Error
<div className="p-4 bg-error/10 border border-error rounded-lg">
  <span className="text-error">Error message</span>
</div>
```

## 🔧 Customization

### Thêm màu mới
1. Cập nhật `src/styles/theme.ts`
2. Thêm vào `tailwind.config.js`
3. Cập nhật CSS variables nếu cần

### Tạo gradient mới
```js
// tailwind.config.js
backgroundImage: {
  'gradient-custom': 'linear-gradient(135deg, #color1 0%, #color2 100%)',
}
```

### Custom shadows
```js
// tailwind.config.js  
boxShadow: {
  'custom': '0 10px 15px -3px rgb(r g b / 0.3)',
}
```

## 📄 Files Structure

```
src/
├── styles/
│   └── theme.ts              # Theme configuration
├── hooks/
│   └── useTheme.ts           # Theme hook
├── components/
│   ├── common/
│   │   ├── ThemeToggle.tsx   # Theme toggle component
│   │   └── BrandShowcase.tsx # Theme showcase
│   └── ui/                   # shadcn/ui components
├── pages/
│   └── ThemePage.tsx         # Theme demo page
├── index.css                 # CSS variables
└── tailwind.config.js        # Tailwind configuration
```

## 🚀 Best Practices

1. **Sử dụng CSS variables** cho dynamic theming
2. **Prefer Tailwind classes** over inline styles
3. **Test cả light và dark mode** khi develop
4. **Sử dụng semantic colors** cho status indicators
5. **Maintain contrast ratios** cho accessibility
6. **Use brand colors consistently** across the app

## 🎯 Demo & Testing

Truy cập `/theme` để xem:
- Brand colors showcase
- Interactive examples
- Component demonstrations
- Theme toggle functionality

## 📚 Resources

- [Tailwind CSS Documentation](https://tailwindcss.com/docs)
- [shadcn/ui Documentation](https://ui.shadcn.com/)
- [CSS Custom Properties](https://developer.mozilla.org/en-US/docs/Web/CSS/--*)
- [Color Accessibility](https://webaim.org/articles/contrast/)
