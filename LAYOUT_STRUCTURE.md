# Layout Structure Documentation

## Tổng quan

Hệ thống layout được thiết kế để tránh code lặp lại và cung cấp cấu trúc linh hoạt cho các loại trang khác nhau.

## Các Layout Components

### 1. MainLayout
**File**: `src/components/layout/MainLayout.tsx`

Layout chính cho hầu hết các trang public, bao gồm Header và Footer.

```tsx
interface MainLayoutProps {
  showHeader?: boolean;
  showFooter?: boolean;
  className?: string;
}
```

**Sử dụng cho**: Trang chủ, Browse, Manga Detail, v.v.

### 2. AuthLayout
**File**: `src/components/layout/AuthLayout.tsx`

Layout wrapper để xử lý authentication logic.

```tsx
interface AuthLayoutProps {
  requireAuth?: boolean;
  redirectTo?: string;
}
```

**Ch<PERSON>c năng**:
- `requireAuth: true` - <PERSON><PERSON><PERSON> cầu đăng nhập, redirect đến `/auth/login` nếu chưa đăng nhập
- `requireAuth: false` - Redirect đến trang chủ nếu đã đăng nhập (cho login/register pages)

### 3. ReaderLayout
**File**: `src/components/layout/ReaderLayout.tsx`

Layout tối giản cho trang đọc truyện, không có Header/Footer để tối đa hóa không gian đọc.

```tsx
interface ReaderLayoutProps {
  className?: string;
}
```

**Sử dụng cho**: Trang đọc truyện (`/read/:chapterId`)

### 4. AdminLayout
**File**: `src/components/layout/AdminLayout.tsx`

Layout cho admin panel với sidebar navigation.

```tsx
interface AdminLayoutProps {
  className?: string;
}
```

**Chức năng**:
- Kiểm tra quyền admin/moderator
- Sidebar navigation cho admin features
- Header với admin-specific actions

## Cấu trúc Router

### Public Routes (MainLayout)
```
/ (MainLayout)
├── / (HomePage)
├── /browse (BrowsePage)
└── /manga/:id (MangaDetailPage)
```

### Auth Routes (MainLayout + AuthLayout)
```
/auth (MainLayout > AuthLayout requireAuth=false)
├── /auth/login (LoginPage)
└── /auth/register (RegisterPage)
```

### Protected Routes (MainLayout + AuthLayout)
```
/user (MainLayout > AuthLayout requireAuth=true)
├── /user/bookmarks (BookmarksPage)
├── /user/history (HistoryPage)
└── /user/profile (ProfilePage)
```

### Reader Routes (ReaderLayout)
```
/read (ReaderLayout)
└── /read/:chapterId (ReaderPage)
```

### Admin Routes (AdminLayout)
```
/admin (AdminLayout)
├── /admin (Dashboard)
├── /admin/manga (Manga Management)
├── /admin/users (User Management)
└── /admin/comments (Comment Management)
```

## Ưu điểm của cấu trúc này

### 1. Tránh Code Lặp Lại
- Header/Footer chỉ được định nghĩa một lần trong MainLayout
- Authentication logic được centralized trong AuthLayout
- Mỗi page component chỉ cần focus vào nội dung chính

### 2. Linh Hoạt
- Có thể dễ dàng thêm layout mới cho các use case khác
- Có thể customize layout cho từng route group
- Dễ dàng thay đổi layout structure mà không ảnh hưởng đến page components

### 3. Bảo Mật
- Authentication logic được handle ở layout level
- Tự động redirect dựa trên authentication state
- Role-based access control cho admin routes

### 4. Performance
- Layout components được reuse
- Nested routing giúp tối ưu re-rendering
- Code splitting có thể được áp dụng ở layout level

## Cách Thêm Route Mới

### 1. Public Route
```tsx
// Thêm vào MainLayout children
{
  path: "new-page",
  element: <NewPage />,
}
```

### 2. Protected Route
```tsx
// Thêm vào AuthLayout requireAuth=true children
{
  path: "user/new-feature",
  element: <NewFeaturePage />,
}
```

### 3. Admin Route
```tsx
// Thêm vào AdminLayout children
{
  path: "admin/new-admin-feature",
  element: <NewAdminFeaturePage />,
}
```

## Navigation Links

Cập nhật links trong Header để phù hợp với cấu trúc mới:

```tsx
// Old
<Link to="/login">Login</Link>
<Link to="/bookmarks">Bookmarks</Link>

// New
<Link to="/auth/login">Login</Link>
<Link to="/user/bookmarks">Bookmarks</Link>
```

## Best Practices

1. **Sử dụng đúng layout cho đúng mục đích**
2. **Không hardcode authentication logic trong page components**
3. **Sử dụng nested routing để tận dụng layout reuse**
4. **Cập nhật navigation links khi thay đổi route structure**
5. **Test authentication flows khi thêm protected routes**
