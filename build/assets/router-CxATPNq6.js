import{r as ya,g as Cr,a as va}from"./vendor-BtP0CW_r.js";function ga(e,t){for(var r=0;r<t.length;r++){const n=t[r];if(typeof n!="string"&&!Array.isArray(n)){for(const a in n)if(a!=="default"&&!(a in e)){const o=Object.getOwnPropertyDescriptor(n,a);o&&Object.defineProperty(e,a,o.get?o:{enumerable:!0,get:()=>n[a]})}}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}var p=ya();const wa=Cr(p),Do=ga({__proto__:null,default:wa},[p]);var Ye={},rr;function ba(){if(rr)return Ye;rr=1,Object.defineProperty(Ye,"__esModule",{value:!0}),Ye.parse=l,Ye.serialize=c;const e=/^[\u0021-\u003A\u003C\u003E-\u007E]+$/,t=/^[\u0021-\u003A\u003C-\u007E]*$/,r=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,n=/^[\u0020-\u003A\u003D-\u007E]*$/,a=Object.prototype.toString,o=(()=>{const h=function(){};return h.prototype=Object.create(null),h})();function l(h,b){const v=new o,x=h.length;if(x<2)return v;const C=(b==null?void 0:b.decode)||f;let w=0;do{const $=h.indexOf("=",w);if($===-1)break;const P=h.indexOf(";",w),k=P===-1?x:P;if($>k){w=h.lastIndexOf(";",$-1)+1;continue}const U=u(h,w,$),y=i(h,$,U),j=h.slice(U,y);if(v[j]===void 0){let V=u(h,$+1,k),A=i(h,k,V);const G=C(h.slice(V,A));v[j]=G}w=k+1}while(w<x);return v}function u(h,b,v){do{const x=h.charCodeAt(b);if(x!==32&&x!==9)return b}while(++b<v);return v}function i(h,b,v){for(;b>v;){const x=h.charCodeAt(--b);if(x!==32&&x!==9)return b+1}return v}function c(h,b,v){const x=(v==null?void 0:v.encode)||encodeURIComponent;if(!e.test(h))throw new TypeError(`argument name is invalid: ${h}`);const C=x(b);if(!t.test(C))throw new TypeError(`argument val is invalid: ${b}`);let w=h+"="+C;if(!v)return w;if(v.maxAge!==void 0){if(!Number.isInteger(v.maxAge))throw new TypeError(`option maxAge is invalid: ${v.maxAge}`);w+="; Max-Age="+v.maxAge}if(v.domain){if(!r.test(v.domain))throw new TypeError(`option domain is invalid: ${v.domain}`);w+="; Domain="+v.domain}if(v.path){if(!n.test(v.path))throw new TypeError(`option path is invalid: ${v.path}`);w+="; Path="+v.path}if(v.expires){if(!g(v.expires)||!Number.isFinite(v.expires.valueOf()))throw new TypeError(`option expires is invalid: ${v.expires}`);w+="; Expires="+v.expires.toUTCString()}if(v.httpOnly&&(w+="; HttpOnly"),v.secure&&(w+="; Secure"),v.partitioned&&(w+="; Partitioned"),v.priority)switch(typeof v.priority=="string"?v.priority.toLowerCase():void 0){case"low":w+="; Priority=Low";break;case"medium":w+="; Priority=Medium";break;case"high":w+="; Priority=High";break;default:throw new TypeError(`option priority is invalid: ${v.priority}`)}if(v.sameSite)switch(typeof v.sameSite=="string"?v.sameSite.toLowerCase():v.sameSite){case!0:case"strict":w+="; SameSite=Strict";break;case"lax":w+="; SameSite=Lax";break;case"none":w+="; SameSite=None";break;default:throw new TypeError(`option sameSite is invalid: ${v.sameSite}`)}return w}function f(h){if(h.indexOf("%")===-1)return h;try{return decodeURIComponent(h)}catch{return h}}function g(h){return a.call(h)==="[object Date]"}return Ye}ba();/**
 * react-router v7.6.2
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */var Pr=e=>{throw TypeError(e)},Ea=(e,t,r)=>t.has(e)||Pr("Cannot "+r),Lt=(e,t,r)=>(Ea(e,t,"read from private field"),r?r.call(e):t.get(e)),Ra=(e,t,r)=>t.has(e)?Pr("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,r),ar="popstate";function xa(e={}){function t(n,a){let{pathname:o,search:l,hash:u}=n.location;return Ze("",{pathname:o,search:l,hash:u},a.state&&a.state.usr||null,a.state&&a.state.key||"default")}function r(n,a){return typeof a=="string"?a:xe(a)}return Ca(t,r,null,e)}function _(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function K(e,t){if(!e){typeof console<"u"&&console.warn(t);try{throw new Error(t)}catch{}}}function Sa(){return Math.random().toString(36).substring(2,10)}function nr(e,t){return{usr:e.state,key:e.key,idx:t}}function Ze(e,t,r=null,n){return{pathname:typeof e=="string"?e:e.pathname,search:"",hash:"",...typeof t=="string"?Se(t):t,state:r,key:t&&t.key||n||Sa()}}function xe({pathname:e="/",search:t="",hash:r=""}){return t&&t!=="?"&&(e+=t.charAt(0)==="?"?t:"?"+t),r&&r!=="#"&&(e+=r.charAt(0)==="#"?r:"#"+r),e}function Se(e){let t={};if(e){let r=e.indexOf("#");r>=0&&(t.hash=e.substring(r),e=e.substring(0,r));let n=e.indexOf("?");n>=0&&(t.search=e.substring(n),e=e.substring(0,n)),e&&(t.pathname=e)}return t}function Ca(e,t,r,n={}){let{window:a=document.defaultView,v5Compat:o=!1}=n,l=a.history,u="POP",i=null,c=f();c==null&&(c=0,l.replaceState({...l.state,idx:c},""));function f(){return(l.state||{idx:null}).idx}function g(){u="POP";let C=f(),w=C==null?null:C-c;c=C,i&&i({action:u,location:x.location,delta:w})}function h(C,w){u="PUSH";let $=Ze(x.location,C,w);c=f()+1;let P=nr($,c),k=x.createHref($);try{l.pushState(P,"",k)}catch(U){if(U instanceof DOMException&&U.name==="DataCloneError")throw U;a.location.assign(k)}o&&i&&i({action:u,location:x.location,delta:1})}function b(C,w){u="REPLACE";let $=Ze(x.location,C,w);c=f();let P=nr($,c),k=x.createHref($);l.replaceState(P,"",k),o&&i&&i({action:u,location:x.location,delta:0})}function v(C){return Lr(C)}let x={get action(){return u},get location(){return e(a,l)},listen(C){if(i)throw new Error("A history only accepts one active listener");return a.addEventListener(ar,g),i=C,()=>{a.removeEventListener(ar,g),i=null}},createHref(C){return t(a,C)},createURL:v,encodeLocation(C){let w=v(C);return{pathname:w.pathname,search:w.search,hash:w.hash}},push:h,replace:b,go(C){return l.go(C)}};return x}function Lr(e,t=!1){let r="http://localhost";typeof window<"u"&&(r=window.location.origin!=="null"?window.location.origin:window.location.href),_(r,"No window.location.(origin|href) available to create URL");let n=typeof e=="string"?e:xe(e);return n=n.replace(/ $/,"%20"),!t&&n.startsWith("//")&&(n=r+n),new URL(n,r)}var Xe,or=class{constructor(e){if(Ra(this,Xe,new Map),e)for(let[t,r]of e)this.set(t,r)}get(e){if(Lt(this,Xe).has(e))return Lt(this,Xe).get(e);if(e.defaultValue!==void 0)return e.defaultValue;throw new Error("No value found for context")}set(e,t){Lt(this,Xe).set(e,t)}};Xe=new WeakMap;var Pa=new Set(["lazy","caseSensitive","path","id","index","children"]);function La(e){return Pa.has(e)}var Da=new Set(["lazy","caseSensitive","path","id","index","unstable_middleware","children"]);function Ma(e){return Da.has(e)}function Ta(e){return e.index===!0}function mt(e,t,r=[],n={}){return e.map((a,o)=>{let l=[...r,String(o)],u=typeof a.id=="string"?a.id:l.join("-");if(_(a.index!==!0||!a.children,"Cannot specify children on an index route"),_(!n[u],`Found a route id collision on id "${u}".  Route id's must be globally unique within Data Router usages`),Ta(a)){let i={...a,...t(a),id:u};return n[u]=i,i}else{let i={...a,...t(a),id:u,children:void 0};return n[u]=i,a.children&&(i.children=mt(a.children,t,l,n)),i}})}function Re(e,t,r="/"){return dt(e,t,r,!1)}function dt(e,t,r,n){let a=typeof t=="string"?Se(t):t,o=ce(a.pathname||"/",r);if(o==null)return null;let l=Dr(e);Oa(l);let u=null;for(let i=0;u==null&&i<l.length;++i){let c=Ba(o);u=ja(l[i],c,n)}return u}function Fa(e,t){let{route:r,pathname:n,params:a}=e;return{id:r.id,pathname:n,params:a,data:t[r.id],handle:r.handle}}function Dr(e,t=[],r=[],n=""){let a=(o,l,u)=>{let i={relativePath:u===void 0?o.path||"":u,caseSensitive:o.caseSensitive===!0,childrenIndex:l,route:o};i.relativePath.startsWith("/")&&(_(i.relativePath.startsWith(n),`Absolute route path "${i.relativePath}" nested under path "${n}" is not valid. An absolute child route path must start with the combined path of all its parent routes.`),i.relativePath=i.relativePath.slice(n.length));let c=ye([n,i.relativePath]),f=r.concat(i);o.children&&o.children.length>0&&(_(o.index!==!0,`Index routes must not have child routes. Please remove all child routes from route path "${c}".`),Dr(o.children,t,f,c)),!(o.path==null&&!o.index)&&t.push({path:c,score:za(c,o.index),routesMeta:f})};return e.forEach((o,l)=>{var u;if(o.path===""||!((u=o.path)!=null&&u.includes("?")))a(o,l);else for(let i of Mr(o.path))a(o,l,i)}),t}function Mr(e){let t=e.split("/");if(t.length===0)return[];let[r,...n]=t,a=r.endsWith("?"),o=r.replace(/\?$/,"");if(n.length===0)return a?[o,""]:[o];let l=Mr(n.join("/")),u=[];return u.push(...l.map(i=>i===""?o:[o,i].join("/"))),a&&u.push(...l),u.map(i=>e.startsWith("/")&&i===""?"/":i)}function Oa(e){e.sort((t,r)=>t.score!==r.score?r.score-t.score:Ia(t.routesMeta.map(n=>n.childrenIndex),r.routesMeta.map(n=>n.childrenIndex)))}var $a=/^:[\w-]+$/,Na=3,ka=2,Ua=1,_a=10,Aa=-2,ir=e=>e==="*";function za(e,t){let r=e.split("/"),n=r.length;return r.some(ir)&&(n+=Aa),t&&(n+=ka),r.filter(a=>!ir(a)).reduce((a,o)=>a+($a.test(o)?Na:o===""?Ua:_a),n)}function Ia(e,t){return e.length===t.length&&e.slice(0,-1).every((n,a)=>n===t[a])?e[e.length-1]-t[t.length-1]:0}function ja(e,t,r=!1){let{routesMeta:n}=e,a={},o="/",l=[];for(let u=0;u<n.length;++u){let i=n[u],c=u===n.length-1,f=o==="/"?t:t.slice(o.length)||"/",g=pt({path:i.relativePath,caseSensitive:i.caseSensitive,end:c},f),h=i.route;if(!g&&c&&r&&!n[n.length-1].route.index&&(g=pt({path:i.relativePath,caseSensitive:i.caseSensitive,end:!1},f)),!g)return null;Object.assign(a,g.params),l.push({params:a,pathname:ye([o,g.pathname]),pathnameBase:Ka(ye([o,g.pathnameBase])),route:h}),g.pathnameBase!=="/"&&(o=ye([o,g.pathnameBase]))}return l}function pt(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[r,n]=Ha(e.path,e.caseSensitive,e.end),a=t.match(r);if(!a)return null;let o=a[0],l=o.replace(/(.)\/+$/,"$1"),u=a.slice(1);return{params:n.reduce((c,{paramName:f,isOptional:g},h)=>{if(f==="*"){let v=u[h]||"";l=o.slice(0,o.length-v.length).replace(/(.)\/+$/,"$1")}const b=u[h];return g&&!b?c[f]=void 0:c[f]=(b||"").replace(/%2F/g,"/"),c},{}),pathname:o,pathnameBase:l,pattern:e}}function Ha(e,t=!1,r=!0){K(e==="*"||!e.endsWith("*")||e.endsWith("/*"),`Route path "${e}" will be treated as if it were "${e.replace(/\*$/,"/*")}" because the \`*\` character must always follow a \`/\` in the pattern. To get rid of this warning, please change the route path to "${e.replace(/\*$/,"/*")}".`);let n=[],a="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(l,u,i)=>(n.push({paramName:u,isOptional:i!=null}),i?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(n.push({paramName:"*"}),a+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):r?a+="\\/*$":e!==""&&e!=="/"&&(a+="(?:(?=\\/|$))"),[new RegExp(a,t?void 0:"i"),n]}function Ba(e){try{return e.split("/").map(t=>decodeURIComponent(t).replace(/\//g,"%2F")).join("/")}catch(t){return K(!1,`The URL path "${e}" could not be decoded because it is a malformed URL segment. This is probably due to a bad percent encoding (${t}).`),e}}function ce(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let r=t.endsWith("/")?t.length-1:t.length,n=e.charAt(r);return n&&n!=="/"?null:e.slice(r)||"/"}function Wa(e,t="/"){let{pathname:r,search:n="",hash:a=""}=typeof e=="string"?Se(e):e;return{pathname:r?r.startsWith("/")?r:Va(r,t):t,search:Ya(n),hash:Ja(a)}}function Va(e,t){let r=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(a=>{a===".."?r.length>1&&r.pop():a!=="."&&r.push(a)}),r.length>1?r.join("/"):"/"}function Dt(e,t,r,n){return`Cannot include a '${e}' character in a manually specified \`to.${t}\` field [${JSON.stringify(n)}].  Please separate it out to the \`to.${r}\` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.`}function Tr(e){return e.filter((t,r)=>r===0||t.route.path&&t.route.path.length>0)}function gt(e){let t=Tr(e);return t.map((r,n)=>n===t.length-1?r.pathname:r.pathnameBase)}function wt(e,t,r,n=!1){let a;typeof e=="string"?a=Se(e):(a={...e},_(!a.pathname||!a.pathname.includes("?"),Dt("?","pathname","search",a)),_(!a.pathname||!a.pathname.includes("#"),Dt("#","pathname","hash",a)),_(!a.search||!a.search.includes("#"),Dt("#","search","hash",a)));let o=e===""||a.pathname==="",l=o?"/":a.pathname,u;if(l==null)u=r;else{let g=t.length-1;if(!n&&l.startsWith("..")){let h=l.split("/");for(;h[0]==="..";)h.shift(),g-=1;a.pathname=h.join("/")}u=g>=0?t[g]:"/"}let i=Wa(a,u),c=l&&l!=="/"&&l.endsWith("/"),f=(o||l===".")&&r.endsWith("/");return!i.pathname.endsWith("/")&&(c||f)&&(i.pathname+="/"),i}var ye=e=>e.join("/").replace(/\/\/+/g,"/"),Ka=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),Ya=e=>!e||e==="?"?"":e.startsWith("?")?e:"?"+e,Ja=e=>!e||e==="#"?"":e.startsWith("#")?e:"#"+e,yt=class{constructor(e,t,r,n=!1){this.status=e,this.statusText=t||"",this.internal=n,r instanceof Error?(this.data=r.toString(),this.error=r):this.data=r}};function et(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}var Fr=["POST","PUT","PATCH","DELETE"],qa=new Set(Fr),Ga=["GET",...Fr],Xa=new Set(Ga),Qa=new Set([301,302,303,307,308]),Za=new Set([307,308]),Mt={state:"idle",location:void 0,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0},en={state:"idle",data:void 0,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0},Je={state:"unblocked",proceed:void 0,reset:void 0,location:void 0},Ut=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,tn=e=>({hasErrorBoundary:!!e.hasErrorBoundary}),Or="remix-router-transitions",$r=Symbol("ResetLoaderData");function rn(e){const t=e.window?e.window:typeof window<"u"?window:void 0,r=typeof t<"u"&&typeof t.document<"u"&&typeof t.document.createElement<"u";_(e.routes.length>0,"You must provide a non-empty routes array to createRouter");let n=e.hydrationRouteProperties||[],a=e.mapRouteProperties||tn,o={},l=mt(e.routes,a,void 0,o),u,i=e.basename||"/",c=e.dataStrategy||sn,f={unstable_middleware:!1,...e.future},g=null,h=new Set,b=null,v=null,x=null,C=e.hydrationData!=null,w=Re(l,e.history.location,i),$=!1,P=null,k;if(w==null&&!e.patchRoutesOnNavigation){let s=ue(404,{pathname:e.history.location.pathname}),{matches:d,route:m}=gr(l);k=!0,w=d,P={[m.id]:s}}else if(w&&!e.hydrationData&&ot(w,l,e.history.location.pathname).active&&(w=null),w)if(w.some(s=>s.route.lazy))k=!1;else if(!w.some(s=>s.route.loader))k=!0;else{let s=e.hydrationData?e.hydrationData.loaderData:null,d=e.hydrationData?e.hydrationData.errors:null;if(d){let m=w.findIndex(E=>d[E.route.id]!==void 0);k=w.slice(0,m+1).every(E=>!$t(E.route,s,d))}else k=w.every(m=>!$t(m.route,s,d))}else{k=!1,w=[];let s=ot(null,l,e.history.location.pathname);s.active&&s.matches&&($=!0,w=s.matches)}let U,y={historyAction:e.history.action,location:e.history.location,matches:w,initialized:k,navigation:Mt,restoreScrollPosition:e.hydrationData!=null?!1:null,preventScrollReset:!1,revalidation:"idle",loaderData:e.hydrationData&&e.hydrationData.loaderData||{},actionData:e.hydrationData&&e.hydrationData.actionData||null,errors:e.hydrationData&&e.hydrationData.errors||P,fetchers:new Map,blockers:new Map},j="POP",V=!1,A,G=!1,de=new Map,Pe=null,Le=!1,H=!1,Y=new Set,B=new Map,Q=0,ae=-1,Z=new Map,re=new Set,fe=new Map,he=new Map,ee=new Set,De=new Map,at,Me=null;function Zr(){if(g=e.history.listen(({action:s,location:d,delta:m})=>{if(at){at(),at=void 0;return}K(De.size===0||m!=null,"You are trying to use a blocker on a POP navigation to a location that was not created by @remix-run/router. This will fail silently in production. This can happen if you are navigating outside the router via `window.history.pushState`/`window.location.hash` instead of using router navigation APIs.  This can also happen if you are using createHashRouter and the user manually changes the URL.");let E=Qt({currentLocation:y.location,nextLocation:d,historyAction:s});if(E&&m!=null){let R=new Promise(L=>{at=L});e.history.go(m*-1),nt(E,{state:"blocked",location:d,proceed(){nt(E,{state:"proceeding",proceed:void 0,reset:void 0,location:d}),R.then(()=>e.history.go(m))},reset(){let L=new Map(y.blockers);L.set(E,Je),te({blockers:L})}});return}return Te(s,d)}),r){wn(t,de);let s=()=>bn(t,de);t.addEventListener("pagehide",s),Pe=()=>t.removeEventListener("pagehide",s)}return y.initialized||Te("POP",y.location,{initialHydration:!0}),U}function ea(){g&&g(),Pe&&Pe(),h.clear(),A&&A.abort(),y.fetchers.forEach((s,d)=>xt(d)),y.blockers.forEach((s,d)=>Xt(d))}function ta(s){return h.add(s),()=>h.delete(s)}function te(s,d={}){y={...y,...s};let m=[],E=[];y.fetchers.forEach((R,L)=>{R.state==="idle"&&(ee.has(L)?m.push(L):E.push(L))}),ee.forEach(R=>{!y.fetchers.has(R)&&!B.has(R)&&m.push(R)}),[...h].forEach(R=>R(y,{deletedFetchers:m,viewTransitionOpts:d.viewTransitionOpts,flushSync:d.flushSync===!0})),m.forEach(R=>xt(R)),E.forEach(R=>y.fetchers.delete(R))}function _e(s,d,{flushSync:m}={}){var T,O;let E=y.actionData!=null&&y.navigation.formMethod!=null&&ie(y.navigation.formMethod)&&y.navigation.state==="loading"&&((T=s.state)==null?void 0:T._isRedirect)!==!0,R;d.actionData?Object.keys(d.actionData).length>0?R=d.actionData:R=null:E?R=y.actionData:R=null;let L=d.loaderData?yr(y.loaderData,d.loaderData,d.matches||[],d.errors):y.loaderData,F=y.blockers;F.size>0&&(F=new Map(F),F.forEach((M,N)=>F.set(N,Je)));let S=V===!0||y.navigation.formMethod!=null&&ie(y.navigation.formMethod)&&((O=s.state)==null?void 0:O._isRedirect)!==!0;u&&(l=u,u=void 0),Le||j==="POP"||(j==="PUSH"?e.history.push(s,s.state):j==="REPLACE"&&e.history.replace(s,s.state));let D;if(j==="POP"){let M=de.get(y.location.pathname);M&&M.has(s.pathname)?D={currentLocation:y.location,nextLocation:s}:de.has(s.pathname)&&(D={currentLocation:s,nextLocation:y.location})}else if(G){let M=de.get(y.location.pathname);M?M.add(s.pathname):(M=new Set([s.pathname]),de.set(y.location.pathname,M)),D={currentLocation:y.location,nextLocation:s}}te({...d,actionData:R,loaderData:L,historyAction:j,location:s,initialized:!0,navigation:Mt,revalidation:"idle",restoreScrollPosition:er(s,d.matches||y.matches),preventScrollReset:S,blockers:F},{viewTransitionOpts:D,flushSync:m===!0}),j="POP",V=!1,G=!1,Le=!1,H=!1,Me==null||Me.resolve(),Me=null}async function Wt(s,d){if(typeof s=="number"){e.history.go(s);return}let m=Ot(y.location,y.matches,i,s,d==null?void 0:d.fromRouteId,d==null?void 0:d.relative),{path:E,submission:R,error:L}=lr(!1,m,d),F=y.location,S=Ze(y.location,E,d&&d.state);S={...S,...e.history.encodeLocation(S)};let D=d&&d.replace!=null?d.replace:void 0,T="PUSH";D===!0?T="REPLACE":D===!1||R!=null&&ie(R.formMethod)&&R.formAction===y.location.pathname+y.location.search&&(T="REPLACE");let O=d&&"preventScrollReset"in d?d.preventScrollReset===!0:void 0,M=(d&&d.flushSync)===!0,N=Qt({currentLocation:F,nextLocation:S,historyAction:T});if(N){nt(N,{state:"blocked",location:S,proceed(){nt(N,{state:"proceeding",proceed:void 0,reset:void 0,location:S}),Wt(s,d)},reset(){let W=new Map(y.blockers);W.set(N,Je),te({blockers:W})}});return}await Te(T,S,{submission:R,pendingError:L,preventScrollReset:O,replace:d&&d.replace,enableViewTransition:d&&d.viewTransition,flushSync:M})}function ra(){Me||(Me=En()),Rt(),te({revalidation:"loading"});let s=Me.promise;return y.navigation.state==="submitting"?s:y.navigation.state==="idle"?(Te(y.historyAction,y.location,{startUninterruptedRevalidation:!0}),s):(Te(j||y.historyAction,y.navigation.location,{overrideNavigation:y.navigation,enableViewTransition:G===!0}),s)}async function Te(s,d,m){A&&A.abort(),A=null,j=s,Le=(m&&m.startUninterruptedRevalidation)===!0,fa(y.location,y.matches),V=(m&&m.preventScrollReset)===!0,G=(m&&m.enableViewTransition)===!0;let E=u||l,R=m&&m.overrideNavigation,L=m!=null&&m.initialHydration&&y.matches&&y.matches.length>0&&!$?y.matches:Re(E,d,i),F=(m&&m.flushSync)===!0;if(L&&y.initialized&&!H&&mn(y.location,d)&&!(m&&m.submission&&ie(m.submission.formMethod))){_e(d,{matches:L},{flushSync:F});return}let S=ot(L,E,d.pathname);if(S.active&&S.matches&&(L=S.matches),!L){let{error:X,notFoundMatches:ne,route:z}=St(d.pathname);_e(d,{matches:ne,loaderData:{},errors:{[z.id]:X}},{flushSync:F});return}A=new AbortController;let D=ze(e.history,d,A.signal,m&&m.submission),T=new or(e.unstable_getContext?await e.unstable_getContext():void 0),O;if(m&&m.pendingError)O=[Ne(L).route.id,{type:"error",error:m.pendingError}];else if(m&&m.submission&&ie(m.submission.formMethod)){let X=await aa(D,d,m.submission,L,T,S.active,m&&m.initialHydration===!0,{replace:m.replace,flushSync:F});if(X.shortCircuited)return;if(X.pendingActionResult){let[ne,z]=X.pendingActionResult;if(oe(z)&&et(z.error)&&z.error.status===404){A=null,_e(d,{matches:X.matches,loaderData:{},errors:{[ne]:z.error}});return}}L=X.matches||L,O=X.pendingActionResult,R=Tt(d,m.submission),F=!1,S.active=!1,D=ze(e.history,D.url,D.signal)}let{shortCircuited:M,matches:N,loaderData:W,errors:q}=await na(D,d,L,T,S.active,R,m&&m.submission,m&&m.fetcherSubmission,m&&m.replace,m&&m.initialHydration===!0,F,O);M||(A=null,_e(d,{matches:N||L,...vr(O),loaderData:W,errors:q}))}async function aa(s,d,m,E,R,L,F,S={}){Rt();let D=vn(d,m);if(te({navigation:D},{flushSync:S.flushSync===!0}),L){let M=await it(E,d.pathname,s.signal);if(M.type==="aborted")return{shortCircuited:!0};if(M.type==="error"){let N=Ne(M.partialMatches).route.id;return{matches:M.partialMatches,pendingActionResult:[N,{type:"error",error:M.error}]}}else if(M.matches)E=M.matches;else{let{notFoundMatches:N,error:W,route:q}=St(d.pathname);return{matches:N,pendingActionResult:[q.id,{type:"error",error:W}]}}}let T,O=Qe(E,d);if(!O.route.action&&!O.route.lazy)T={type:"error",error:ue(405,{method:s.method,pathname:d.pathname,routeId:O.route.id})};else{let M=Ie(a,o,s,E,O,F?[]:n,R),N=await He(s,M,R,null);if(T=N[O.route.id],!T){for(let W of E)if(N[W.route.id]){T=N[W.route.id];break}}if(s.signal.aborted)return{shortCircuited:!0}}if(ke(T)){let M;return S&&S.replace!=null?M=S.replace:M=hr(T.response.headers.get("Location"),new URL(s.url),i)===y.location.pathname+y.location.search,await Fe(s,T,!0,{submission:m,replace:M}),{shortCircuited:!0}}if(oe(T)){let M=Ne(E,O.route.id);return(S&&S.replace)!==!0&&(j="PUSH"),{matches:E,pendingActionResult:[M.route.id,T,O.route.id]}}return{matches:E,pendingActionResult:[O.route.id,T]}}async function na(s,d,m,E,R,L,F,S,D,T,O,M){let N=L||Tt(d,F),W=F||S||br(N),q=!Le&&!T;if(R){if(q){let se=Vt(M);te({navigation:N,...se!==void 0?{actionData:se}:{}},{flushSync:O})}let I=await it(m,d.pathname,s.signal);if(I.type==="aborted")return{shortCircuited:!0};if(I.type==="error"){let se=Ne(I.partialMatches).route.id;return{matches:I.partialMatches,loaderData:{},errors:{[se]:I.error}}}else if(I.matches)m=I.matches;else{let{error:se,notFoundMatches:be,route:st}=St(d.pathname);return{matches:be,loaderData:{},errors:{[st.id]:se}}}}let X=u||l,{dsMatches:ne,revalidatingFetchers:z}=sr(s,E,a,o,e.history,y,m,W,d,T?[]:n,T===!0,H,Y,ee,fe,re,X,i,e.patchRoutesOnNavigation!=null,M);if(ae=++Q,!e.dataStrategy&&!ne.some(I=>I.shouldLoad)&&z.length===0){let I=qt();return _e(d,{matches:m,loaderData:{},errors:M&&oe(M[1])?{[M[0]]:M[1].error}:null,...vr(M),...I?{fetchers:new Map(y.fetchers)}:{}},{flushSync:O}),{shortCircuited:!0}}if(q){let I={};if(!R){I.navigation=N;let se=Vt(M);se!==void 0&&(I.actionData=se)}z.length>0&&(I.fetchers=oa(z)),te(I,{flushSync:O})}z.forEach(I=>{we(I.key),I.controller&&B.set(I.key,I.controller)});let Be=()=>z.forEach(I=>we(I.key));A&&A.signal.addEventListener("abort",Be);let{loaderResults:Oe,fetcherResults:We}=await Kt(ne,z,s,E);if(s.signal.aborted)return{shortCircuited:!0};A&&A.signal.removeEventListener("abort",Be),z.forEach(I=>B.delete(I.key));let le=ut(Oe);if(le)return await Fe(s,le.result,!0,{replace:D}),{shortCircuited:!0};if(le=ut(We),le)return re.add(le.key),await Fe(s,le.result,!0,{replace:D}),{shortCircuited:!0};let{loaderData:Ve,errors:Ke}=pr(y,m,Oe,M,z,We);T&&y.errors&&(Ke={...y.errors,...Ke});let Ct=qt(),$e=Gt(ae),lt=Ct||$e||z.length>0;return{matches:m,loaderData:Ve,errors:Ke,...lt?{fetchers:new Map(y.fetchers)}:{}}}function Vt(s){if(s&&!oe(s[1]))return{[s[0]]:s[1].data};if(y.actionData)return Object.keys(y.actionData).length===0?null:y.actionData}function oa(s){return s.forEach(d=>{let m=y.fetchers.get(d.key),E=qe(void 0,m?m.data:void 0);y.fetchers.set(d.key,E)}),new Map(y.fetchers)}async function ia(s,d,m,E){we(s);let R=(E&&E.flushSync)===!0,L=u||l,F=Ot(y.location,y.matches,i,m,d,E==null?void 0:E.relative),S=Re(L,F,i),D=ot(S,L,F);if(D.active&&D.matches&&(S=D.matches),!S){ve(s,d,ue(404,{pathname:F}),{flushSync:R});return}let{path:T,submission:O,error:M}=lr(!0,F,E);if(M){ve(s,d,M,{flushSync:R});return}let N=Qe(S,T),W=new or(e.unstable_getContext?await e.unstable_getContext():void 0),q=(E&&E.preventScrollReset)===!0;if(O&&ie(O.formMethod)){await la(s,d,T,N,S,W,D.active,R,q,O);return}fe.set(s,{routeId:d,path:T}),await sa(s,d,T,N,S,W,D.active,R,q,O)}async function la(s,d,m,E,R,L,F,S,D,T){Rt(),fe.delete(s);function O(J){if(!J.route.action&&!J.route.lazy){let Ae=ue(405,{method:T.formMethod,pathname:m,routeId:d});return ve(s,d,Ae,{flushSync:S}),!0}return!1}if(!F&&O(E))return;let M=y.fetchers.get(s);ge(s,gn(T,M),{flushSync:S});let N=new AbortController,W=ze(e.history,m,N.signal,T);if(F){let J=await it(R,m,W.signal,s);if(J.type==="aborted")return;if(J.type==="error"){ve(s,d,J.error,{flushSync:S});return}else if(J.matches){if(R=J.matches,E=Qe(R,m),O(E))return}else{ve(s,d,ue(404,{pathname:m}),{flushSync:S});return}}B.set(s,N);let q=Q,X=Ie(a,o,W,R,E,n,L),z=(await He(W,X,L,s))[E.route.id];if(W.signal.aborted){B.get(s)===N&&B.delete(s);return}if(ee.has(s)){if(ke(z)||oe(z)){ge(s,Ee(void 0));return}}else{if(ke(z))if(B.delete(s),ae>q){ge(s,Ee(void 0));return}else return re.add(s),ge(s,qe(T)),Fe(W,z,!1,{fetcherSubmission:T,preventScrollReset:D});if(oe(z)){ve(s,d,z.error);return}}let Be=y.navigation.location||y.location,Oe=ze(e.history,Be,N.signal),We=u||l,le=y.navigation.state!=="idle"?Re(We,y.navigation.location,i):y.matches;_(le,"Didn't find any matches after fetcher action");let Ve=++Q;Z.set(s,Ve);let Ke=qe(T,z.data);y.fetchers.set(s,Ke);let{dsMatches:Ct,revalidatingFetchers:$e}=sr(Oe,L,a,o,e.history,y,le,T,Be,n,!1,H,Y,ee,fe,re,We,i,e.patchRoutesOnNavigation!=null,[E.route.id,z]);$e.filter(J=>J.key!==s).forEach(J=>{let Ae=J.key,tr=y.fetchers.get(Ae),pa=qe(void 0,tr?tr.data:void 0);y.fetchers.set(Ae,pa),we(Ae),J.controller&&B.set(Ae,J.controller)}),te({fetchers:new Map(y.fetchers)});let lt=()=>$e.forEach(J=>we(J.key));N.signal.addEventListener("abort",lt);let{loaderResults:I,fetcherResults:se}=await Kt(Ct,$e,Oe,L);if(N.signal.aborted)return;if(N.signal.removeEventListener("abort",lt),Z.delete(s),B.delete(s),$e.forEach(J=>B.delete(J.key)),y.fetchers.has(s)){let J=Ee(z.data);y.fetchers.set(s,J)}let be=ut(I);if(be)return Fe(Oe,be.result,!1,{preventScrollReset:D});if(be=ut(se),be)return re.add(be.key),Fe(Oe,be.result,!1,{preventScrollReset:D});let{loaderData:st,errors:Pt}=pr(y,le,I,void 0,$e,se);Gt(Ve),y.navigation.state==="loading"&&Ve>ae?(_(j,"Expected pending action"),A&&A.abort(),_e(y.navigation.location,{matches:le,loaderData:st,errors:Pt,fetchers:new Map(y.fetchers)})):(te({errors:Pt,loaderData:yr(y.loaderData,st,le,Pt),fetchers:new Map(y.fetchers)}),H=!1)}async function sa(s,d,m,E,R,L,F,S,D,T){let O=y.fetchers.get(s);ge(s,qe(T,O?O.data:void 0),{flushSync:S});let M=new AbortController,N=ze(e.history,m,M.signal);if(F){let z=await it(R,m,N.signal,s);if(z.type==="aborted")return;if(z.type==="error"){ve(s,d,z.error,{flushSync:S});return}else if(z.matches)R=z.matches,E=Qe(R,m);else{ve(s,d,ue(404,{pathname:m}),{flushSync:S});return}}B.set(s,M);let W=Q,q=Ie(a,o,N,R,E,n,L),ne=(await He(N,q,L,s))[E.route.id];if(B.get(s)===M&&B.delete(s),!N.signal.aborted){if(ee.has(s)){ge(s,Ee(void 0));return}if(ke(ne))if(ae>W){ge(s,Ee(void 0));return}else{re.add(s),await Fe(N,ne,!1,{preventScrollReset:D});return}if(oe(ne)){ve(s,d,ne.error);return}ge(s,Ee(ne.data))}}async function Fe(s,d,m,{submission:E,fetcherSubmission:R,preventScrollReset:L,replace:F}={}){d.response.headers.has("X-Remix-Revalidate")&&(H=!0);let S=d.response.headers.get("Location");_(S,"Expected a Location header on the redirect Response"),S=hr(S,new URL(s.url),i);let D=Ze(y.location,S,{_isRedirect:!0});if(r){let q=!1;if(d.response.headers.has("X-Remix-Reload-Document"))q=!0;else if(Ut.test(S)){const X=Lr(S,!0);q=X.origin!==t.location.origin||ce(X.pathname,i)==null}if(q){F?t.location.replace(S):t.location.assign(S);return}}A=null;let T=F===!0||d.response.headers.has("X-Remix-Replace")?"REPLACE":"PUSH",{formMethod:O,formAction:M,formEncType:N}=y.navigation;!E&&!R&&O&&M&&N&&(E=br(y.navigation));let W=E||R;if(Za.has(d.response.status)&&W&&ie(W.formMethod))await Te(T,D,{submission:{...W,formAction:S},preventScrollReset:L||V,enableViewTransition:m?G:void 0});else{let q=Tt(D,E);await Te(T,D,{overrideNavigation:q,fetcherSubmission:R,preventScrollReset:L||V,enableViewTransition:m?G:void 0})}}async function He(s,d,m,E){let R,L={};try{R=await un(c,s,d,E,m,!1)}catch(F){return d.filter(S=>S.shouldLoad).forEach(S=>{L[S.route.id]={type:"error",error:F}}),L}if(s.signal.aborted)return L;for(let[F,S]of Object.entries(R))if(pn(S)){let D=S.result;L[F]={type:"redirect",response:fn(D,s,F,d,i)}}else L[F]=await dn(S);return L}async function Kt(s,d,m,E){let R=He(m,s,E,null),L=Promise.all(d.map(async D=>{if(D.matches&&D.match&&D.request&&D.controller){let O=(await He(D.request,D.matches,E,D.key))[D.match.route.id];return{[D.key]:O}}else return Promise.resolve({[D.key]:{type:"error",error:ue(404,{pathname:D.path})}})})),F=await R,S=(await L).reduce((D,T)=>Object.assign(D,T),{});return{loaderResults:F,fetcherResults:S}}function Rt(){H=!0,fe.forEach((s,d)=>{B.has(d)&&Y.add(d),we(d)})}function ge(s,d,m={}){y.fetchers.set(s,d),te({fetchers:new Map(y.fetchers)},{flushSync:(m&&m.flushSync)===!0})}function ve(s,d,m,E={}){let R=Ne(y.matches,d);xt(s),te({errors:{[R.route.id]:m},fetchers:new Map(y.fetchers)},{flushSync:(E&&E.flushSync)===!0})}function Yt(s){return he.set(s,(he.get(s)||0)+1),ee.has(s)&&ee.delete(s),y.fetchers.get(s)||en}function xt(s){let d=y.fetchers.get(s);B.has(s)&&!(d&&d.state==="loading"&&Z.has(s))&&we(s),fe.delete(s),Z.delete(s),re.delete(s),ee.delete(s),Y.delete(s),y.fetchers.delete(s)}function ua(s){let d=(he.get(s)||0)-1;d<=0?(he.delete(s),ee.add(s)):he.set(s,d),te({fetchers:new Map(y.fetchers)})}function we(s){let d=B.get(s);d&&(d.abort(),B.delete(s))}function Jt(s){for(let d of s){let m=Yt(d),E=Ee(m.data);y.fetchers.set(d,E)}}function qt(){let s=[],d=!1;for(let m of re){let E=y.fetchers.get(m);_(E,`Expected fetcher: ${m}`),E.state==="loading"&&(re.delete(m),s.push(m),d=!0)}return Jt(s),d}function Gt(s){let d=[];for(let[m,E]of Z)if(E<s){let R=y.fetchers.get(m);_(R,`Expected fetcher: ${m}`),R.state==="loading"&&(we(m),Z.delete(m),d.push(m))}return Jt(d),d.length>0}function ca(s,d){let m=y.blockers.get(s)||Je;return De.get(s)!==d&&De.set(s,d),m}function Xt(s){y.blockers.delete(s),De.delete(s)}function nt(s,d){let m=y.blockers.get(s)||Je;_(m.state==="unblocked"&&d.state==="blocked"||m.state==="blocked"&&d.state==="blocked"||m.state==="blocked"&&d.state==="proceeding"||m.state==="blocked"&&d.state==="unblocked"||m.state==="proceeding"&&d.state==="unblocked",`Invalid blocker state transition: ${m.state} -> ${d.state}`);let E=new Map(y.blockers);E.set(s,d),te({blockers:E})}function Qt({currentLocation:s,nextLocation:d,historyAction:m}){if(De.size===0)return;De.size>1&&K(!1,"A router only supports one blocker at a time");let E=Array.from(De.entries()),[R,L]=E[E.length-1],F=y.blockers.get(R);if(!(F&&F.state==="proceeding")&&L({currentLocation:s,nextLocation:d,historyAction:m}))return R}function St(s){let d=ue(404,{pathname:s}),m=u||l,{matches:E,route:R}=gr(m);return{notFoundMatches:E,route:R,error:d}}function da(s,d,m){if(b=s,x=d,v=m||null,!C&&y.navigation===Mt){C=!0;let E=er(y.location,y.matches);E!=null&&te({restoreScrollPosition:E})}return()=>{b=null,x=null,v=null}}function Zt(s,d){return v&&v(s,d.map(E=>Fa(E,y.loaderData)))||s.key}function fa(s,d){if(b&&x){let m=Zt(s,d);b[m]=x()}}function er(s,d){if(b){let m=Zt(s,d),E=b[m];if(typeof E=="number")return E}return null}function ot(s,d,m){if(e.patchRoutesOnNavigation)if(s){if(Object.keys(s[0].params).length>0)return{active:!0,matches:dt(d,m,i,!0)}}else return{active:!0,matches:dt(d,m,i,!0)||[]};return{active:!1,matches:null}}async function it(s,d,m,E){if(!e.patchRoutesOnNavigation)return{type:"success",matches:s};let R=s;for(;;){let L=u==null,F=u||l,S=o;try{await e.patchRoutesOnNavigation({signal:m,path:d,matches:R,fetcherKey:E,patch:(O,M)=>{m.aborted||ur(O,M,F,S,a)}})}catch(O){return{type:"error",error:O,partialMatches:R}}finally{L&&!m.aborted&&(l=[...l])}if(m.aborted)return{type:"aborted"};let D=Re(F,d,i);if(D)return{type:"success",matches:D};let T=dt(F,d,i,!0);if(!T||R.length===T.length&&R.every((O,M)=>O.route.id===T[M].route.id))return{type:"success",matches:null};R=T}}function ha(s){o={},u=mt(s,a,void 0,o)}function ma(s,d){let m=u==null;ur(s,d,u||l,o,a),m&&(l=[...l],te({}))}return U={get basename(){return i},get future(){return f},get state(){return y},get routes(){return l},get window(){return t},initialize:Zr,subscribe:ta,enableScrollRestoration:da,navigate:Wt,fetch:ia,revalidate:ra,createHref:s=>e.history.createHref(s),encodeLocation:s=>e.history.encodeLocation(s),getFetcher:Yt,deleteFetcher:ua,dispose:ea,getBlocker:ca,deleteBlocker:Xt,patchRoutes:ma,_internalFetchControllers:B,_internalSetRoutes:ha},U}function an(e){return e!=null&&("formData"in e&&e.formData!=null||"body"in e&&e.body!==void 0)}function Ot(e,t,r,n,a,o){let l,u;if(a){l=[];for(let c of t)if(l.push(c),c.route.id===a){u=c;break}}else l=t,u=t[t.length-1];let i=wt(n||".",gt(l),ce(e.pathname,r)||e.pathname,o==="path");if(n==null&&(i.search=e.search,i.hash=e.hash),(n==null||n===""||n===".")&&u){let c=_t(i.search);if(u.route.index&&!c)i.search=i.search?i.search.replace(/^\?/,"?index&"):"?index";else if(!u.route.index&&c){let f=new URLSearchParams(i.search),g=f.getAll("index");f.delete("index"),g.filter(b=>b).forEach(b=>f.append("index",b));let h=f.toString();i.search=h?`?${h}`:""}}return r!=="/"&&(i.pathname=i.pathname==="/"?r:ye([r,i.pathname])),xe(i)}function lr(e,t,r){if(!r||!an(r))return{path:t};if(r.formMethod&&!yn(r.formMethod))return{path:t,error:ue(405,{method:r.formMethod})};let n=()=>({path:t,error:ue(400,{type:"invalid-body"})}),o=(r.formMethod||"get").toUpperCase(),l=zr(t);if(r.body!==void 0){if(r.formEncType==="text/plain"){if(!ie(o))return n();let g=typeof r.body=="string"?r.body:r.body instanceof FormData||r.body instanceof URLSearchParams?Array.from(r.body.entries()).reduce((h,[b,v])=>`${h}${b}=${v}
`,""):String(r.body);return{path:t,submission:{formMethod:o,formAction:l,formEncType:r.formEncType,formData:void 0,json:void 0,text:g}}}else if(r.formEncType==="application/json"){if(!ie(o))return n();try{let g=typeof r.body=="string"?JSON.parse(r.body):r.body;return{path:t,submission:{formMethod:o,formAction:l,formEncType:r.formEncType,formData:void 0,json:g,text:void 0}}}catch{return n()}}}_(typeof FormData=="function","FormData is not available in this environment");let u,i;if(r.formData)u=kt(r.formData),i=r.formData;else if(r.body instanceof FormData)u=kt(r.body),i=r.body;else if(r.body instanceof URLSearchParams)u=r.body,i=mr(u);else if(r.body==null)u=new URLSearchParams,i=new FormData;else try{u=new URLSearchParams(r.body),i=mr(u)}catch{return n()}let c={formMethod:o,formAction:l,formEncType:r&&r.formEncType||"application/x-www-form-urlencoded",formData:i,json:void 0,text:void 0};if(ie(c.formMethod))return{path:t,submission:c};let f=Se(t);return e&&f.search&&_t(f.search)&&u.append("index",""),f.search=`?${u}`,{path:xe(f),submission:c}}function sr(e,t,r,n,a,o,l,u,i,c,f,g,h,b,v,x,C,w,$,P){var Le;let k=P?oe(P[1])?P[1].error:P[1].data:void 0,U=a.createURL(o.location),y=a.createURL(i),j;if(f&&o.errors){let H=Object.keys(o.errors)[0];j=l.findIndex(Y=>Y.route.id===H)}else if(P&&oe(P[1])){let H=P[0];j=l.findIndex(Y=>Y.route.id===H)-1}let V=P?P[1].statusCode:void 0,A=V&&V>=400,G={currentUrl:U,currentParams:((Le=o.matches[0])==null?void 0:Le.params)||{},nextUrl:y,nextParams:l[0].params,...u,actionResult:k,actionStatus:V},de=l.map((H,Y)=>{let{route:B}=H,Q=null;if(j!=null&&Y>j?Q=!1:B.lazy?Q=!0:B.loader==null?Q=!1:f?Q=$t(B,o.loaderData,o.errors):nn(o.loaderData,o.matches[Y],H)&&(Q=!0),Q!==null)return Nt(r,n,e,H,c,t,Q);let ae=A?!1:g||U.pathname+U.search===y.pathname+y.search||U.search!==y.search||on(o.matches[Y],H),Z={...G,defaultShouldRevalidate:ae},re=vt(H,Z);return Nt(r,n,e,H,c,t,re,Z)}),Pe=[];return v.forEach((H,Y)=>{if(f||!l.some(ee=>ee.route.id===H.routeId)||b.has(Y))return;let B=o.fetchers.get(Y),Q=B&&B.state!=="idle"&&B.data===void 0,ae=Re(C,H.path,w);if(!ae){if($&&Q)return;Pe.push({key:Y,routeId:H.routeId,path:H.path,matches:null,match:null,request:null,controller:null});return}if(x.has(Y))return;let Z=Qe(ae,H.path),re=new AbortController,fe=ze(a,H.path,re.signal),he=null;if(h.has(Y))h.delete(Y),he=Ie(r,n,fe,ae,Z,c,t);else if(Q)g&&(he=Ie(r,n,fe,ae,Z,c,t));else{let ee={...G,defaultShouldRevalidate:A?!1:g};vt(Z,ee)&&(he=Ie(r,n,fe,ae,Z,c,t,ee))}he&&Pe.push({key:Y,routeId:H.routeId,path:H.path,matches:he,match:Z,request:fe,controller:re})}),{dsMatches:de,revalidatingFetchers:Pe}}function $t(e,t,r){if(e.lazy)return!0;if(!e.loader)return!1;let n=t!=null&&e.id in t,a=r!=null&&r[e.id]!==void 0;return!n&&a?!1:typeof e.loader=="function"&&e.loader.hydrate===!0?!0:!n&&!a}function nn(e,t,r){let n=!t||r.route.id!==t.route.id,a=!e.hasOwnProperty(r.route.id);return n||a}function on(e,t){let r=e.route.path;return e.pathname!==t.pathname||r!=null&&r.endsWith("*")&&e.params["*"]!==t.params["*"]}function vt(e,t){if(e.route.shouldRevalidate){let r=e.route.shouldRevalidate(t);if(typeof r=="boolean")return r}return t.defaultShouldRevalidate}function ur(e,t,r,n,a){let o;if(e){let i=n[e];_(i,`No route found to patch children into: routeId = ${e}`),i.children||(i.children=[]),o=i.children}else o=r;let l=t.filter(i=>!o.some(c=>Nr(i,c))),u=mt(l,a,[e||"_","patch",String((o==null?void 0:o.length)||"0")],n);o.push(...u)}function Nr(e,t){return"id"in e&&"id"in t&&e.id===t.id?!0:e.index===t.index&&e.path===t.path&&e.caseSensitive===t.caseSensitive?(!e.children||e.children.length===0)&&(!t.children||t.children.length===0)?!0:e.children.every((r,n)=>{var a;return(a=t.children)==null?void 0:a.some(o=>Nr(r,o))}):!1}var cr=new WeakMap,kr=({key:e,route:t,manifest:r,mapRouteProperties:n})=>{let a=r[t.id];if(_(a,"No route found in manifest"),!a.lazy||typeof a.lazy!="object")return;let o=a.lazy[e];if(!o)return;let l=cr.get(a);l||(l={},cr.set(a,l));let u=l[e];if(u)return u;let i=(async()=>{let c=La(e),g=a[e]!==void 0&&e!=="hasErrorBoundary";if(c)K(!c,"Route property "+e+" is not a supported lazy route property. This property will be ignored."),l[e]=Promise.resolve();else if(g)K(!1,`Route "${a.id}" has a static property "${e}" defined. The lazy property will be ignored.`);else{let h=await o();h!=null&&(Object.assign(a,{[e]:h}),Object.assign(a,n(a)))}typeof a.lazy=="object"&&(a.lazy[e]=void 0,Object.values(a.lazy).every(h=>h===void 0)&&(a.lazy=void 0))})();return l[e]=i,i},dr=new WeakMap;function ln(e,t,r,n,a){let o=r[e.id];if(_(o,"No route found in manifest"),!e.lazy)return{lazyRoutePromise:void 0,lazyHandlerPromise:void 0};if(typeof e.lazy=="function"){let f=dr.get(o);if(f)return{lazyRoutePromise:f,lazyHandlerPromise:f};let g=(async()=>{_(typeof e.lazy=="function","No lazy route function found");let h=await e.lazy(),b={};for(let v in h){let x=h[v];if(x===void 0)continue;let C=Ma(v),$=o[v]!==void 0&&v!=="hasErrorBoundary";C?K(!C,"Route property "+v+" is not a supported property to be returned from a lazy route function. This property will be ignored."):$?K(!$,`Route "${o.id}" has a static property "${v}" defined but its lazy function is also returning a value for this property. The lazy route property "${v}" will be ignored.`):b[v]=x}Object.assign(o,b),Object.assign(o,{...n(o),lazy:void 0})})();return dr.set(o,g),g.catch(()=>{}),{lazyRoutePromise:g,lazyHandlerPromise:g}}let l=Object.keys(e.lazy),u=[],i;for(let f of l){if(a&&a.includes(f))continue;let g=kr({key:f,route:e,manifest:r,mapRouteProperties:n});g&&(u.push(g),f===t&&(i=g))}let c=u.length>0?Promise.all(u).then(()=>{}):void 0;return c==null||c.catch(()=>{}),i==null||i.catch(()=>{}),{lazyRoutePromise:c,lazyHandlerPromise:i}}async function fr(e){let t=e.matches.filter(a=>a.shouldLoad),r={};return(await Promise.all(t.map(a=>a.resolve()))).forEach((a,o)=>{r[t[o].route.id]=a}),r}async function sn(e){return e.matches.some(t=>t.route.unstable_middleware)?Ur(e,!1,()=>fr(e),(t,r)=>({[r]:{type:"error",result:t}})):fr(e)}async function Ur(e,t,r,n){let{matches:a,request:o,params:l,context:u}=e,i={handlerResult:void 0};try{let c=a.flatMap(g=>g.route.unstable_middleware?g.route.unstable_middleware.map(h=>[g.route.id,h]):[]),f=await _r({request:o,params:l,context:u},c,t,i,r);return t?f:i.handlerResult}catch(c){if(!i.middlewareError)throw c;let f=await n(i.middlewareError.error,i.middlewareError.routeId);return i.handlerResult?Object.assign(i.handlerResult,f):f}}async function _r(e,t,r,n,a,o=0){let{request:l}=e;if(l.signal.aborted)throw l.signal.reason?l.signal.reason:new Error(`Request aborted without an \`AbortSignal.reason\`: ${l.method} ${l.url}`);let u=t[o];if(!u)return n.handlerResult=await a(),n.handlerResult;let[i,c]=u,f=!1,g,h=async()=>{if(f)throw new Error("You may only call `next()` once per middleware");f=!0,await _r(e,t,r,n,a,o+1)};try{let b=await c({request:e.request,params:e.params,context:e.context},h);return f?b===void 0?g:b:h()}catch(b){throw n.middlewareError?n.middlewareError.error!==b&&(n.middlewareError={routeId:i,error:b}):n.middlewareError={routeId:i,error:b},b}}function Ar(e,t,r,n,a){let o=kr({key:"unstable_middleware",route:n.route,manifest:t,mapRouteProperties:e}),l=ln(n.route,ie(r.method)?"action":"loader",t,e,a);return{middleware:o,route:l.lazyRoutePromise,handler:l.lazyHandlerPromise}}function Nt(e,t,r,n,a,o,l,u=null){let i=!1,c=Ar(e,t,r,n,a);return{...n,_lazyPromises:c,shouldLoad:l,unstable_shouldRevalidateArgs:u,unstable_shouldCallHandler(f){return i=!0,u?typeof f=="boolean"?vt(n,{...u,defaultShouldRevalidate:f}):vt(n,u):l},resolve(f){return i||l||f&&r.method==="GET"&&(n.route.lazy||n.route.loader)?cn({request:r,match:n,lazyHandlerPromise:c==null?void 0:c.handler,lazyRoutePromise:c==null?void 0:c.route,handlerOverride:f,scopedContext:o}):Promise.resolve({type:"data",result:void 0})}}}function Ie(e,t,r,n,a,o,l,u=null){return n.map(i=>i.route.id!==a.route.id?{...i,shouldLoad:!1,unstable_shouldRevalidateArgs:u,unstable_shouldCallHandler:()=>!1,_lazyPromises:Ar(e,t,r,i,o),resolve:()=>Promise.resolve({type:"data",result:void 0})}:Nt(e,t,r,i,o,l,!0,u))}async function un(e,t,r,n,a,o){r.some(c=>{var f;return(f=c._lazyPromises)==null?void 0:f.middleware})&&await Promise.all(r.map(c=>{var f;return(f=c._lazyPromises)==null?void 0:f.middleware}));let l={request:t,params:r[0].params,context:a,matches:r},i=await e({...l,fetcherKey:n,unstable_runClientMiddleware:c=>{let f=l;return Ur(f,!1,()=>c({...f,fetcherKey:n,unstable_runClientMiddleware:()=>{throw new Error("Cannot call `unstable_runClientMiddleware()` from within an `unstable_runClientMiddleware` handler")}}),(g,h)=>({[h]:{type:"error",result:g}}))}});try{await Promise.all(r.flatMap(c=>{var f,g;return[(f=c._lazyPromises)==null?void 0:f.handler,(g=c._lazyPromises)==null?void 0:g.route]}))}catch{}return i}async function cn({request:e,match:t,lazyHandlerPromise:r,lazyRoutePromise:n,handlerOverride:a,scopedContext:o}){let l,u,i=ie(e.method),c=i?"action":"loader",f=g=>{let h,b=new Promise((C,w)=>h=w);u=()=>h(),e.signal.addEventListener("abort",u);let v=C=>typeof g!="function"?Promise.reject(new Error(`You cannot call the handler for a route which defines a boolean "${c}" [routeId: ${t.route.id}]`)):g({request:e,params:t.params,context:o},...C!==void 0?[C]:[]),x=(async()=>{try{return{type:"data",result:await(a?a(w=>v(w)):v())}}catch(C){return{type:"error",result:C}}})();return Promise.race([x,b])};try{let g=i?t.route.action:t.route.loader;if(r||n)if(g){let h,[b]=await Promise.all([f(g).catch(v=>{h=v}),r,n]);if(h!==void 0)throw h;l=b}else{await r;let h=i?t.route.action:t.route.loader;if(h)[l]=await Promise.all([f(h),n]);else if(c==="action"){let b=new URL(e.url),v=b.pathname+b.search;throw ue(405,{method:e.method,pathname:v,routeId:t.route.id})}else return{type:"data",result:void 0}}else if(g)l=await f(g);else{let h=new URL(e.url),b=h.pathname+h.search;throw ue(404,{pathname:b})}}catch(g){return{type:"error",result:g}}finally{u&&e.signal.removeEventListener("abort",u)}return l}async function dn(e){var n,a,o,l,u,i;let{result:t,type:r}=e;if(Ir(t)){let c;try{let f=t.headers.get("Content-Type");f&&/\bapplication\/json\b/.test(f)?t.body==null?c=null:c=await t.json():c=await t.text()}catch(f){return{type:"error",error:f}}return r==="error"?{type:"error",error:new yt(t.status,t.statusText,c),statusCode:t.status,headers:t.headers}:{type:"data",data:c,statusCode:t.status,headers:t.headers}}return r==="error"?wr(t)?t.data instanceof Error?{type:"error",error:t.data,statusCode:(n=t.init)==null?void 0:n.status,headers:(a=t.init)!=null&&a.headers?new Headers(t.init.headers):void 0}:{type:"error",error:new yt(((o=t.init)==null?void 0:o.status)||500,void 0,t.data),statusCode:et(t)?t.status:void 0,headers:(l=t.init)!=null&&l.headers?new Headers(t.init.headers):void 0}:{type:"error",error:t,statusCode:et(t)?t.status:void 0}:wr(t)?{type:"data",data:t.data,statusCode:(u=t.init)==null?void 0:u.status,headers:(i=t.init)!=null&&i.headers?new Headers(t.init.headers):void 0}:{type:"data",data:t}}function fn(e,t,r,n,a){let o=e.headers.get("Location");if(_(o,"Redirects returned/thrown from loaders/actions must have a Location header"),!Ut.test(o)){let l=n.slice(0,n.findIndex(u=>u.route.id===r)+1);o=Ot(new URL(t.url),l,a,o),e.headers.set("Location",o)}return e}function hr(e,t,r){if(Ut.test(e)){let n=e,a=n.startsWith("//")?new URL(t.protocol+n):new URL(n),o=ce(a.pathname,r)!=null;if(a.origin===t.origin&&o)return a.pathname+a.search+a.hash}return e}function ze(e,t,r,n){let a=e.createURL(zr(t)).toString(),o={signal:r};if(n&&ie(n.formMethod)){let{formMethod:l,formEncType:u}=n;o.method=l.toUpperCase(),u==="application/json"?(o.headers=new Headers({"Content-Type":u}),o.body=JSON.stringify(n.json)):u==="text/plain"?o.body=n.text:u==="application/x-www-form-urlencoded"&&n.formData?o.body=kt(n.formData):o.body=n.formData}return new Request(a,o)}function kt(e){let t=new URLSearchParams;for(let[r,n]of e.entries())t.append(r,typeof n=="string"?n:n.name);return t}function mr(e){let t=new FormData;for(let[r,n]of e.entries())t.append(r,n);return t}function hn(e,t,r,n=!1,a=!1){let o={},l=null,u,i=!1,c={},f=r&&oe(r[1])?r[1].error:void 0;return e.forEach(g=>{if(!(g.route.id in t))return;let h=g.route.id,b=t[h];if(_(!ke(b),"Cannot handle redirect results in processLoaderData"),oe(b)){let v=b.error;if(f!==void 0&&(v=f,f=void 0),l=l||{},a)l[h]=v;else{let x=Ne(e,h);l[x.route.id]==null&&(l[x.route.id]=v)}n||(o[h]=$r),i||(i=!0,u=et(b.error)?b.error.status:500),b.headers&&(c[h]=b.headers)}else o[h]=b.data,b.statusCode&&b.statusCode!==200&&!i&&(u=b.statusCode),b.headers&&(c[h]=b.headers)}),f!==void 0&&r&&(l={[r[0]]:f},r[2]&&(o[r[2]]=void 0)),{loaderData:o,errors:l,statusCode:u||200,loaderHeaders:c}}function pr(e,t,r,n,a,o){let{loaderData:l,errors:u}=hn(t,r,n);return a.filter(i=>!i.matches||i.matches.some(c=>c.shouldLoad)).forEach(i=>{let{key:c,match:f,controller:g}=i,h=o[c];if(_(h,"Did not find corresponding fetcher result"),!(g&&g.signal.aborted))if(oe(h)){let b=Ne(e.matches,f==null?void 0:f.route.id);u&&u[b.route.id]||(u={...u,[b.route.id]:h.error}),e.fetchers.delete(c)}else if(ke(h))_(!1,"Unhandled fetcher revalidation redirect");else{let b=Ee(h.data);e.fetchers.set(c,b)}}),{loaderData:l,errors:u}}function yr(e,t,r,n){let a=Object.entries(t).filter(([,o])=>o!==$r).reduce((o,[l,u])=>(o[l]=u,o),{});for(let o of r){let l=o.route.id;if(!t.hasOwnProperty(l)&&e.hasOwnProperty(l)&&o.route.loader&&(a[l]=e[l]),n&&n.hasOwnProperty(l))break}return a}function vr(e){return e?oe(e[1])?{actionData:{}}:{actionData:{[e[0]]:e[1].data}}:{}}function Ne(e,t){return(t?e.slice(0,e.findIndex(n=>n.route.id===t)+1):[...e]).reverse().find(n=>n.route.hasErrorBoundary===!0)||e[0]}function gr(e){let t=e.length===1?e[0]:e.find(r=>r.index||!r.path||r.path==="/")||{id:"__shim-error-route__"};return{matches:[{params:{},pathname:"",pathnameBase:"",route:t}],route:t}}function ue(e,{pathname:t,routeId:r,method:n,type:a,message:o}={}){let l="Unknown Server Error",u="Unknown @remix-run/router error";return e===400?(l="Bad Request",n&&t&&r?u=`You made a ${n} request to "${t}" but did not provide a \`loader\` for route "${r}", so there is no way to handle the request.`:a==="invalid-body"&&(u="Unable to encode submission body")):e===403?(l="Forbidden",u=`Route "${r}" does not match URL "${t}"`):e===404?(l="Not Found",u=`No route matches URL "${t}"`):e===405&&(l="Method Not Allowed",n&&t&&r?u=`You made a ${n.toUpperCase()} request to "${t}" but did not provide an \`action\` for route "${r}", so there is no way to handle the request.`:n&&(u=`Invalid request method "${n.toUpperCase()}"`)),new yt(e||500,l,new Error(u),!0)}function ut(e){let t=Object.entries(e);for(let r=t.length-1;r>=0;r--){let[n,a]=t[r];if(ke(a))return{key:n,result:a}}}function zr(e){let t=typeof e=="string"?Se(e):e;return xe({...t,hash:""})}function mn(e,t){return e.pathname!==t.pathname||e.search!==t.search?!1:e.hash===""?t.hash!=="":e.hash===t.hash?!0:t.hash!==""}function pn(e){return Ir(e.result)&&Qa.has(e.result.status)}function oe(e){return e.type==="error"}function ke(e){return(e&&e.type)==="redirect"}function wr(e){return typeof e=="object"&&e!=null&&"type"in e&&"data"in e&&"init"in e&&e.type==="DataWithResponseInit"}function Ir(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.headers=="object"&&typeof e.body<"u"}function yn(e){return Xa.has(e.toUpperCase())}function ie(e){return qa.has(e.toUpperCase())}function _t(e){return new URLSearchParams(e).getAll("index").some(t=>t==="")}function Qe(e,t){let r=typeof t=="string"?Se(t).search:t.search;if(e[e.length-1].route.index&&_t(r||""))return e[e.length-1];let n=Tr(e);return n[n.length-1]}function br(e){let{formMethod:t,formAction:r,formEncType:n,text:a,formData:o,json:l}=e;if(!(!t||!r||!n)){if(a!=null)return{formMethod:t,formAction:r,formEncType:n,formData:void 0,json:void 0,text:a};if(o!=null)return{formMethod:t,formAction:r,formEncType:n,formData:o,json:void 0,text:void 0};if(l!==void 0)return{formMethod:t,formAction:r,formEncType:n,formData:void 0,json:l,text:void 0}}}function Tt(e,t){return t?{state:"loading",location:e,formMethod:t.formMethod,formAction:t.formAction,formEncType:t.formEncType,formData:t.formData,json:t.json,text:t.text}:{state:"loading",location:e,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0}}function vn(e,t){return{state:"submitting",location:e,formMethod:t.formMethod,formAction:t.formAction,formEncType:t.formEncType,formData:t.formData,json:t.json,text:t.text}}function qe(e,t){return e?{state:"loading",formMethod:e.formMethod,formAction:e.formAction,formEncType:e.formEncType,formData:e.formData,json:e.json,text:e.text,data:t}:{state:"loading",formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0,data:t}}function gn(e,t){return{state:"submitting",formMethod:e.formMethod,formAction:e.formAction,formEncType:e.formEncType,formData:e.formData,json:e.json,text:e.text,data:t?t.data:void 0}}function Ee(e){return{state:"idle",formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0,data:e}}function wn(e,t){try{let r=e.sessionStorage.getItem(Or);if(r){let n=JSON.parse(r);for(let[a,o]of Object.entries(n||{}))o&&Array.isArray(o)&&t.set(a,new Set(o||[]))}}catch{}}function bn(e,t){if(t.size>0){let r={};for(let[n,a]of t)r[n]=[...a];try{e.sessionStorage.setItem(Or,JSON.stringify(r))}catch(n){K(!1,`Failed to save applied view transitions in sessionStorage (${n}).`)}}}function En(){let e,t,r=new Promise((n,a)=>{e=async o=>{n(o);try{await r}catch{}},t=async o=>{a(o);try{await r}catch{}}});return{promise:r,resolve:e,reject:t}}var Ue=p.createContext(null);Ue.displayName="DataRouter";var tt=p.createContext(null);tt.displayName="DataRouterState";var At=p.createContext({isTransitioning:!1});At.displayName="ViewTransition";var jr=p.createContext(new Map);jr.displayName="Fetchers";var Rn=p.createContext(null);Rn.displayName="Await";var me=p.createContext(null);me.displayName="Navigation";var bt=p.createContext(null);bt.displayName="Location";var pe=p.createContext({outlet:null,matches:[],isDataRoute:!1});pe.displayName="Route";var zt=p.createContext(null);zt.displayName="RouteError";function xn(e,{relative:t}={}){_(je(),"useHref() may be used only in the context of a <Router> component.");let{basename:r,navigator:n}=p.useContext(me),{hash:a,pathname:o,search:l}=rt(e,{relative:t}),u=o;return r!=="/"&&(u=o==="/"?r:ye([r,o])),n.createHref({pathname:u,search:l,hash:a})}function je(){return p.useContext(bt)!=null}function Ce(){return _(je(),"useLocation() may be used only in the context of a <Router> component."),p.useContext(bt).location}var Hr="You should call navigate() in a React.useEffect(), not when your component is first rendered.";function Br(e){p.useContext(me).static||p.useLayoutEffect(e)}function Wr(){let{isDataRoute:e}=p.useContext(pe);return e?An():Sn()}function Sn(){_(je(),"useNavigate() may be used only in the context of a <Router> component.");let e=p.useContext(Ue),{basename:t,navigator:r}=p.useContext(me),{matches:n}=p.useContext(pe),{pathname:a}=Ce(),o=JSON.stringify(gt(n)),l=p.useRef(!1);return Br(()=>{l.current=!0}),p.useCallback((i,c={})=>{if(K(l.current,Hr),!l.current)return;if(typeof i=="number"){r.go(i);return}let f=wt(i,JSON.parse(o),a,c.relative==="path");e==null&&t!=="/"&&(f.pathname=f.pathname==="/"?t:ye([t,f.pathname])),(c.replace?r.replace:r.push)(f,c.state,c)},[t,r,o,a,e])}var Cn=p.createContext(null);function Pn(e){let t=p.useContext(pe).outlet;return t&&p.createElement(Cn.Provider,{value:e},t)}function rt(e,{relative:t}={}){let{matches:r}=p.useContext(pe),{pathname:n}=Ce(),a=JSON.stringify(gt(r));return p.useMemo(()=>wt(e,JSON.parse(a),n,t==="path"),[e,a,n,t])}function Ln(e,t,r,n){_(je(),"useRoutes() may be used only in the context of a <Router> component.");let{navigator:a}=p.useContext(me),{matches:o}=p.useContext(pe),l=o[o.length-1],u=l?l.params:{},i=l?l.pathname:"/",c=l?l.pathnameBase:"/",f=l&&l.route;{let w=f&&f.path||"";Vr(i,!f||w.endsWith("*")||w.endsWith("*?"),`You rendered descendant <Routes> (or called \`useRoutes()\`) at "${i}" (under <Route path="${w}">) but the parent route path has no trailing "*". This means if you navigate deeper, the parent won't match anymore and therefore the child routes will never render.

Please change the parent <Route path="${w}"> to <Route path="${w==="/"?"*":`${w}/*`}">.`)}let g=Ce(),h;h=g;let b=h.pathname||"/",v=b;if(c!=="/"){let w=c.replace(/^\//,"").split("/");v="/"+b.replace(/^\//,"").split("/").slice(w.length).join("/")}let x=Re(e,{pathname:v});return K(f||x!=null,`No routes matched location "${h.pathname}${h.search}${h.hash}" `),K(x==null||x[x.length-1].route.element!==void 0||x[x.length-1].route.Component!==void 0||x[x.length-1].route.lazy!==void 0,`Matched leaf route at location "${h.pathname}${h.search}${h.hash}" does not have an element or Component. This means it will render an <Outlet /> with a null value by default resulting in an "empty" page.`),On(x&&x.map(w=>Object.assign({},w,{params:Object.assign({},u,w.params),pathname:ye([c,a.encodeLocation?a.encodeLocation(w.pathname).pathname:w.pathname]),pathnameBase:w.pathnameBase==="/"?c:ye([c,a.encodeLocation?a.encodeLocation(w.pathnameBase).pathname:w.pathnameBase])})),o,r,n)}function Dn(){let e=_n(),t=et(e)?`${e.status} ${e.statusText}`:e instanceof Error?e.message:JSON.stringify(e),r=e instanceof Error?e.stack:null,n="rgba(200,200,200, 0.5)",a={padding:"0.5rem",backgroundColor:n},o={padding:"2px 4px",backgroundColor:n},l=null;return console.error("Error handled by React Router default ErrorBoundary:",e),l=p.createElement(p.Fragment,null,p.createElement("p",null,"💿 Hey developer 👋"),p.createElement("p",null,"You can provide a way better UX than this when your app throws errors by providing your own ",p.createElement("code",{style:o},"ErrorBoundary")," or"," ",p.createElement("code",{style:o},"errorElement")," prop on your route.")),p.createElement(p.Fragment,null,p.createElement("h2",null,"Unexpected Application Error!"),p.createElement("h3",{style:{fontStyle:"italic"}},t),r?p.createElement("pre",{style:a},r):null,l)}var Mn=p.createElement(Dn,null),Tn=class extends p.Component{constructor(e){super(e),this.state={location:e.location,revalidation:e.revalidation,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,t){return t.location!==e.location||t.revalidation!=="idle"&&e.revalidation==="idle"?{error:e.error,location:e.location,revalidation:e.revalidation}:{error:e.error!==void 0?e.error:t.error,location:t.location,revalidation:e.revalidation||t.revalidation}}componentDidCatch(e,t){console.error("React Router caught the following error during render",e,t)}render(){return this.state.error!==void 0?p.createElement(pe.Provider,{value:this.props.routeContext},p.createElement(zt.Provider,{value:this.state.error,children:this.props.component})):this.props.children}};function Fn({routeContext:e,match:t,children:r}){let n=p.useContext(Ue);return n&&n.static&&n.staticContext&&(t.route.errorElement||t.route.ErrorBoundary)&&(n.staticContext._deepestRenderedBoundaryId=t.route.id),p.createElement(pe.Provider,{value:e},r)}function On(e,t=[],r=null,n=null){if(e==null){if(!r)return null;if(r.errors)e=r.matches;else if(t.length===0&&!r.initialized&&r.matches.length>0)e=r.matches;else return null}let a=e,o=r==null?void 0:r.errors;if(o!=null){let i=a.findIndex(c=>c.route.id&&(o==null?void 0:o[c.route.id])!==void 0);_(i>=0,`Could not find a matching route for errors on route IDs: ${Object.keys(o).join(",")}`),a=a.slice(0,Math.min(a.length,i+1))}let l=!1,u=-1;if(r)for(let i=0;i<a.length;i++){let c=a[i];if((c.route.HydrateFallback||c.route.hydrateFallbackElement)&&(u=i),c.route.id){let{loaderData:f,errors:g}=r,h=c.route.loader&&!f.hasOwnProperty(c.route.id)&&(!g||g[c.route.id]===void 0);if(c.route.lazy||h){l=!0,u>=0?a=a.slice(0,u+1):a=[a[0]];break}}}return a.reduceRight((i,c,f)=>{let g,h=!1,b=null,v=null;r&&(g=o&&c.route.id?o[c.route.id]:void 0,b=c.route.errorElement||Mn,l&&(u<0&&f===0?(Vr("route-fallback",!1,"No `HydrateFallback` element provided to render during initial hydration"),h=!0,v=null):u===f&&(h=!0,v=c.route.hydrateFallbackElement||null)));let x=t.concat(a.slice(0,f+1)),C=()=>{let w;return g?w=b:h?w=v:c.route.Component?w=p.createElement(c.route.Component,null):c.route.element?w=c.route.element:w=i,p.createElement(Fn,{match:c,routeContext:{outlet:i,matches:x,isDataRoute:r!=null},children:w})};return r&&(c.route.ErrorBoundary||c.route.errorElement||f===0)?p.createElement(Tn,{location:r.location,revalidation:r.revalidation,component:b,error:g,children:C(),routeContext:{outlet:null,matches:x,isDataRoute:!0}}):C()},null)}function It(e){return`${e} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function $n(e){let t=p.useContext(Ue);return _(t,It(e)),t}function Nn(e){let t=p.useContext(tt);return _(t,It(e)),t}function kn(e){let t=p.useContext(pe);return _(t,It(e)),t}function jt(e){let t=kn(e),r=t.matches[t.matches.length-1];return _(r.route.id,`${e} can only be used on routes that contain a unique "id"`),r.route.id}function Un(){return jt("useRouteId")}function _n(){var n;let e=p.useContext(zt),t=Nn("useRouteError"),r=jt("useRouteError");return e!==void 0?e:(n=t.errors)==null?void 0:n[r]}function An(){let{router:e}=$n("useNavigate"),t=jt("useNavigate"),r=p.useRef(!1);return Br(()=>{r.current=!0}),p.useCallback(async(a,o={})=>{K(r.current,Hr),r.current&&(typeof a=="number"?e.navigate(a):await e.navigate(a,{fromRouteId:t,...o}))},[e,t])}var Er={};function Vr(e,t,r){!t&&!Er[e]&&(Er[e]=!0,K(!1,r))}var Rr={};function xr(e,t){!e&&!Rr[t]&&(Rr[t]=!0,console.warn(t))}function zn(e){let t={hasErrorBoundary:e.hasErrorBoundary||e.ErrorBoundary!=null||e.errorElement!=null};return e.Component&&(e.element&&K(!1,"You should not include both `Component` and `element` on your route - `Component` will be used."),Object.assign(t,{element:p.createElement(e.Component),Component:void 0})),e.HydrateFallback&&(e.hydrateFallbackElement&&K(!1,"You should not include both `HydrateFallback` and `hydrateFallbackElement` on your route - `HydrateFallback` will be used."),Object.assign(t,{hydrateFallbackElement:p.createElement(e.HydrateFallback),HydrateFallback:void 0})),e.ErrorBoundary&&(e.errorElement&&K(!1,"You should not include both `ErrorBoundary` and `errorElement` on your route - `ErrorBoundary` will be used."),Object.assign(t,{errorElement:p.createElement(e.ErrorBoundary),ErrorBoundary:void 0})),t}var In=["HydrateFallback","hydrateFallbackElement"],jn=class{constructor(){this.status="pending",this.promise=new Promise((e,t)=>{this.resolve=r=>{this.status==="pending"&&(this.status="resolved",e(r))},this.reject=r=>{this.status==="pending"&&(this.status="rejected",t(r))}})}};function Hn({router:e,flushSync:t}){let[r,n]=p.useState(e.state),[a,o]=p.useState(),[l,u]=p.useState({isTransitioning:!1}),[i,c]=p.useState(),[f,g]=p.useState(),[h,b]=p.useState(),v=p.useRef(new Map),x=p.useCallback((P,{deletedFetchers:k,flushSync:U,viewTransitionOpts:y})=>{P.fetchers.forEach((V,A)=>{V.data!==void 0&&v.current.set(A,V.data)}),k.forEach(V=>v.current.delete(V)),xr(U===!1||t!=null,'You provided the `flushSync` option to a router update, but you are not using the `<RouterProvider>` from `react-router/dom` so `ReactDOM.flushSync()` is unavailable.  Please update your app to `import { RouterProvider } from "react-router/dom"` and ensure you have `react-dom` installed as a dependency to use the `flushSync` option.');let j=e.window!=null&&e.window.document!=null&&typeof e.window.document.startViewTransition=="function";if(xr(y==null||j,"You provided the `viewTransition` option to a router update, but you do not appear to be running in a DOM environment as `window.startViewTransition` is not available."),!y||!j){t&&U?t(()=>n(P)):p.startTransition(()=>n(P));return}if(t&&U){t(()=>{f&&(i&&i.resolve(),f.skipTransition()),u({isTransitioning:!0,flushSync:!0,currentLocation:y.currentLocation,nextLocation:y.nextLocation})});let V=e.window.document.startViewTransition(()=>{t(()=>n(P))});V.finished.finally(()=>{t(()=>{c(void 0),g(void 0),o(void 0),u({isTransitioning:!1})})}),t(()=>g(V));return}f?(i&&i.resolve(),f.skipTransition(),b({state:P,currentLocation:y.currentLocation,nextLocation:y.nextLocation})):(o(P),u({isTransitioning:!0,flushSync:!1,currentLocation:y.currentLocation,nextLocation:y.nextLocation}))},[e.window,t,f,i]);p.useLayoutEffect(()=>e.subscribe(x),[e,x]),p.useEffect(()=>{l.isTransitioning&&!l.flushSync&&c(new jn)},[l]),p.useEffect(()=>{if(i&&a&&e.window){let P=a,k=i.promise,U=e.window.document.startViewTransition(async()=>{p.startTransition(()=>n(P)),await k});U.finished.finally(()=>{c(void 0),g(void 0),o(void 0),u({isTransitioning:!1})}),g(U)}},[a,i,e.window]),p.useEffect(()=>{i&&a&&r.location.key===a.location.key&&i.resolve()},[i,f,r.location,a]),p.useEffect(()=>{!l.isTransitioning&&h&&(o(h.state),u({isTransitioning:!0,flushSync:!1,currentLocation:h.currentLocation,nextLocation:h.nextLocation}),b(void 0))},[l.isTransitioning,h]);let C=p.useMemo(()=>({createHref:e.createHref,encodeLocation:e.encodeLocation,go:P=>e.navigate(P),push:(P,k,U)=>e.navigate(P,{state:k,preventScrollReset:U==null?void 0:U.preventScrollReset}),replace:(P,k,U)=>e.navigate(P,{replace:!0,state:k,preventScrollReset:U==null?void 0:U.preventScrollReset})}),[e]),w=e.basename||"/",$=p.useMemo(()=>({router:e,navigator:C,static:!1,basename:w}),[e,C,w]);return p.createElement(p.Fragment,null,p.createElement(Ue.Provider,{value:$},p.createElement(tt.Provider,{value:r},p.createElement(jr.Provider,{value:v.current},p.createElement(At.Provider,{value:l},p.createElement(Vn,{basename:w,location:r.location,navigationType:r.historyAction,navigator:C},p.createElement(Bn,{routes:e.routes,future:e.future,state:r})))))),null)}var Bn=p.memo(Wn);function Wn({routes:e,future:t,state:r}){return Ln(e,void 0,r,t)}function Mo({to:e,replace:t,state:r,relative:n}){_(je(),"<Navigate> may be used only in the context of a <Router> component.");let{static:a}=p.useContext(me);K(!a,"<Navigate> must not be used on the initial render in a <StaticRouter>. This is a no-op, but you should modify your code so the <Navigate> is only ever rendered in response to some user interaction or state change.");let{matches:o}=p.useContext(pe),{pathname:l}=Ce(),u=Wr(),i=wt(e,gt(o),l,n==="path"),c=JSON.stringify(i);return p.useEffect(()=>{u(JSON.parse(c),{replace:t,state:r,relative:n})},[u,c,n,t,r]),null}function To(e){return Pn(e.context)}function Vn({basename:e="/",children:t=null,location:r,navigationType:n="POP",navigator:a,static:o=!1}){_(!je(),"You cannot render a <Router> inside another <Router>. You should never have more than one in your app.");let l=e.replace(/^\/*/,"/"),u=p.useMemo(()=>({basename:l,navigator:a,static:o,future:{}}),[l,a,o]);typeof r=="string"&&(r=Se(r));let{pathname:i="/",search:c="",hash:f="",state:g=null,key:h="default"}=r,b=p.useMemo(()=>{let v=ce(i,l);return v==null?null:{location:{pathname:v,search:c,hash:f,state:g,key:h},navigationType:n}},[l,i,c,f,g,h,n]);return K(b!=null,`<Router basename="${l}"> is not able to match the URL "${i}${c}${f}" because it does not start with the basename, so the <Router> won't render anything.`),b==null?null:p.createElement(me.Provider,{value:u},p.createElement(bt.Provider,{children:t,value:b}))}var ft="get",ht="application/x-www-form-urlencoded";function Et(e){return e!=null&&typeof e.tagName=="string"}function Kn(e){return Et(e)&&e.tagName.toLowerCase()==="button"}function Yn(e){return Et(e)&&e.tagName.toLowerCase()==="form"}function Jn(e){return Et(e)&&e.tagName.toLowerCase()==="input"}function qn(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}function Gn(e,t){return e.button===0&&(!t||t==="_self")&&!qn(e)}var ct=null;function Xn(){if(ct===null)try{new FormData(document.createElement("form"),0),ct=!1}catch{ct=!0}return ct}var Qn=new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);function Ft(e){return e!=null&&!Qn.has(e)?(K(!1,`"${e}" is not a valid \`encType\` for \`<Form>\`/\`<fetcher.Form>\` and will default to "${ht}"`),null):e}function Zn(e,t){let r,n,a,o,l;if(Yn(e)){let u=e.getAttribute("action");n=u?ce(u,t):null,r=e.getAttribute("method")||ft,a=Ft(e.getAttribute("enctype"))||ht,o=new FormData(e)}else if(Kn(e)||Jn(e)&&(e.type==="submit"||e.type==="image")){let u=e.form;if(u==null)throw new Error('Cannot submit a <button> or <input type="submit"> without a <form>');let i=e.getAttribute("formaction")||u.getAttribute("action");if(n=i?ce(i,t):null,r=e.getAttribute("formmethod")||u.getAttribute("method")||ft,a=Ft(e.getAttribute("formenctype"))||Ft(u.getAttribute("enctype"))||ht,o=new FormData(u,e),!Xn()){let{name:c,type:f,value:g}=e;if(f==="image"){let h=c?`${c}.`:"";o.append(`${h}x`,"0"),o.append(`${h}y`,"0")}else c&&o.append(c,g)}}else{if(Et(e))throw new Error('Cannot submit element that is not <form>, <button>, or <input type="submit|image">');r=ft,n=null,a=ht,l=e}return o&&a==="text/plain"&&(l=o,o=void 0),{action:n,method:r.toLowerCase(),encType:a,formData:o,body:l}}function Ht(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}async function eo(e,t){if(e.id in t)return t[e.id];try{let r=await import(e.module);return t[e.id]=r,r}catch(r){return console.error(`Error loading route module \`${e.module}\`, reloading page...`),console.error(r),window.__reactRouterContext&&window.__reactRouterContext.isSpaMode,window.location.reload(),new Promise(()=>{})}}function to(e){return e==null?!1:e.href==null?e.rel==="preload"&&typeof e.imageSrcSet=="string"&&typeof e.imageSizes=="string":typeof e.rel=="string"&&typeof e.href=="string"}async function ro(e,t,r){let n=await Promise.all(e.map(async a=>{let o=t.routes[a.route.id];if(o){let l=await eo(o,r);return l.links?l.links():[]}return[]}));return io(n.flat(1).filter(to).filter(a=>a.rel==="stylesheet"||a.rel==="preload").map(a=>a.rel==="stylesheet"?{...a,rel:"prefetch",as:"style"}:{...a,rel:"prefetch"}))}function Sr(e,t,r,n,a,o){let l=(i,c)=>r[c]?i.route.id!==r[c].route.id:!0,u=(i,c)=>{var f;return r[c].pathname!==i.pathname||((f=r[c].route.path)==null?void 0:f.endsWith("*"))&&r[c].params["*"]!==i.params["*"]};return o==="assets"?t.filter((i,c)=>l(i,c)||u(i,c)):o==="data"?t.filter((i,c)=>{var g;let f=n.routes[i.route.id];if(!f||!f.hasLoader)return!1;if(l(i,c)||u(i,c))return!0;if(i.route.shouldRevalidate){let h=i.route.shouldRevalidate({currentUrl:new URL(a.pathname+a.search+a.hash,window.origin),currentParams:((g=r[0])==null?void 0:g.params)||{},nextUrl:new URL(e,window.origin),nextParams:i.params,defaultShouldRevalidate:!0});if(typeof h=="boolean")return h}return!0}):[]}function ao(e,t,{includeHydrateFallback:r}={}){return no(e.map(n=>{let a=t.routes[n.route.id];if(!a)return[];let o=[a.module];return a.clientActionModule&&(o=o.concat(a.clientActionModule)),a.clientLoaderModule&&(o=o.concat(a.clientLoaderModule)),r&&a.hydrateFallbackModule&&(o=o.concat(a.hydrateFallbackModule)),a.imports&&(o=o.concat(a.imports)),o}).flat(1))}function no(e){return[...new Set(e)]}function oo(e){let t={},r=Object.keys(e).sort();for(let n of r)t[n]=e[n];return t}function io(e,t){let r=new Set;return new Set(t),e.reduce((n,a)=>{let o=JSON.stringify(oo(a));return r.has(o)||(r.add(o),n.push({key:o,link:a})),n},[])}Object.getOwnPropertyNames(Object.prototype).sort().join("\0");var lo=new Set([100,101,204,205]);function so(e,t){let r=typeof e=="string"?new URL(e,typeof window>"u"?"server://singlefetch/":window.location.origin):e;return r.pathname==="/"?r.pathname="_root.data":t&&ce(r.pathname,t)==="/"?r.pathname=`${t.replace(/\/$/,"")}/_root.data`:r.pathname=`${r.pathname.replace(/\/$/,"")}.data`,r}function Kr(){let e=p.useContext(Ue);return Ht(e,"You must render this element inside a <DataRouterContext.Provider> element"),e}function uo(){let e=p.useContext(tt);return Ht(e,"You must render this element inside a <DataRouterStateContext.Provider> element"),e}var Bt=p.createContext(void 0);Bt.displayName="FrameworkContext";function Yr(){let e=p.useContext(Bt);return Ht(e,"You must render this element inside a <HydratedRouter> element"),e}function co(e,t){let r=p.useContext(Bt),[n,a]=p.useState(!1),[o,l]=p.useState(!1),{onFocus:u,onBlur:i,onMouseEnter:c,onMouseLeave:f,onTouchStart:g}=t,h=p.useRef(null);p.useEffect(()=>{if(e==="render"&&l(!0),e==="viewport"){let x=w=>{w.forEach($=>{l($.isIntersecting)})},C=new IntersectionObserver(x,{threshold:.5});return h.current&&C.observe(h.current),()=>{C.disconnect()}}},[e]),p.useEffect(()=>{if(n){let x=setTimeout(()=>{l(!0)},100);return()=>{clearTimeout(x)}}},[n]);let b=()=>{a(!0)},v=()=>{a(!1),l(!1)};return r?e!=="intent"?[o,h,{}]:[o,h,{onFocus:Ge(u,b),onBlur:Ge(i,v),onMouseEnter:Ge(c,b),onMouseLeave:Ge(f,v),onTouchStart:Ge(g,b)}]:[!1,h,{}]}function Ge(e,t){return r=>{e&&e(r),r.defaultPrevented||t(r)}}function fo({page:e,...t}){let{router:r}=Kr(),n=p.useMemo(()=>Re(r.routes,e,r.basename),[r.routes,e,r.basename]);return n?p.createElement(mo,{page:e,matches:n,...t}):null}function ho(e){let{manifest:t,routeModules:r}=Yr(),[n,a]=p.useState([]);return p.useEffect(()=>{let o=!1;return ro(e,t,r).then(l=>{o||a(l)}),()=>{o=!0}},[e,t,r]),n}function mo({page:e,matches:t,...r}){let n=Ce(),{manifest:a,routeModules:o}=Yr(),{basename:l}=Kr(),{loaderData:u,matches:i}=uo(),c=p.useMemo(()=>Sr(e,t,i,a,n,"data"),[e,t,i,a,n]),f=p.useMemo(()=>Sr(e,t,i,a,n,"assets"),[e,t,i,a,n]),g=p.useMemo(()=>{if(e===n.pathname+n.search+n.hash)return[];let v=new Set,x=!1;if(t.forEach(w=>{var P;let $=a.routes[w.route.id];!$||!$.hasLoader||(!c.some(k=>k.route.id===w.route.id)&&w.route.id in u&&((P=o[w.route.id])!=null&&P.shouldRevalidate)||$.hasClientLoader?x=!0:v.add(w.route.id))}),v.size===0)return[];let C=so(e,l);return x&&v.size>0&&C.searchParams.set("_routes",t.filter(w=>v.has(w.route.id)).map(w=>w.route.id).join(",")),[C.pathname+C.search]},[l,u,n,a,c,t,e,o]),h=p.useMemo(()=>ao(f,a),[f,a]),b=ho(f);return p.createElement(p.Fragment,null,g.map(v=>p.createElement("link",{key:v,rel:"prefetch",as:"fetch",href:v,...r})),h.map(v=>p.createElement("link",{key:v,rel:"modulepreload",href:v,...r})),b.map(({key:v,link:x})=>p.createElement("link",{key:v,...x})))}function po(...e){return t=>{e.forEach(r=>{typeof r=="function"?r(t):r!=null&&(r.current=t)})}}var Jr=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u";try{Jr&&(window.__reactRouterVersion="7.6.2")}catch{}function Fo(e,t){return rn({basename:t==null?void 0:t.basename,unstable_getContext:t==null?void 0:t.unstable_getContext,future:t==null?void 0:t.future,history:xa({window:t==null?void 0:t.window}),hydrationData:yo(),routes:e,mapRouteProperties:zn,hydrationRouteProperties:In,dataStrategy:t==null?void 0:t.dataStrategy,patchRoutesOnNavigation:t==null?void 0:t.patchRoutesOnNavigation,window:t==null?void 0:t.window}).initialize()}function yo(){let e=window==null?void 0:window.__staticRouterHydrationData;return e&&e.errors&&(e={...e,errors:vo(e.errors)}),e}function vo(e){if(!e)return null;let t=Object.entries(e),r={};for(let[n,a]of t)if(a&&a.__type==="RouteErrorResponse")r[n]=new yt(a.status,a.statusText,a.data,a.internal===!0);else if(a&&a.__type==="Error"){if(a.__subType){let o=window[a.__subType];if(typeof o=="function")try{let l=new o(a.message);l.stack="",r[n]=l}catch{}}if(r[n]==null){let o=new Error(a.message);o.stack="",r[n]=o}}else r[n]=a;return r}var qr=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,Gr=p.forwardRef(function({onClick:t,discover:r="render",prefetch:n="none",relative:a,reloadDocument:o,replace:l,state:u,target:i,to:c,preventScrollReset:f,viewTransition:g,...h},b){let{basename:v}=p.useContext(me),x=typeof c=="string"&&qr.test(c),C,w=!1;if(typeof c=="string"&&x&&(C=c,Jr))try{let A=new URL(window.location.href),G=c.startsWith("//")?new URL(A.protocol+c):new URL(c),de=ce(G.pathname,v);G.origin===A.origin&&de!=null?c=de+G.search+G.hash:w=!0}catch{K(!1,`<Link to="${c}"> contains an invalid URL which will probably break when clicked - please update to a valid URL path.`)}let $=xn(c,{relative:a}),[P,k,U]=co(n,h),y=Eo(c,{replace:l,state:u,target:i,preventScrollReset:f,relative:a,viewTransition:g});function j(A){t&&t(A),A.defaultPrevented||y(A)}let V=p.createElement("a",{...h,...U,href:C||$,onClick:w||o?t:j,ref:po(b,k),target:i,"data-discover":!x&&r==="render"?"true":void 0});return P&&!x?p.createElement(p.Fragment,null,V,p.createElement(fo,{page:$})):V});Gr.displayName="Link";var go=p.forwardRef(function({"aria-current":t="page",caseSensitive:r=!1,className:n="",end:a=!1,style:o,to:l,viewTransition:u,children:i,...c},f){let g=rt(l,{relative:c.relative}),h=Ce(),b=p.useContext(tt),{navigator:v,basename:x}=p.useContext(me),C=b!=null&&Po(g)&&u===!0,w=v.encodeLocation?v.encodeLocation(g).pathname:g.pathname,$=h.pathname,P=b&&b.navigation&&b.navigation.location?b.navigation.location.pathname:null;r||($=$.toLowerCase(),P=P?P.toLowerCase():null,w=w.toLowerCase()),P&&x&&(P=ce(P,x)||P);const k=w!=="/"&&w.endsWith("/")?w.length-1:w.length;let U=$===w||!a&&$.startsWith(w)&&$.charAt(k)==="/",y=P!=null&&(P===w||!a&&P.startsWith(w)&&P.charAt(w.length)==="/"),j={isActive:U,isPending:y,isTransitioning:C},V=U?t:void 0,A;typeof n=="function"?A=n(j):A=[n,U?"active":null,y?"pending":null,C?"transitioning":null].filter(Boolean).join(" ");let G=typeof o=="function"?o(j):o;return p.createElement(Gr,{...c,"aria-current":V,className:A,ref:f,style:G,to:l,viewTransition:u},typeof i=="function"?i(j):i)});go.displayName="NavLink";var wo=p.forwardRef(({discover:e="render",fetcherKey:t,navigate:r,reloadDocument:n,replace:a,state:o,method:l=ft,action:u,onSubmit:i,relative:c,preventScrollReset:f,viewTransition:g,...h},b)=>{let v=So(),x=Co(u,{relative:c}),C=l.toLowerCase()==="get"?"get":"post",w=typeof u=="string"&&qr.test(u),$=P=>{if(i&&i(P),P.defaultPrevented)return;P.preventDefault();let k=P.nativeEvent.submitter,U=(k==null?void 0:k.getAttribute("formmethod"))||l;v(k||P.currentTarget,{fetcherKey:t,method:U,navigate:r,replace:a,state:o,relative:c,preventScrollReset:f,viewTransition:g})};return p.createElement("form",{ref:b,method:C,action:x,onSubmit:n?i:$,...h,"data-discover":!w&&e==="render"?"true":void 0})});wo.displayName="Form";function bo(e){return`${e} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function Xr(e){let t=p.useContext(Ue);return _(t,bo(e)),t}function Eo(e,{target:t,replace:r,state:n,preventScrollReset:a,relative:o,viewTransition:l}={}){let u=Wr(),i=Ce(),c=rt(e,{relative:o});return p.useCallback(f=>{if(Gn(f,t)){f.preventDefault();let g=r!==void 0?r:xe(i)===xe(c);u(e,{replace:g,state:n,preventScrollReset:a,relative:o,viewTransition:l})}},[i,u,c,r,n,t,e,a,o,l])}var Ro=0,xo=()=>`__${String(++Ro)}__`;function So(){let{router:e}=Xr("useSubmit"),{basename:t}=p.useContext(me),r=Un();return p.useCallback(async(n,a={})=>{let{action:o,method:l,encType:u,formData:i,body:c}=Zn(n,t);if(a.navigate===!1){let f=a.fetcherKey||xo();await e.fetch(f,r,a.action||o,{preventScrollReset:a.preventScrollReset,formData:i,body:c,formMethod:a.method||l,formEncType:a.encType||u,flushSync:a.flushSync})}else await e.navigate(a.action||o,{preventScrollReset:a.preventScrollReset,formData:i,body:c,formMethod:a.method||l,formEncType:a.encType||u,replace:a.replace,state:a.state,fromRouteId:r,flushSync:a.flushSync,viewTransition:a.viewTransition})},[e,t,r])}function Co(e,{relative:t}={}){let{basename:r}=p.useContext(me),n=p.useContext(pe);_(n,"useFormAction must be used inside a RouteContext");let[a]=n.matches.slice(-1),o={...rt(e||".",{relative:t})},l=Ce();if(e==null){o.search=l.search;let u=new URLSearchParams(o.search),i=u.getAll("index");if(i.some(f=>f==="")){u.delete("index"),i.filter(g=>g).forEach(g=>u.append("index",g));let f=u.toString();o.search=f?`?${f}`:""}}return(!e||e===".")&&a.route.index&&(o.search=o.search?o.search.replace(/^\?/,"?index&"):"?index"),r!=="/"&&(o.pathname=o.pathname==="/"?r:ye([r,o.pathname])),xe(o)}function Po(e,t={}){let r=p.useContext(At);_(r!=null,"`useViewTransitionState` must be used within `react-router-dom`'s `RouterProvider`.  Did you accidentally import `RouterProvider` from `react-router`?");let{basename:n}=Xr("useViewTransitionState"),a=rt(e,{relative:t.relative});if(!r.isTransitioning)return!1;let o=ce(r.currentLocation.pathname,n)||r.currentLocation.pathname,l=ce(r.nextLocation.pathname,n)||r.nextLocation.pathname;return pt(a.pathname,l)!=null||pt(a.pathname,o)!=null}[...lo];var Qr=va();const Oo=Cr(Qr);/**
 * react-router v7.6.2
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function $o(e){return p.createElement(Hn,{flushSync:Qr.flushSync,...e})}export{Gr as L,Mo as N,To as O,Do as R,Qr as a,Oo as b,wa as c,Wr as d,Fo as e,$o as f,p as r,Ce as u};
//# sourceMappingURL=router-CxATPNq6.js.map
