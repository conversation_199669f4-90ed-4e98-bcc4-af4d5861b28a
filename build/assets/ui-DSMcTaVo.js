import{r as i,R as pe,a as <PERSON>,b as Me}from"./router-CxATPNq6.js";import{j as A}from"./query-DYyOl6VU.js";function re(e,t){if(typeof e=="function")return e(t);e!=null&&(e.current=t)}function ye(...e){return t=>{let n=!1;const r=e.map(o=>{const c=re(o,t);return!n&&typeof c=="function"&&(n=!0),c});if(n)return()=>{for(let o=0;o<r.length;o++){const c=r[o];typeof c=="function"?c():re(e[o],null)}}}}function $(...e){return i.useCallback(ye(...e),e)}function Ee(e){const t=xe(e),n=i.forwardRef((r,o)=>{const{children:c,...s}=r,a=i.Children.toArray(c),m=a.find(De);if(m){const l=m.props.children,f=a.map(d=>d===m?i.Children.count(l)>1?i.Children.only(null):i.isValidElement(l)?l.props.children:null:d);return A.jsx(t,{...s,ref:o,children:i.isValidElement(l)?i.cloneElement(l,void 0,f):null})}return A.jsx(t,{...s,ref:o,children:c})});return n.displayName=`${e}.Slot`,n}var nn=Ee("Slot");function xe(e){const t=i.forwardRef((n,r)=>{const{children:o,...c}=n;if(i.isValidElement(o)){const s=Fe(o),a=ke(c,o.props);return o.type!==i.Fragment&&(a.ref=r?ye(r,s):s),i.cloneElement(o,a)}return i.Children.count(o)>1?i.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}var Ie=Symbol("radix.slottable");function De(e){return i.isValidElement(e)&&typeof e.type=="function"&&"__radixId"in e.type&&e.type.__radixId===Ie}function ke(e,t){const n={...t};for(const r in t){const o=e[r],c=t[r];/^on[A-Z]/.test(r)?o&&c?n[r]=(...a)=>{const m=c(...a);return o(...a),m}:o&&(n[r]=o):r==="style"?n[r]={...o,...c}:r==="className"&&(n[r]=[o,c].filter(Boolean).join(" "))}return{...e,...n}}function Fe(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}function H(e,t,{checkForDefaultPrevented:n=!0}={}){return function(o){if(e==null||e(o),n===!1||!o.defaultPrevented)return t==null?void 0:t(o)}}function rn(e,t=[]){let n=[];function r(c,s){const a=i.createContext(s),m=n.length;n=[...n,s];const l=d=>{var y;const{scope:v,children:p,...w}=d,u=((y=v==null?void 0:v[e])==null?void 0:y[m])||a,h=i.useMemo(()=>w,Object.values(w));return A.jsx(u.Provider,{value:h,children:p})};l.displayName=c+"Provider";function f(d,v){var u;const p=((u=v==null?void 0:v[e])==null?void 0:u[m])||a,w=i.useContext(p);if(w)return w;if(s!==void 0)return s;throw new Error(`\`${d}\` must be used within \`${c}\``)}return[l,f]}const o=()=>{const c=n.map(s=>i.createContext(s));return function(a){const m=(a==null?void 0:a[e])||c;return i.useMemo(()=>({[`__scope${e}`]:{...a,[e]:m}}),[a,m])}};return o.scopeName=e,[r,We(o,...t)]}function We(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(c){const s=r.reduce((a,{useScope:m,scopeName:l})=>{const d=m(c)[`__scope${l}`];return{...a,...d}},{});return i.useMemo(()=>({[`__scope${t.scopeName}`]:s}),[s])}};return n.scopeName=t.scopeName,n}var D=globalThis!=null&&globalThis.document?i.useLayoutEffect:()=>{},_e=pe[" useInsertionEffect ".trim().toString()]||D;function on({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){const[o,c,s]=Be({defaultProp:t,onChange:n}),a=e!==void 0,m=a?e:o;{const f=i.useRef(e!==void 0);i.useEffect(()=>{const d=f.current;d!==a&&console.warn(`${r} is changing from ${d?"controlled":"uncontrolled"} to ${a?"controlled":"uncontrolled"}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`),f.current=a},[a,r])}const l=i.useCallback(f=>{var d;if(a){const v=Ue(f)?f(e):f;v!==e&&((d=s.current)==null||d.call(s,v))}else c(f)},[a,e,c,s]);return[m,l]}function Be({defaultProp:e,onChange:t}){const[n,r]=i.useState(e),o=i.useRef(n),c=i.useRef(t);return _e(()=>{c.current=t},[t]),i.useEffect(()=>{var s;o.current!==n&&((s=c.current)==null||s.call(c,n),o.current=n)},[n,o]),[n,r,c]}function Ue(e){return typeof e=="function"}var je=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"],K=je.reduce((e,t)=>{const n=Ee(`Primitive.${t}`),r=i.forwardRef((o,c)=>{const{asChild:s,...a}=o,m=s?n:t;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),A.jsx(m,{...a,ref:c})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{});function Ve(e,t){e&&Le.flushSync(()=>e.dispatchEvent(t))}function k(e){const t=i.useRef(e);return i.useEffect(()=>{t.current=e}),i.useMemo(()=>(...n)=>{var r;return(r=t.current)==null?void 0:r.call(t,...n)},[])}function $e(e,t=globalThis==null?void 0:globalThis.document){const n=k(e);i.useEffect(()=>{const r=o=>{o.key==="Escape"&&n(o)};return t.addEventListener("keydown",r,{capture:!0}),()=>t.removeEventListener("keydown",r,{capture:!0})},[n,t])}var Ke="DismissableLayer",te="dismissableLayer.update",Xe="dismissableLayer.pointerDownOutside",Ye="dismissableLayer.focusOutside",oe,ge=i.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),He=i.forwardRef((e,t)=>{const{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:r,onPointerDownOutside:o,onFocusOutside:c,onInteractOutside:s,onDismiss:a,...m}=e,l=i.useContext(ge),[f,d]=i.useState(null),v=(f==null?void 0:f.ownerDocument)??(globalThis==null?void 0:globalThis.document),[,p]=i.useState({}),w=$(t,E=>d(E)),u=Array.from(l.layers),[h]=[...l.layersWithOutsidePointerEventsDisabled].slice(-1),y=u.indexOf(h),g=f?u.indexOf(f):-1,b=l.layersWithOutsidePointerEventsDisabled.size>0,S=g>=y,C=Ze(E=>{const N=E.target,I=[...l.branches].some(Y=>Y.contains(N));!S||I||(o==null||o(E),s==null||s(E),E.defaultPrevented||a==null||a())},v),R=qe(E=>{const N=E.target;[...l.branches].some(Y=>Y.contains(N))||(c==null||c(E),s==null||s(E),E.defaultPrevented||a==null||a())},v);return $e(E=>{g===l.layers.size-1&&(r==null||r(E),!E.defaultPrevented&&a&&(E.preventDefault(),a()))},v),i.useEffect(()=>{if(f)return n&&(l.layersWithOutsidePointerEventsDisabled.size===0&&(oe=v.body.style.pointerEvents,v.body.style.pointerEvents="none"),l.layersWithOutsidePointerEventsDisabled.add(f)),l.layers.add(f),ae(),()=>{n&&l.layersWithOutsidePointerEventsDisabled.size===1&&(v.body.style.pointerEvents=oe)}},[f,v,n,l]),i.useEffect(()=>()=>{f&&(l.layers.delete(f),l.layersWithOutsidePointerEventsDisabled.delete(f),ae())},[f,l]),i.useEffect(()=>{const E=()=>p({});return document.addEventListener(te,E),()=>document.removeEventListener(te,E)},[]),A.jsx(K.div,{...m,ref:w,style:{pointerEvents:b?S?"auto":"none":void 0,...e.style},onFocusCapture:H(e.onFocusCapture,R.onFocusCapture),onBlurCapture:H(e.onBlurCapture,R.onBlurCapture),onPointerDownCapture:H(e.onPointerDownCapture,C.onPointerDownCapture)})});He.displayName=Ke;var ze="DismissableLayerBranch",Ge=i.forwardRef((e,t)=>{const n=i.useContext(ge),r=i.useRef(null),o=$(t,r);return i.useEffect(()=>{const c=r.current;if(c)return n.branches.add(c),()=>{n.branches.delete(c)}},[n.branches]),A.jsx(K.div,{...e,ref:o})});Ge.displayName=ze;function Ze(e,t=globalThis==null?void 0:globalThis.document){const n=k(e),r=i.useRef(!1),o=i.useRef(()=>{});return i.useEffect(()=>{const c=a=>{if(a.target&&!r.current){let m=function(){be(Xe,n,l,{discrete:!0})};const l={originalEvent:a};a.pointerType==="touch"?(t.removeEventListener("click",o.current),o.current=m,t.addEventListener("click",o.current,{once:!0})):m()}else t.removeEventListener("click",o.current);r.current=!1},s=window.setTimeout(()=>{t.addEventListener("pointerdown",c)},0);return()=>{window.clearTimeout(s),t.removeEventListener("pointerdown",c),t.removeEventListener("click",o.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}function qe(e,t=globalThis==null?void 0:globalThis.document){const n=k(e),r=i.useRef(!1);return i.useEffect(()=>{const o=c=>{c.target&&!r.current&&be(Ye,n,{originalEvent:c},{discrete:!1})};return t.addEventListener("focusin",o),()=>t.removeEventListener("focusin",o)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}function ae(){const e=new CustomEvent(te);document.dispatchEvent(e)}function be(e,t,n,{discrete:r}){const o=n.originalEvent.target,c=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?Ve(o,c):o.dispatchEvent(c)}var z=0;function an(){i.useEffect(()=>{const e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??ie()),document.body.insertAdjacentElement("beforeend",e[1]??ie()),z++,()=>{z===1&&document.querySelectorAll("[data-radix-focus-guard]").forEach(t=>t.remove()),z--}},[])}function ie(){const e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var G="focusScope.autoFocusOnMount",Z="focusScope.autoFocusOnUnmount",ce={bubbles:!1,cancelable:!0},Qe="FocusScope",Je=i.forwardRef((e,t)=>{const{loop:n=!1,trapped:r=!1,onMountAutoFocus:o,onUnmountAutoFocus:c,...s}=e,[a,m]=i.useState(null),l=k(o),f=k(c),d=i.useRef(null),v=$(t,u=>m(u)),p=i.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;i.useEffect(()=>{if(r){let u=function(b){if(p.paused||!a)return;const S=b.target;a.contains(S)?d.current=S:O(d.current,{select:!0})},h=function(b){if(p.paused||!a)return;const S=b.relatedTarget;S!==null&&(a.contains(S)||O(d.current,{select:!0}))},y=function(b){if(document.activeElement===document.body)for(const C of b)C.removedNodes.length>0&&O(a)};document.addEventListener("focusin",u),document.addEventListener("focusout",h);const g=new MutationObserver(y);return a&&g.observe(a,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",u),document.removeEventListener("focusout",h),g.disconnect()}}},[r,a,p.paused]),i.useEffect(()=>{if(a){ue.add(p);const u=document.activeElement;if(!a.contains(u)){const y=new CustomEvent(G,ce);a.addEventListener(G,l),a.dispatchEvent(y),y.defaultPrevented||(et(at(Se(a)),{select:!0}),document.activeElement===u&&O(a))}return()=>{a.removeEventListener(G,l),setTimeout(()=>{const y=new CustomEvent(Z,ce);a.addEventListener(Z,f),a.dispatchEvent(y),y.defaultPrevented||O(u??document.body,{select:!0}),a.removeEventListener(Z,f),ue.remove(p)},0)}}},[a,l,f,p]);const w=i.useCallback(u=>{if(!n&&!r||p.paused)return;const h=u.key==="Tab"&&!u.altKey&&!u.ctrlKey&&!u.metaKey,y=document.activeElement;if(h&&y){const g=u.currentTarget,[b,S]=tt(g);b&&S?!u.shiftKey&&y===S?(u.preventDefault(),n&&O(b,{select:!0})):u.shiftKey&&y===b&&(u.preventDefault(),n&&O(S,{select:!0})):y===g&&u.preventDefault()}},[n,r,p.paused]);return A.jsx(K.div,{tabIndex:-1,...s,ref:v,onKeyDown:w})});Je.displayName=Qe;function et(e,{select:t=!1}={}){const n=document.activeElement;for(const r of e)if(O(r,{select:t}),document.activeElement!==n)return}function tt(e){const t=Se(e),n=se(t,e),r=se(t.reverse(),e);return[n,r]}function Se(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:r=>{const o=r.tagName==="INPUT"&&r.type==="hidden";return r.disabled||r.hidden||o?NodeFilter.FILTER_SKIP:r.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function se(e,t){for(const n of e)if(!nt(n,{upTo:t}))return n}function nt(e,{upTo:t}){if(getComputedStyle(e).visibility==="hidden")return!0;for(;e;){if(t!==void 0&&e===t)return!1;if(getComputedStyle(e).display==="none")return!0;e=e.parentElement}return!1}function rt(e){return e instanceof HTMLInputElement&&"select"in e}function O(e,{select:t=!1}={}){if(e&&e.focus){const n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&rt(e)&&t&&e.select()}}var ue=ot();function ot(){let e=[];return{add(t){const n=e[0];t!==n&&(n==null||n.pause()),e=le(e,t),e.unshift(t)},remove(t){var n;e=le(e,t),(n=e[0])==null||n.resume()}}}function le(e,t){const n=[...e],r=n.indexOf(t);return r!==-1&&n.splice(r,1),n}function at(e){return e.filter(t=>t.tagName!=="A")}var it=pe[" useId ".trim().toString()]||(()=>{}),ct=0;function cn(e){const[t,n]=i.useState(it());return D(()=>{n(r=>r??String(ct++))},[e]),t?`radix-${t}`:""}var st="Portal",ut=i.forwardRef((e,t)=>{var a;const{container:n,...r}=e,[o,c]=i.useState(!1);D(()=>c(!0),[]);const s=n||o&&((a=globalThis==null?void 0:globalThis.document)==null?void 0:a.body);return s?Me.createPortal(A.jsx(K.div,{...r,ref:t}),s):null});ut.displayName=st;function lt(e,t){return i.useReducer((n,r)=>t[n][r]??n,e)}var ft=e=>{const{present:t,children:n}=e,r=dt(t),o=typeof n=="function"?n({present:r.isPresent}):i.Children.only(n),c=$(r.ref,vt(o));return typeof n=="function"||r.isPresent?i.cloneElement(o,{ref:c}):null};ft.displayName="Presence";function dt(e){const[t,n]=i.useState(),r=i.useRef(null),o=i.useRef(e),c=i.useRef("none"),s=e?"mounted":"unmounted",[a,m]=lt(s,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return i.useEffect(()=>{const l=F(r.current);c.current=a==="mounted"?l:"none"},[a]),D(()=>{const l=r.current,f=o.current;if(f!==e){const v=c.current,p=F(l);e?m("MOUNT"):p==="none"||(l==null?void 0:l.display)==="none"?m("UNMOUNT"):m(f&&v!==p?"ANIMATION_OUT":"UNMOUNT"),o.current=e}},[e,m]),D(()=>{if(t){let l;const f=t.ownerDocument.defaultView??window,d=p=>{const u=F(r.current).includes(p.animationName);if(p.target===t&&u&&(m("ANIMATION_END"),!o.current)){const h=t.style.animationFillMode;t.style.animationFillMode="forwards",l=f.setTimeout(()=>{t.style.animationFillMode==="forwards"&&(t.style.animationFillMode=h)})}},v=p=>{p.target===t&&(c.current=F(r.current))};return t.addEventListener("animationstart",v),t.addEventListener("animationcancel",d),t.addEventListener("animationend",d),()=>{f.clearTimeout(l),t.removeEventListener("animationstart",v),t.removeEventListener("animationcancel",d),t.removeEventListener("animationend",d)}}else m("ANIMATION_END")},[t,m]),{isPresent:["mounted","unmountSuspended"].includes(a),ref:i.useCallback(l=>{r.current=l?getComputedStyle(l):null,n(l)},[])}}function F(e){return(e==null?void 0:e.animationName)||"none"}function vt(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}var mt=function(e){if(typeof document>"u")return null;var t=Array.isArray(e)?e[0]:e;return t.ownerDocument.body},T=new WeakMap,W=new WeakMap,_={},q=0,we=function(e){return e&&(e.host||we(e.parentNode))},ht=function(e,t){return t.map(function(n){if(e.contains(n))return n;var r=we(n);return r&&e.contains(r)?r:(console.error("aria-hidden",n,"in not contained inside",e,". Doing nothing"),null)}).filter(function(n){return!!n})},pt=function(e,t,n,r){var o=ht(t,Array.isArray(e)?e:[e]);_[n]||(_[n]=new WeakMap);var c=_[n],s=[],a=new Set,m=new Set(o),l=function(d){!d||a.has(d)||(a.add(d),l(d.parentNode))};o.forEach(l);var f=function(d){!d||m.has(d)||Array.prototype.forEach.call(d.children,function(v){if(a.has(v))f(v);else try{var p=v.getAttribute(r),w=p!==null&&p!=="false",u=(T.get(v)||0)+1,h=(c.get(v)||0)+1;T.set(v,u),c.set(v,h),s.push(v),u===1&&w&&W.set(v,!0),h===1&&v.setAttribute(n,"true"),w||v.setAttribute(r,"true")}catch(y){console.error("aria-hidden: cannot operate on ",v,y)}})};return f(t),a.clear(),q++,function(){s.forEach(function(d){var v=T.get(d)-1,p=c.get(d)-1;T.set(d,v),c.set(d,p),v||(W.has(d)||d.removeAttribute(r),W.delete(d)),p||d.removeAttribute(n)}),q--,q||(T=new WeakMap,T=new WeakMap,W=new WeakMap,_={})}},sn=function(e,t,n){n===void 0&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),o=mt(e);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live], script"))),pt(r,o,n,"aria-hidden")):function(){return null}},P=function(){return P=Object.assign||function(t){for(var n,r=1,o=arguments.length;r<o;r++){n=arguments[r];for(var c in n)Object.prototype.hasOwnProperty.call(n,c)&&(t[c]=n[c])}return t},P.apply(this,arguments)};function Ce(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}function yt(e,t,n){if(n||arguments.length===2)for(var r=0,o=t.length,c;r<o;r++)(c||!(r in t))&&(c||(c=Array.prototype.slice.call(t,0,r)),c[r]=t[r]);return e.concat(c||Array.prototype.slice.call(t))}var j="right-scroll-bar-position",V="width-before-scroll-bar",Et="with-scroll-bars-hidden",gt="--removed-body-scroll-bar-size";function Q(e,t){return typeof e=="function"?e(t):e&&(e.current=t),e}function bt(e,t){var n=i.useState(function(){return{value:e,callback:t,facade:{get current(){return n.value},set current(r){var o=n.value;o!==r&&(n.value=r,n.callback(r,o))}}}})[0];return n.callback=t,n.facade}var St=typeof window<"u"?i.useLayoutEffect:i.useEffect,fe=new WeakMap;function wt(e,t){var n=bt(null,function(r){return e.forEach(function(o){return Q(o,r)})});return St(function(){var r=fe.get(n);if(r){var o=new Set(r),c=new Set(e),s=n.current;o.forEach(function(a){c.has(a)||Q(a,null)}),c.forEach(function(a){o.has(a)||Q(a,s)})}fe.set(n,e)},[e]),n}function Ct(e){return e}function Pt(e,t){t===void 0&&(t=Ct);var n=[],r=!1,o={read:function(){if(r)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:e},useMedium:function(c){var s=t(c,r);return n.push(s),function(){n=n.filter(function(a){return a!==s})}},assignSyncMedium:function(c){for(r=!0;n.length;){var s=n;n=[],s.forEach(c)}n={push:function(a){return c(a)},filter:function(){return n}}},assignMedium:function(c){r=!0;var s=[];if(n.length){var a=n;n=[],a.forEach(c),s=n}var m=function(){var f=s;s=[],f.forEach(c)},l=function(){return Promise.resolve().then(m)};l(),n={push:function(f){s.push(f),l()},filter:function(f){return s=s.filter(f),n}}}};return o}function Rt(e){e===void 0&&(e={});var t=Pt(null);return t.options=P({async:!0,ssr:!1},e),t}var Pe=function(e){var t=e.sideCar,n=Ce(e,["sideCar"]);if(!t)throw new Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw new Error("Sidecar medium not found");return i.createElement(r,P({},n))};Pe.isSideCarExport=!0;function Nt(e,t){return e.useMedium(t),Pe}var Re=Rt(),J=function(){},X=i.forwardRef(function(e,t){var n=i.useRef(null),r=i.useState({onScrollCapture:J,onWheelCapture:J,onTouchMoveCapture:J}),o=r[0],c=r[1],s=e.forwardProps,a=e.children,m=e.className,l=e.removeScrollBar,f=e.enabled,d=e.shards,v=e.sideCar,p=e.noRelative,w=e.noIsolation,u=e.inert,h=e.allowPinchZoom,y=e.as,g=y===void 0?"div":y,b=e.gapMode,S=Ce(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),C=v,R=wt([n,t]),E=P(P({},S),o);return i.createElement(i.Fragment,null,f&&i.createElement(C,{sideCar:Re,removeScrollBar:l,shards:d,noRelative:p,noIsolation:w,inert:u,setCallbacks:c,allowPinchZoom:!!h,lockRef:n,gapMode:b}),s?i.cloneElement(i.Children.only(a),P(P({},E),{ref:R})):i.createElement(g,P({},E,{className:m,ref:R}),a))});X.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1};X.classNames={fullWidth:V,zeroRight:j};var Ot=function(){if(typeof __webpack_nonce__<"u")return __webpack_nonce__};function At(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=Ot();return t&&e.setAttribute("nonce",t),e}function Tt(e,t){e.styleSheet?e.styleSheet.cssText=t:e.appendChild(document.createTextNode(t))}function Lt(e){var t=document.head||document.getElementsByTagName("head")[0];t.appendChild(e)}var Mt=function(){var e=0,t=null;return{add:function(n){e==0&&(t=At())&&(Tt(t,n),Lt(t)),e++},remove:function(){e--,!e&&t&&(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},xt=function(){var e=Mt();return function(t,n){i.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},Ne=function(){var e=xt(),t=function(n){var r=n.styles,o=n.dynamic;return e(r,o),null};return t},It={left:0,top:0,right:0,gap:0},ee=function(e){return parseInt(e||"",10)||0},Dt=function(e){var t=window.getComputedStyle(document.body),n=t[e==="padding"?"paddingLeft":"marginLeft"],r=t[e==="padding"?"paddingTop":"marginTop"],o=t[e==="padding"?"paddingRight":"marginRight"];return[ee(n),ee(r),ee(o)]},kt=function(e){if(e===void 0&&(e="margin"),typeof window>"u")return It;var t=Dt(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},Ft=Ne(),x="data-scroll-locked",Wt=function(e,t,n,r){var o=e.left,c=e.top,s=e.right,a=e.gap;return n===void 0&&(n="margin"),`
  .`.concat(Et,` {
   overflow: hidden `).concat(r,`;
   padding-right: `).concat(a,"px ").concat(r,`;
  }
  body[`).concat(x,`] {
    overflow: hidden `).concat(r,`;
    overscroll-behavior: contain;
    `).concat([t&&"position: relative ".concat(r,";"),n==="margin"&&`
    padding-left: `.concat(o,`px;
    padding-top: `).concat(c,`px;
    padding-right: `).concat(s,`px;
    margin-left:0;
    margin-top:0;
    margin-right: `).concat(a,"px ").concat(r,`;
    `),n==="padding"&&"padding-right: ".concat(a,"px ").concat(r,";")].filter(Boolean).join(""),`
  }
  
  .`).concat(j,` {
    right: `).concat(a,"px ").concat(r,`;
  }
  
  .`).concat(V,` {
    margin-right: `).concat(a,"px ").concat(r,`;
  }
  
  .`).concat(j," .").concat(j,` {
    right: 0 `).concat(r,`;
  }
  
  .`).concat(V," .").concat(V,` {
    margin-right: 0 `).concat(r,`;
  }
  
  body[`).concat(x,`] {
    `).concat(gt,": ").concat(a,`px;
  }
`)},de=function(){var e=parseInt(document.body.getAttribute(x)||"0",10);return isFinite(e)?e:0},_t=function(){i.useEffect(function(){return document.body.setAttribute(x,(de()+1).toString()),function(){var e=de()-1;e<=0?document.body.removeAttribute(x):document.body.setAttribute(x,e.toString())}},[])},Bt=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=r===void 0?"margin":r;_t();var c=i.useMemo(function(){return kt(o)},[o]);return i.createElement(Ft,{styles:Wt(c,!t,o,n?"":"!important")})},ne=!1;if(typeof window<"u")try{var B=Object.defineProperty({},"passive",{get:function(){return ne=!0,!0}});window.addEventListener("test",B,B),window.removeEventListener("test",B,B)}catch{ne=!1}var L=ne?{passive:!1}:!1,Ut=function(e){return e.tagName==="TEXTAREA"},Oe=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return n[t]!=="hidden"&&!(n.overflowY===n.overflowX&&!Ut(e)&&n[t]==="visible")},jt=function(e){return Oe(e,"overflowY")},Vt=function(e){return Oe(e,"overflowX")},ve=function(e,t){var n=t.ownerDocument,r=t;do{typeof ShadowRoot<"u"&&r instanceof ShadowRoot&&(r=r.host);var o=Ae(e,r);if(o){var c=Te(e,r),s=c[1],a=c[2];if(s>a)return!0}r=r.parentNode}while(r&&r!==n.body);return!1},$t=function(e){var t=e.scrollTop,n=e.scrollHeight,r=e.clientHeight;return[t,n,r]},Kt=function(e){var t=e.scrollLeft,n=e.scrollWidth,r=e.clientWidth;return[t,n,r]},Ae=function(e,t){return e==="v"?jt(t):Vt(t)},Te=function(e,t){return e==="v"?$t(t):Kt(t)},Xt=function(e,t){return e==="h"&&t==="rtl"?-1:1},Yt=function(e,t,n,r,o){var c=Xt(e,window.getComputedStyle(t).direction),s=c*r,a=n.target,m=t.contains(a),l=!1,f=s>0,d=0,v=0;do{if(!a)break;var p=Te(e,a),w=p[0],u=p[1],h=p[2],y=u-h-c*w;(w||y)&&Ae(e,a)&&(d+=y,v+=w);var g=a.parentNode;a=g&&g.nodeType===Node.DOCUMENT_FRAGMENT_NODE?g.host:g}while(!m&&a!==document.body||m&&(t.contains(a)||t===a));return(f&&Math.abs(d)<1||!f&&Math.abs(v)<1)&&(l=!0),l},U=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},me=function(e){return[e.deltaX,e.deltaY]},he=function(e){return e&&"current"in e?e.current:e},Ht=function(e,t){return e[0]===t[0]&&e[1]===t[1]},zt=function(e){return`
  .block-interactivity-`.concat(e,` {pointer-events: none;}
  .allow-interactivity-`).concat(e,` {pointer-events: all;}
`)},Gt=0,M=[];function Zt(e){var t=i.useRef([]),n=i.useRef([0,0]),r=i.useRef(),o=i.useState(Gt++)[0],c=i.useState(Ne)[0],s=i.useRef(e);i.useEffect(function(){s.current=e},[e]),i.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var u=yt([e.lockRef.current],(e.shards||[]).map(he),!0).filter(Boolean);return u.forEach(function(h){return h.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),u.forEach(function(h){return h.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var a=i.useCallback(function(u,h){if("touches"in u&&u.touches.length===2||u.type==="wheel"&&u.ctrlKey)return!s.current.allowPinchZoom;var y=U(u),g=n.current,b="deltaX"in u?u.deltaX:g[0]-y[0],S="deltaY"in u?u.deltaY:g[1]-y[1],C,R=u.target,E=Math.abs(b)>Math.abs(S)?"h":"v";if("touches"in u&&E==="h"&&R.type==="range")return!1;var N=ve(E,R);if(!N)return!0;if(N?C=E:(C=E==="v"?"h":"v",N=ve(E,R)),!N)return!1;if(!r.current&&"changedTouches"in u&&(b||S)&&(r.current=C),!C)return!0;var I=r.current||C;return Yt(I,h,u,I==="h"?b:S)},[]),m=i.useCallback(function(u){var h=u;if(!(!M.length||M[M.length-1]!==c)){var y="deltaY"in h?me(h):U(h),g=t.current.filter(function(C){return C.name===h.type&&(C.target===h.target||h.target===C.shadowParent)&&Ht(C.delta,y)})[0];if(g&&g.should){h.cancelable&&h.preventDefault();return}if(!g){var b=(s.current.shards||[]).map(he).filter(Boolean).filter(function(C){return C.contains(h.target)}),S=b.length>0?a(h,b[0]):!s.current.noIsolation;S&&h.cancelable&&h.preventDefault()}}},[]),l=i.useCallback(function(u,h,y,g){var b={name:u,delta:h,target:y,should:g,shadowParent:qt(y)};t.current.push(b),setTimeout(function(){t.current=t.current.filter(function(S){return S!==b})},1)},[]),f=i.useCallback(function(u){n.current=U(u),r.current=void 0},[]),d=i.useCallback(function(u){l(u.type,me(u),u.target,a(u,e.lockRef.current))},[]),v=i.useCallback(function(u){l(u.type,U(u),u.target,a(u,e.lockRef.current))},[]);i.useEffect(function(){return M.push(c),e.setCallbacks({onScrollCapture:d,onWheelCapture:d,onTouchMoveCapture:v}),document.addEventListener("wheel",m,L),document.addEventListener("touchmove",m,L),document.addEventListener("touchstart",f,L),function(){M=M.filter(function(u){return u!==c}),document.removeEventListener("wheel",m,L),document.removeEventListener("touchmove",m,L),document.removeEventListener("touchstart",f,L)}},[]);var p=e.removeScrollBar,w=e.inert;return i.createElement(i.Fragment,null,w?i.createElement(c,{styles:zt(o)}):null,p?i.createElement(Bt,{noRelative:e.noRelative,gapMode:e.gapMode}):null)}function qt(e){for(var t=null;e!==null;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}const Qt=Nt(Re,Zt);var Jt=i.forwardRef(function(e,t){return i.createElement(X,P({},e,{ref:t,sideCar:Qt}))});Jt.classNames=X.classNames;export{He as D,Je as F,K as P,Jt as R,nn as S,Ee as a,D as b,rn as c,k as d,cn as e,H as f,on as g,ye as h,ft as i,ut as j,an as k,sn as l,Ve as m,$ as u};
//# sourceMappingURL=ui-DSMcTaVo.js.map
