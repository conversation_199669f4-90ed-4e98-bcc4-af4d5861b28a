import{Q as Ho,j as o,a as Ro}from"./query-DYyOl6VU.js";import{r as x,c as Me,a as Po,L as ie,O as Lt,u as Oo,N as jt,d as Lo,e as Ao,f as To}from"./router-CxATPNq6.js";import{a as Eo}from"./vendor-BtP0CW_r.js";import{S as Io,c as at,u as we,a as Xt,P as fe,b as Jt,d as At,e as Qt,f as $,g as Cs,h as Ms,i as Tt,j as Fo,k as Do,R as _o,F as $o,D as Zo,l as zo,m as Bo}from"./ui-DSMcTaVo.js";(function(){const e=document.createElement("link").relList;if(e&&e.supports&&e.supports("modulepreload"))return;for(const r of document.querySelectorAll('link[rel="modulepreload"]'))s(r);new MutationObserver(r=>{for(const i of r)if(i.type==="childList")for(const a of i.addedNodes)a.tagName==="LINK"&&a.rel==="modulepreload"&&s(a)}).observe(document,{childList:!0,subtree:!0});function n(r){const i={};return r.integrity&&(i.integrity=r.integrity),r.referrerPolicy&&(i.referrerPolicy=r.referrerPolicy),r.crossOrigin==="use-credentials"?i.credentials="include":r.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function s(r){if(r.ep)return;r.ep=!0;const i=n(r);fetch(r.href,i)}})();var ht={},Tn;function Ko(){if(Tn)return ht;Tn=1;var t=Eo();return ht.createRoot=t.createRoot,ht.hydrateRoot=t.hydrateRoot,ht}var Uo=Ko();const Go=new Ho({defaultOptions:{queries:{staleTime:5*60*1e3,gcTime:10*60*1e3,retry:(t,e)=>(e==null?void 0:e.status)>=400&&(e==null?void 0:e.status)<500?!1:t<3,refetchOnWindowFocus:!1},mutations:{retry:!1}}}),Wo=(t,e,n,s)=>{var i,a,l,d;const r=[n,{code:e,...s||{}}];if((a=(i=t==null?void 0:t.services)==null?void 0:i.logger)!=null&&a.forward)return t.services.logger.forward(r,"warn","react-i18next::",!0);Te(r[0])&&(r[0]=`react-i18next:: ${r[0]}`),(d=(l=t==null?void 0:t.services)==null?void 0:l.logger)!=null&&d.warn?t.services.logger.warn(...r):console!=null&&console.warn&&console.warn(...r)},En={},en=(t,e,n,s)=>{Te(n)&&En[n]||(Te(n)&&(En[n]=new Date),Wo(t,e,n,s))},Ss=(t,e)=>()=>{if(t.isInitialized)e();else{const n=()=>{setTimeout(()=>{t.off("initialized",n)},0),e()};t.on("initialized",n)}},tn=(t,e,n)=>{t.loadNamespaces(e,Ss(t,n))},In=(t,e,n,s)=>{if(Te(n)&&(n=[n]),t.options.preload&&t.options.preload.indexOf(e)>-1)return tn(t,n,s);n.forEach(r=>{t.options.ns.indexOf(r)<0&&t.options.ns.push(r)}),t.loadLanguages(e,Ss(t,s))},qo=(t,e,n={})=>!e.languages||!e.languages.length?(en(e,"NO_LANGUAGES","i18n.languages were undefined or empty",{languages:e.languages}),!0):e.hasLoadedNamespace(t,{lng:n.lng,precheck:(s,r)=>{var i;if(((i=n.bindI18n)==null?void 0:i.indexOf("languageChanging"))>-1&&s.services.backendConnector.backend&&s.isLanguageChangingTo&&!r(s.isLanguageChangingTo,t))return!1}}),Te=t=>typeof t=="string",Yo=t=>typeof t=="object"&&t!==null,Xo=/&(?:amp|#38|lt|#60|gt|#62|apos|#39|quot|#34|nbsp|#160|copy|#169|reg|#174|hellip|#8230|#x2F|#47);/g,Jo={"&amp;":"&","&#38;":"&","&lt;":"<","&#60;":"<","&gt;":">","&#62;":">","&apos;":"'","&#39;":"'","&quot;":'"',"&#34;":'"',"&nbsp;":" ","&#160;":" ","&copy;":"©","&#169;":"©","&reg;":"®","&#174;":"®","&hellip;":"…","&#8230;":"…","&#x2F;":"/","&#47;":"/"},Qo=t=>Jo[t],ei=t=>t.replace(Xo,Qo);let nn={bindI18n:"languageChanged",bindI18nStore:"",transEmptyNodeValue:"",transSupportBasicHtmlNodes:!0,transWrapTextNodes:"",transKeepBasicHtmlNodesFor:["br","strong","i","p"],useSuspense:!0,unescape:ei};const ti=(t={})=>{nn={...nn,...t}},ni=()=>nn;let ks;const si=t=>{ks=t},ri=()=>ks,oi={type:"3rdParty",init(t){ti(t.options.react),si(t)}},ii=x.createContext();class ai{constructor(){this.usedNamespaces={}}addUsedNamespaces(e){e.forEach(n=>{this.usedNamespaces[n]||(this.usedNamespaces[n]=!0)})}getUsedNamespaces(){return Object.keys(this.usedNamespaces)}}const li=(t,e)=>{const n=x.useRef();return x.useEffect(()=>{n.current=t},[t,e]),n.current},Vs=(t,e,n,s)=>t.getFixedT(e,n,s),ci=(t,e,n,s)=>x.useCallback(Vs(t,e,n,s),[t,e,n,s]),Oe=(t,e={})=>{var C,M,P,O;const{i18n:n}=e,{i18n:s,defaultNS:r}=x.useContext(ii)||{},i=n||s||ri();if(i&&!i.reportNamespaces&&(i.reportNamespaces=new ai),!i){en(i,"NO_I18NEXT_INSTANCE","useTranslation: You will need to pass in an i18next instance by using initReactI18next");const w=(R,D)=>Te(D)?D:Yo(D)&&Te(D.defaultValue)?D.defaultValue:Array.isArray(R)?R[R.length-1]:R,H=[w,{},!1];return H.t=w,H.i18n={},H.ready=!1,H}(C=i.options.react)!=null&&C.wait&&en(i,"DEPRECATED_OPTION","useTranslation: It seems you are still using the old wait option, you may migrate to the new useSuspense behaviour.");const a={...ni(),...i.options.react,...e},{useSuspense:l,keyPrefix:d}=a;let c=t||r||((M=i.options)==null?void 0:M.defaultNS);c=Te(c)?[c]:c||["translation"],(O=(P=i.reportNamespaces).addUsedNamespaces)==null||O.call(P,c);const f=(i.isInitialized||i.initializedStoreOnce)&&c.every(w=>qo(w,i,a)),u=ci(i,e.lng||null,a.nsMode==="fallback"?c:c[0],d),h=()=>u,m=()=>Vs(i,e.lng||null,a.nsMode==="fallback"?c:c[0],d),[g,p]=x.useState(h);let b=c.join();e.lng&&(b=`${e.lng}${b}`);const v=li(b),y=x.useRef(!0);x.useEffect(()=>{const{bindI18n:w,bindI18nStore:H}=a;y.current=!0,!f&&!l&&(e.lng?In(i,e.lng,c,()=>{y.current&&p(m)}):tn(i,c,()=>{y.current&&p(m)})),f&&v&&v!==b&&y.current&&p(m);const R=()=>{y.current&&p(m)};return w&&(i==null||i.on(w,R)),H&&(i==null||i.store.on(H,R)),()=>{y.current=!1,i&&(w==null||w.split(" ").forEach(D=>i.off(D,R))),H&&i&&H.split(" ").forEach(D=>i.store.off(D,R))}},[i,b]),x.useEffect(()=>{y.current&&f&&p(h)},[i,d,f]);const N=[g,i,f];if(N.t=g,N.i18n=i,N.ready=f,f||!f&&!l)return N;throw new Promise(w=>{e.lng?In(i,e.lng,c,()=>w()):tn(i,c,()=>w())})};/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const di=t=>t.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),ui=t=>t.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,n,s)=>s?s.toUpperCase():n.toLowerCase()),Fn=t=>{const e=ui(t);return e.charAt(0).toUpperCase()+e.slice(1)},Hs=(...t)=>t.filter((e,n,s)=>!!e&&e.trim()!==""&&s.indexOf(e)===n).join(" ").trim(),fi=t=>{for(const e in t)if(e.startsWith("aria-")||e==="role"||e==="title")return!0};/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var hi={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const mi=x.forwardRef(({color:t="currentColor",size:e=24,strokeWidth:n=2,absoluteStrokeWidth:s,className:r="",children:i,iconNode:a,...l},d)=>x.createElement("svg",{ref:d,...hi,width:e,height:e,stroke:t,strokeWidth:s?Number(n)*24/Number(e):n,className:Hs("lucide",r),...!i&&!fi(l)&&{"aria-hidden":"true"},...l},[...a.map(([c,f])=>x.createElement(c,f)),...Array.isArray(i)?i:[i]]));/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const te=(t,e)=>{const n=x.forwardRef(({className:s,...r},i)=>x.createElement(mi,{ref:i,iconNode:e,className:Hs(`lucide-${di(Fn(t))}`,`lucide-${t}`,s),...r}));return n.displayName=Fn(t),n};/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const gi=[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]],pi=te("arrow-left",gi);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const xi=[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]],sn=te("book-open",xi);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const bi=[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]],yi=te("check",bi);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const vi=[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]],wi=te("chevron-right",vi);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ji=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]],Ni=te("circle",ji);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ci=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]],Mi=te("clock",Ci);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Si=[["path",{d:"M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4",key:"tonef"}],["path",{d:"M9 18c-4.51 2-5-2-7-2",key:"9comsn"}]],ki=te("github",Si);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Vi=[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]],Hi=te("house",Vi);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ri=[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]],Pi=te("mail",Ri);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Oi=[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]],Li=te("menu",Oi);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ai=[["rect",{width:"20",height:"14",x:"2",y:"3",rx:"2",key:"48i651"}],["line",{x1:"8",x2:"16",y1:"21",y2:"21",key:"1svkeh"}],["line",{x1:"12",x2:"12",y1:"17",y2:"21",key:"vw1qmm"}]],Ti=te("monitor",Ai);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ei=[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]],Dn=te("moon",Ei);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ii=[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]],Fi=te("search",Ii);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Di=[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]],_n=te("star",Di);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _i=[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]],$n=te("sun",_i);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $i=[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]],Zi=te("trending-up",$i);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const zi=[["path",{d:"M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z",key:"pff0z6"}]],Bi=te("twitter",zi);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ki=[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]],Ui=te("user",Ki);function Rs(t){var e,n,s="";if(typeof t=="string"||typeof t=="number")s+=t;else if(typeof t=="object")if(Array.isArray(t)){var r=t.length;for(e=0;e<r;e++)t[e]&&(n=Rs(t[e]))&&(s&&(s+=" "),s+=n)}else for(n in t)t[n]&&(s&&(s+=" "),s+=n);return s}function Ps(){for(var t,e,n=0,s="",r=arguments.length;n<r;n++)(t=arguments[n])&&(e=Rs(t))&&(s&&(s+=" "),s+=e);return s}const Zn=t=>typeof t=="boolean"?`${t}`:t===0?"0":t,zn=Ps,Os=(t,e)=>n=>{var s;if((e==null?void 0:e.variants)==null)return zn(t,n==null?void 0:n.class,n==null?void 0:n.className);const{variants:r,defaultVariants:i}=e,a=Object.keys(r).map(c=>{const f=n==null?void 0:n[c],u=i==null?void 0:i[c];if(f===null)return null;const h=Zn(f)||Zn(u);return r[c][h]}),l=n&&Object.entries(n).reduce((c,f)=>{let[u,h]=f;return h===void 0||(c[u]=h),c},{}),d=e==null||(s=e.compoundVariants)===null||s===void 0?void 0:s.reduce((c,f)=>{let{class:u,className:h,...m}=f;return Object.entries(m).every(g=>{let[p,b]=g;return Array.isArray(b)?b.includes({...i,...l}[p]):{...i,...l}[p]===b})?[...c,u,h]:c},[]);return zn(t,a,d,n==null?void 0:n.class,n==null?void 0:n.className)},xn="-",Gi=t=>{const e=qi(t),{conflictingClassGroups:n,conflictingClassGroupModifiers:s}=t;return{getClassGroupId:a=>{const l=a.split(xn);return l[0]===""&&l.length!==1&&l.shift(),Ls(l,e)||Wi(a)},getConflictingClassGroupIds:(a,l)=>{const d=n[a]||[];return l&&s[a]?[...d,...s[a]]:d}}},Ls=(t,e)=>{var a;if(t.length===0)return e.classGroupId;const n=t[0],s=e.nextPart.get(n),r=s?Ls(t.slice(1),s):void 0;if(r)return r;if(e.validators.length===0)return;const i=t.join(xn);return(a=e.validators.find(({validator:l})=>l(i)))==null?void 0:a.classGroupId},Bn=/^\[(.+)\]$/,Wi=t=>{if(Bn.test(t)){const e=Bn.exec(t)[1],n=e==null?void 0:e.substring(0,e.indexOf(":"));if(n)return"arbitrary.."+n}},qi=t=>{const{theme:e,classGroups:n}=t,s={nextPart:new Map,validators:[]};for(const r in n)rn(n[r],s,r,e);return s},rn=(t,e,n,s)=>{t.forEach(r=>{if(typeof r=="string"){const i=r===""?e:Kn(e,r);i.classGroupId=n;return}if(typeof r=="function"){if(Yi(r)){rn(r(s),e,n,s);return}e.validators.push({validator:r,classGroupId:n});return}Object.entries(r).forEach(([i,a])=>{rn(a,Kn(e,i),n,s)})})},Kn=(t,e)=>{let n=t;return e.split(xn).forEach(s=>{n.nextPart.has(s)||n.nextPart.set(s,{nextPart:new Map,validators:[]}),n=n.nextPart.get(s)}),n},Yi=t=>t.isThemeGetter,Xi=t=>{if(t<1)return{get:()=>{},set:()=>{}};let e=0,n=new Map,s=new Map;const r=(i,a)=>{n.set(i,a),e++,e>t&&(e=0,s=n,n=new Map)};return{get(i){let a=n.get(i);if(a!==void 0)return a;if((a=s.get(i))!==void 0)return r(i,a),a},set(i,a){n.has(i)?n.set(i,a):r(i,a)}}},on="!",an=":",Ji=an.length,Qi=t=>{const{prefix:e,experimentalParseClassName:n}=t;let s=r=>{const i=[];let a=0,l=0,d=0,c;for(let g=0;g<r.length;g++){let p=r[g];if(a===0&&l===0){if(p===an){i.push(r.slice(d,g)),d=g+Ji;continue}if(p==="/"){c=g;continue}}p==="["?a++:p==="]"?a--:p==="("?l++:p===")"&&l--}const f=i.length===0?r:r.substring(d),u=ea(f),h=u!==f,m=c&&c>d?c-d:void 0;return{modifiers:i,hasImportantModifier:h,baseClassName:u,maybePostfixModifierPosition:m}};if(e){const r=e+an,i=s;s=a=>a.startsWith(r)?i(a.substring(r.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:a,maybePostfixModifierPosition:void 0}}if(n){const r=s;s=i=>n({className:i,parseClassName:r})}return s},ea=t=>t.endsWith(on)?t.substring(0,t.length-1):t.startsWith(on)?t.substring(1):t,ta=t=>{const e=Object.fromEntries(t.orderSensitiveModifiers.map(s=>[s,!0]));return s=>{if(s.length<=1)return s;const r=[];let i=[];return s.forEach(a=>{a[0]==="["||e[a]?(r.push(...i.sort(),a),i=[]):i.push(a)}),r.push(...i.sort()),r}},na=t=>({cache:Xi(t.cacheSize),parseClassName:Qi(t),sortModifiers:ta(t),...Gi(t)}),sa=/\s+/,ra=(t,e)=>{const{parseClassName:n,getClassGroupId:s,getConflictingClassGroupIds:r,sortModifiers:i}=e,a=[],l=t.trim().split(sa);let d="";for(let c=l.length-1;c>=0;c-=1){const f=l[c],{isExternal:u,modifiers:h,hasImportantModifier:m,baseClassName:g,maybePostfixModifierPosition:p}=n(f);if(u){d=f+(d.length>0?" "+d:d);continue}let b=!!p,v=s(b?g.substring(0,p):g);if(!v){if(!b){d=f+(d.length>0?" "+d:d);continue}if(v=s(g),!v){d=f+(d.length>0?" "+d:d);continue}b=!1}const y=i(h).join(":"),N=m?y+on:y,C=N+v;if(a.includes(C))continue;a.push(C);const M=r(v,b);for(let P=0;P<M.length;++P){const O=M[P];a.push(N+O)}d=f+(d.length>0?" "+d:d)}return d};function oa(){let t=0,e,n,s="";for(;t<arguments.length;)(e=arguments[t++])&&(n=As(e))&&(s&&(s+=" "),s+=n);return s}const As=t=>{if(typeof t=="string")return t;let e,n="";for(let s=0;s<t.length;s++)t[s]&&(e=As(t[s]))&&(n&&(n+=" "),n+=e);return n};function ia(t,...e){let n,s,r,i=a;function a(d){const c=e.reduce((f,u)=>u(f),t());return n=na(c),s=n.cache.get,r=n.cache.set,i=l,l(d)}function l(d){const c=s(d);if(c)return c;const f=ra(d,n);return r(d,f),f}return function(){return i(oa.apply(null,arguments))}}const Q=t=>{const e=n=>n[t]||[];return e.isThemeGetter=!0,e},Ts=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,Es=/^\((?:(\w[\w-]*):)?(.+)\)$/i,aa=/^\d+\/\d+$/,la=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,ca=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,da=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,ua=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,fa=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,_e=t=>aa.test(t),I=t=>!!t&&!Number.isNaN(Number(t)),He=t=>!!t&&Number.isInteger(Number(t)),Bt=t=>t.endsWith("%")&&I(t.slice(0,-1)),Ce=t=>la.test(t),ha=()=>!0,ma=t=>ca.test(t)&&!da.test(t),Is=()=>!1,ga=t=>ua.test(t),pa=t=>fa.test(t),xa=t=>!k(t)&&!V(t),ba=t=>Ue(t,_s,Is),k=t=>Ts.test(t),Ae=t=>Ue(t,$s,ma),Kt=t=>Ue(t,Na,I),Un=t=>Ue(t,Fs,Is),ya=t=>Ue(t,Ds,pa),mt=t=>Ue(t,Zs,ga),V=t=>Es.test(t),Xe=t=>Ge(t,$s),va=t=>Ge(t,Ca),Gn=t=>Ge(t,Fs),wa=t=>Ge(t,_s),ja=t=>Ge(t,Ds),gt=t=>Ge(t,Zs,!0),Ue=(t,e,n)=>{const s=Ts.exec(t);return s?s[1]?e(s[1]):n(s[2]):!1},Ge=(t,e,n=!1)=>{const s=Es.exec(t);return s?s[1]?e(s[1]):n:!1},Fs=t=>t==="position"||t==="percentage",Ds=t=>t==="image"||t==="url",_s=t=>t==="length"||t==="size"||t==="bg-size",$s=t=>t==="length",Na=t=>t==="number",Ca=t=>t==="family-name",Zs=t=>t==="shadow",Ma=()=>{const t=Q("color"),e=Q("font"),n=Q("text"),s=Q("font-weight"),r=Q("tracking"),i=Q("leading"),a=Q("breakpoint"),l=Q("container"),d=Q("spacing"),c=Q("radius"),f=Q("shadow"),u=Q("inset-shadow"),h=Q("text-shadow"),m=Q("drop-shadow"),g=Q("blur"),p=Q("perspective"),b=Q("aspect"),v=Q("ease"),y=Q("animate"),N=()=>["auto","avoid","all","avoid-page","page","left","right","column"],C=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],M=()=>[...C(),V,k],P=()=>["auto","hidden","clip","visible","scroll"],O=()=>["auto","contain","none"],w=()=>[V,k,d],H=()=>[_e,"full","auto",...w()],R=()=>[He,"none","subgrid",V,k],D=()=>["auto",{span:["full",He,V,k]},He,V,k],z=()=>[He,"auto",V,k],F=()=>["auto","min","max","fr",V,k],T=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],K=()=>["start","end","center","stretch","center-safe","end-safe"],L=()=>["auto",...w()],E=()=>[_e,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...w()],j=()=>[t,V,k],S=()=>[...C(),Gn,Un,{position:[V,k]}],Z=()=>["no-repeat",{repeat:["","x","y","space","round"]}],_=()=>["auto","cover","contain",wa,ba,{size:[V,k]}],W=()=>[Bt,Xe,Ae],U=()=>["","none","full",c,V,k],q=()=>["",I,Xe,Ae],de=()=>["solid","dashed","dotted","double"],ge=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],B=()=>[I,Bt,Gn,Un],Ve=()=>["","none",g,V,k],ne=()=>["none",I,V,k],pe=()=>["none",I,V,k],De=()=>[I,V,k],Le=()=>[_e,"full",...w()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[Ce],breakpoint:[Ce],color:[ha],container:[Ce],"drop-shadow":[Ce],ease:["in","out","in-out"],font:[xa],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[Ce],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[Ce],shadow:[Ce],spacing:["px",I],text:[Ce],"text-shadow":[Ce],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",_e,k,V,b]}],container:["container"],columns:[{columns:[I,k,V,l]}],"break-after":[{"break-after":N()}],"break-before":[{"break-before":N()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:M()}],overflow:[{overflow:P()}],"overflow-x":[{"overflow-x":P()}],"overflow-y":[{"overflow-y":P()}],overscroll:[{overscroll:O()}],"overscroll-x":[{"overscroll-x":O()}],"overscroll-y":[{"overscroll-y":O()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:H()}],"inset-x":[{"inset-x":H()}],"inset-y":[{"inset-y":H()}],start:[{start:H()}],end:[{end:H()}],top:[{top:H()}],right:[{right:H()}],bottom:[{bottom:H()}],left:[{left:H()}],visibility:["visible","invisible","collapse"],z:[{z:[He,"auto",V,k]}],basis:[{basis:[_e,"full","auto",l,...w()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[I,_e,"auto","initial","none",k]}],grow:[{grow:["",I,V,k]}],shrink:[{shrink:["",I,V,k]}],order:[{order:[He,"first","last","none",V,k]}],"grid-cols":[{"grid-cols":R()}],"col-start-end":[{col:D()}],"col-start":[{"col-start":z()}],"col-end":[{"col-end":z()}],"grid-rows":[{"grid-rows":R()}],"row-start-end":[{row:D()}],"row-start":[{"row-start":z()}],"row-end":[{"row-end":z()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":F()}],"auto-rows":[{"auto-rows":F()}],gap:[{gap:w()}],"gap-x":[{"gap-x":w()}],"gap-y":[{"gap-y":w()}],"justify-content":[{justify:[...T(),"normal"]}],"justify-items":[{"justify-items":[...K(),"normal"]}],"justify-self":[{"justify-self":["auto",...K()]}],"align-content":[{content:["normal",...T()]}],"align-items":[{items:[...K(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...K(),{baseline:["","last"]}]}],"place-content":[{"place-content":T()}],"place-items":[{"place-items":[...K(),"baseline"]}],"place-self":[{"place-self":["auto",...K()]}],p:[{p:w()}],px:[{px:w()}],py:[{py:w()}],ps:[{ps:w()}],pe:[{pe:w()}],pt:[{pt:w()}],pr:[{pr:w()}],pb:[{pb:w()}],pl:[{pl:w()}],m:[{m:L()}],mx:[{mx:L()}],my:[{my:L()}],ms:[{ms:L()}],me:[{me:L()}],mt:[{mt:L()}],mr:[{mr:L()}],mb:[{mb:L()}],ml:[{ml:L()}],"space-x":[{"space-x":w()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":w()}],"space-y-reverse":["space-y-reverse"],size:[{size:E()}],w:[{w:[l,"screen",...E()]}],"min-w":[{"min-w":[l,"screen","none",...E()]}],"max-w":[{"max-w":[l,"screen","none","prose",{screen:[a]},...E()]}],h:[{h:["screen","lh",...E()]}],"min-h":[{"min-h":["screen","lh","none",...E()]}],"max-h":[{"max-h":["screen","lh",...E()]}],"font-size":[{text:["base",n,Xe,Ae]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[s,V,Kt]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",Bt,k]}],"font-family":[{font:[va,k,e]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[r,V,k]}],"line-clamp":[{"line-clamp":[I,"none",V,Kt]}],leading:[{leading:[i,...w()]}],"list-image":[{"list-image":["none",V,k]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",V,k]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:j()}],"text-color":[{text:j()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...de(),"wavy"]}],"text-decoration-thickness":[{decoration:[I,"from-font","auto",V,Ae]}],"text-decoration-color":[{decoration:j()}],"underline-offset":[{"underline-offset":[I,"auto",V,k]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:w()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",V,k]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",V,k]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:S()}],"bg-repeat":[{bg:Z()}],"bg-size":[{bg:_()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},He,V,k],radial:["",V,k],conic:[He,V,k]},ja,ya]}],"bg-color":[{bg:j()}],"gradient-from-pos":[{from:W()}],"gradient-via-pos":[{via:W()}],"gradient-to-pos":[{to:W()}],"gradient-from":[{from:j()}],"gradient-via":[{via:j()}],"gradient-to":[{to:j()}],rounded:[{rounded:U()}],"rounded-s":[{"rounded-s":U()}],"rounded-e":[{"rounded-e":U()}],"rounded-t":[{"rounded-t":U()}],"rounded-r":[{"rounded-r":U()}],"rounded-b":[{"rounded-b":U()}],"rounded-l":[{"rounded-l":U()}],"rounded-ss":[{"rounded-ss":U()}],"rounded-se":[{"rounded-se":U()}],"rounded-ee":[{"rounded-ee":U()}],"rounded-es":[{"rounded-es":U()}],"rounded-tl":[{"rounded-tl":U()}],"rounded-tr":[{"rounded-tr":U()}],"rounded-br":[{"rounded-br":U()}],"rounded-bl":[{"rounded-bl":U()}],"border-w":[{border:q()}],"border-w-x":[{"border-x":q()}],"border-w-y":[{"border-y":q()}],"border-w-s":[{"border-s":q()}],"border-w-e":[{"border-e":q()}],"border-w-t":[{"border-t":q()}],"border-w-r":[{"border-r":q()}],"border-w-b":[{"border-b":q()}],"border-w-l":[{"border-l":q()}],"divide-x":[{"divide-x":q()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":q()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...de(),"hidden","none"]}],"divide-style":[{divide:[...de(),"hidden","none"]}],"border-color":[{border:j()}],"border-color-x":[{"border-x":j()}],"border-color-y":[{"border-y":j()}],"border-color-s":[{"border-s":j()}],"border-color-e":[{"border-e":j()}],"border-color-t":[{"border-t":j()}],"border-color-r":[{"border-r":j()}],"border-color-b":[{"border-b":j()}],"border-color-l":[{"border-l":j()}],"divide-color":[{divide:j()}],"outline-style":[{outline:[...de(),"none","hidden"]}],"outline-offset":[{"outline-offset":[I,V,k]}],"outline-w":[{outline:["",I,Xe,Ae]}],"outline-color":[{outline:j()}],shadow:[{shadow:["","none",f,gt,mt]}],"shadow-color":[{shadow:j()}],"inset-shadow":[{"inset-shadow":["none",u,gt,mt]}],"inset-shadow-color":[{"inset-shadow":j()}],"ring-w":[{ring:q()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:j()}],"ring-offset-w":[{"ring-offset":[I,Ae]}],"ring-offset-color":[{"ring-offset":j()}],"inset-ring-w":[{"inset-ring":q()}],"inset-ring-color":[{"inset-ring":j()}],"text-shadow":[{"text-shadow":["none",h,gt,mt]}],"text-shadow-color":[{"text-shadow":j()}],opacity:[{opacity:[I,V,k]}],"mix-blend":[{"mix-blend":[...ge(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":ge()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[I]}],"mask-image-linear-from-pos":[{"mask-linear-from":B()}],"mask-image-linear-to-pos":[{"mask-linear-to":B()}],"mask-image-linear-from-color":[{"mask-linear-from":j()}],"mask-image-linear-to-color":[{"mask-linear-to":j()}],"mask-image-t-from-pos":[{"mask-t-from":B()}],"mask-image-t-to-pos":[{"mask-t-to":B()}],"mask-image-t-from-color":[{"mask-t-from":j()}],"mask-image-t-to-color":[{"mask-t-to":j()}],"mask-image-r-from-pos":[{"mask-r-from":B()}],"mask-image-r-to-pos":[{"mask-r-to":B()}],"mask-image-r-from-color":[{"mask-r-from":j()}],"mask-image-r-to-color":[{"mask-r-to":j()}],"mask-image-b-from-pos":[{"mask-b-from":B()}],"mask-image-b-to-pos":[{"mask-b-to":B()}],"mask-image-b-from-color":[{"mask-b-from":j()}],"mask-image-b-to-color":[{"mask-b-to":j()}],"mask-image-l-from-pos":[{"mask-l-from":B()}],"mask-image-l-to-pos":[{"mask-l-to":B()}],"mask-image-l-from-color":[{"mask-l-from":j()}],"mask-image-l-to-color":[{"mask-l-to":j()}],"mask-image-x-from-pos":[{"mask-x-from":B()}],"mask-image-x-to-pos":[{"mask-x-to":B()}],"mask-image-x-from-color":[{"mask-x-from":j()}],"mask-image-x-to-color":[{"mask-x-to":j()}],"mask-image-y-from-pos":[{"mask-y-from":B()}],"mask-image-y-to-pos":[{"mask-y-to":B()}],"mask-image-y-from-color":[{"mask-y-from":j()}],"mask-image-y-to-color":[{"mask-y-to":j()}],"mask-image-radial":[{"mask-radial":[V,k]}],"mask-image-radial-from-pos":[{"mask-radial-from":B()}],"mask-image-radial-to-pos":[{"mask-radial-to":B()}],"mask-image-radial-from-color":[{"mask-radial-from":j()}],"mask-image-radial-to-color":[{"mask-radial-to":j()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":C()}],"mask-image-conic-pos":[{"mask-conic":[I]}],"mask-image-conic-from-pos":[{"mask-conic-from":B()}],"mask-image-conic-to-pos":[{"mask-conic-to":B()}],"mask-image-conic-from-color":[{"mask-conic-from":j()}],"mask-image-conic-to-color":[{"mask-conic-to":j()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:S()}],"mask-repeat":[{mask:Z()}],"mask-size":[{mask:_()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",V,k]}],filter:[{filter:["","none",V,k]}],blur:[{blur:Ve()}],brightness:[{brightness:[I,V,k]}],contrast:[{contrast:[I,V,k]}],"drop-shadow":[{"drop-shadow":["","none",m,gt,mt]}],"drop-shadow-color":[{"drop-shadow":j()}],grayscale:[{grayscale:["",I,V,k]}],"hue-rotate":[{"hue-rotate":[I,V,k]}],invert:[{invert:["",I,V,k]}],saturate:[{saturate:[I,V,k]}],sepia:[{sepia:["",I,V,k]}],"backdrop-filter":[{"backdrop-filter":["","none",V,k]}],"backdrop-blur":[{"backdrop-blur":Ve()}],"backdrop-brightness":[{"backdrop-brightness":[I,V,k]}],"backdrop-contrast":[{"backdrop-contrast":[I,V,k]}],"backdrop-grayscale":[{"backdrop-grayscale":["",I,V,k]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[I,V,k]}],"backdrop-invert":[{"backdrop-invert":["",I,V,k]}],"backdrop-opacity":[{"backdrop-opacity":[I,V,k]}],"backdrop-saturate":[{"backdrop-saturate":[I,V,k]}],"backdrop-sepia":[{"backdrop-sepia":["",I,V,k]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":w()}],"border-spacing-x":[{"border-spacing-x":w()}],"border-spacing-y":[{"border-spacing-y":w()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",V,k]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[I,"initial",V,k]}],ease:[{ease:["linear","initial",v,V,k]}],delay:[{delay:[I,V,k]}],animate:[{animate:["none",y,V,k]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[p,V,k]}],"perspective-origin":[{"perspective-origin":M()}],rotate:[{rotate:ne()}],"rotate-x":[{"rotate-x":ne()}],"rotate-y":[{"rotate-y":ne()}],"rotate-z":[{"rotate-z":ne()}],scale:[{scale:pe()}],"scale-x":[{"scale-x":pe()}],"scale-y":[{"scale-y":pe()}],"scale-z":[{"scale-z":pe()}],"scale-3d":["scale-3d"],skew:[{skew:De()}],"skew-x":[{"skew-x":De()}],"skew-y":[{"skew-y":De()}],transform:[{transform:[V,k,"","none","gpu","cpu"]}],"transform-origin":[{origin:M()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:Le()}],"translate-x":[{"translate-x":Le()}],"translate-y":[{"translate-y":Le()}],"translate-z":[{"translate-z":Le()}],"translate-none":["translate-none"],accent:[{accent:j()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:j()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",V,k]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":w()}],"scroll-mx":[{"scroll-mx":w()}],"scroll-my":[{"scroll-my":w()}],"scroll-ms":[{"scroll-ms":w()}],"scroll-me":[{"scroll-me":w()}],"scroll-mt":[{"scroll-mt":w()}],"scroll-mr":[{"scroll-mr":w()}],"scroll-mb":[{"scroll-mb":w()}],"scroll-ml":[{"scroll-ml":w()}],"scroll-p":[{"scroll-p":w()}],"scroll-px":[{"scroll-px":w()}],"scroll-py":[{"scroll-py":w()}],"scroll-ps":[{"scroll-ps":w()}],"scroll-pe":[{"scroll-pe":w()}],"scroll-pt":[{"scroll-pt":w()}],"scroll-pr":[{"scroll-pr":w()}],"scroll-pb":[{"scroll-pb":w()}],"scroll-pl":[{"scroll-pl":w()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",V,k]}],fill:[{fill:["none",...j()]}],"stroke-w":[{stroke:[I,Xe,Ae,Kt]}],stroke:[{stroke:["none",...j()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}},Sa=ia(Ma);function Y(...t){return Sa(Ps(t))}const ka=Os("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),G=x.forwardRef(({className:t,variant:e,size:n,asChild:s=!1,...r},i)=>{const a=s?Io:"button";return o.jsx(a,{className:Y(ka({variant:e,size:n,className:t})),ref:i,...r})});G.displayName="Button";const Nt=x.forwardRef(({className:t,type:e,...n},s)=>o.jsx("input",{type:e,className:Y("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",t),ref:s,...n}));Nt.displayName="Input";const Wn=t=>{let e;const n=new Set,s=(c,f)=>{const u=typeof c=="function"?c(e):c;if(!Object.is(u,e)){const h=e;e=f??(typeof u!="object"||u===null)?u:Object.assign({},e,u),n.forEach(m=>m(e,h))}},r=()=>e,l={setState:s,getState:r,getInitialState:()=>d,subscribe:c=>(n.add(c),()=>n.delete(c))},d=e=t(s,r,l);return l},Va=t=>t?Wn(t):Wn,Ha=t=>t;function Ra(t,e=Ha){const n=Me.useSyncExternalStore(t.subscribe,()=>e(t.getState()),()=>e(t.getInitialState()));return Me.useDebugValue(n),n}const Pa=t=>{const e=Va(t),n=s=>Ra(e,s);return Object.assign(n,e),n},zs=t=>Pa;function Oa(t,e){let n;try{n=t()}catch{return}return{getItem:r=>{var i;const a=d=>d===null?null:JSON.parse(d,void 0),l=(i=n.getItem(r))!=null?i:null;return l instanceof Promise?l.then(a):a(l)},setItem:(r,i)=>n.setItem(r,JSON.stringify(i,void 0)),removeItem:r=>n.removeItem(r)}}const ln=t=>e=>{try{const n=t(e);return n instanceof Promise?n:{then(s){return ln(s)(n)},catch(s){return this}}}catch(n){return{then(s){return this},catch(s){return ln(s)(n)}}}},La=(t,e)=>(n,s,r)=>{let i={storage:Oa(()=>localStorage),partialize:p=>p,version:0,merge:(p,b)=>({...b,...p}),...e},a=!1;const l=new Set,d=new Set;let c=i.storage;if(!c)return t((...p)=>{console.warn(`[zustand persist middleware] Unable to update item '${i.name}', the given storage is currently unavailable.`),n(...p)},s,r);const f=()=>{const p=i.partialize({...s()});return c.setItem(i.name,{state:p,version:i.version})},u=r.setState;r.setState=(p,b)=>{u(p,b),f()};const h=t((...p)=>{n(...p),f()},s,r);r.getInitialState=()=>h;let m;const g=()=>{var p,b;if(!c)return;a=!1,l.forEach(y=>{var N;return y((N=s())!=null?N:h)});const v=((b=i.onRehydrateStorage)==null?void 0:b.call(i,(p=s())!=null?p:h))||void 0;return ln(c.getItem.bind(c))(i.name).then(y=>{if(y)if(typeof y.version=="number"&&y.version!==i.version){if(i.migrate){const N=i.migrate(y.state,y.version);return N instanceof Promise?N.then(C=>[!0,C]):[!0,N]}console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}else return[!1,y.state];return[!1,void 0]}).then(y=>{var N;const[C,M]=y;if(m=i.merge(M,(N=s())!=null?N:h),n(m,!0),C)return f()}).then(()=>{v==null||v(m,void 0),m=s(),a=!0,d.forEach(y=>y(m))}).catch(y=>{v==null||v(void 0,y)})};return r.persist={setOptions:p=>{i={...i,...p},p.storage&&(c=p.storage)},clearStorage:()=>{c==null||c.removeItem(i.name)},getOptions:()=>i,rehydrate:()=>g(),hasHydrated:()=>a,onHydrate:p=>(l.add(p),()=>{l.delete(p)}),onFinishHydration:p=>(d.add(p),()=>{d.delete(p)})},i.skipHydration||g(),m||h},Bs=La,Ut=()=>typeof window>"u"?"light":window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light",yt=t=>{const e=document.documentElement;e.classList.remove("light","dark"),e.classList.add(t)},cn=zs()(Bs((t,e)=>({theme:"system",actualTheme:Ut(),setTheme:n=>{const s=n==="system"?Ut():n;yt(s),t({theme:n,actualTheme:s})},toggleTheme:()=>{const{actualTheme:n}=e(),s=n==="light"?"dark":"light";yt(s),t({theme:s,actualTheme:s})}}),{name:"theme-storage",onRehydrateStorage:()=>t=>{if(t){const e=t.theme==="system"?Ut():t.theme;yt(e),t.actualTheme=e}}}));typeof window<"u"&&window.matchMedia("(prefers-color-scheme: dark)").addEventListener("change",t=>{const{theme:e}=cn.getState();if(e==="system"){const n=t.matches?"dark":"light";yt(n),cn.setState({actualTheme:n})}});function Ks(t){const e=t+"CollectionProvider",[n,s]=at(e),[r,i]=n(e,{collectionRef:{current:null},itemMap:new Map}),a=p=>{const{scope:b,children:v}=p,y=Me.useRef(null),N=Me.useRef(new Map).current;return o.jsx(r,{scope:b,itemMap:N,collectionRef:y,children:v})};a.displayName=e;const l=t+"CollectionSlot",d=Xt(l),c=Me.forwardRef((p,b)=>{const{scope:v,children:y}=p,N=i(l,v),C=we(b,N.collectionRef);return o.jsx(d,{ref:C,children:y})});c.displayName=l;const f=t+"CollectionItemSlot",u="data-radix-collection-item",h=Xt(f),m=Me.forwardRef((p,b)=>{const{scope:v,children:y,...N}=p,C=Me.useRef(null),M=we(b,C),P=i(f,v);return Me.useEffect(()=>(P.itemMap.set(C,{ref:C,...N}),()=>void P.itemMap.delete(C))),o.jsx(h,{[u]:"",ref:M,children:y})});m.displayName=f;function g(p){const b=i(t+"CollectionConsumer",p);return Me.useCallback(()=>{const y=b.collectionRef.current;if(!y)return[];const N=Array.from(y.querySelectorAll(`[${u}]`));return Array.from(b.itemMap.values()).sort((P,O)=>N.indexOf(P.ref.current)-N.indexOf(O.ref.current))},[b.collectionRef,b.itemMap])}return[{Provider:a,Slot:c,ItemSlot:m},g,s]}var Aa=x.createContext(void 0);function Us(t){const e=x.useContext(Aa);return t||e||"ltr"}const Ta=["top","right","bottom","left"],Re=Math.min,le=Math.max,Ct=Math.round,pt=Math.floor,ve=t=>({x:t,y:t}),Ea={left:"right",right:"left",bottom:"top",top:"bottom"},Ia={start:"end",end:"start"};function dn(t,e,n){return le(t,Re(e,n))}function Se(t,e){return typeof t=="function"?t(e):t}function ke(t){return t.split("-")[0]}function We(t){return t.split("-")[1]}function bn(t){return t==="x"?"y":"x"}function yn(t){return t==="y"?"height":"width"}function be(t){return["top","bottom"].includes(ke(t))?"y":"x"}function vn(t){return bn(be(t))}function Fa(t,e,n){n===void 0&&(n=!1);const s=We(t),r=vn(t),i=yn(r);let a=r==="x"?s===(n?"end":"start")?"right":"left":s==="start"?"bottom":"top";return e.reference[i]>e.floating[i]&&(a=Mt(a)),[a,Mt(a)]}function Da(t){const e=Mt(t);return[un(t),e,un(e)]}function un(t){return t.replace(/start|end/g,e=>Ia[e])}function _a(t,e,n){const s=["left","right"],r=["right","left"],i=["top","bottom"],a=["bottom","top"];switch(t){case"top":case"bottom":return n?e?r:s:e?s:r;case"left":case"right":return e?i:a;default:return[]}}function $a(t,e,n,s){const r=We(t);let i=_a(ke(t),n==="start",s);return r&&(i=i.map(a=>a+"-"+r),e&&(i=i.concat(i.map(un)))),i}function Mt(t){return t.replace(/left|right|bottom|top/g,e=>Ea[e])}function Za(t){return{top:0,right:0,bottom:0,left:0,...t}}function Gs(t){return typeof t!="number"?Za(t):{top:t,right:t,bottom:t,left:t}}function St(t){const{x:e,y:n,width:s,height:r}=t;return{width:s,height:r,top:n,left:e,right:e+s,bottom:n+r,x:e,y:n}}function qn(t,e,n){let{reference:s,floating:r}=t;const i=be(e),a=vn(e),l=yn(a),d=ke(e),c=i==="y",f=s.x+s.width/2-r.width/2,u=s.y+s.height/2-r.height/2,h=s[l]/2-r[l]/2;let m;switch(d){case"top":m={x:f,y:s.y-r.height};break;case"bottom":m={x:f,y:s.y+s.height};break;case"right":m={x:s.x+s.width,y:u};break;case"left":m={x:s.x-r.width,y:u};break;default:m={x:s.x,y:s.y}}switch(We(e)){case"start":m[a]-=h*(n&&c?-1:1);break;case"end":m[a]+=h*(n&&c?-1:1);break}return m}const za=async(t,e,n)=>{const{placement:s="bottom",strategy:r="absolute",middleware:i=[],platform:a}=n,l=i.filter(Boolean),d=await(a.isRTL==null?void 0:a.isRTL(e));let c=await a.getElementRects({reference:t,floating:e,strategy:r}),{x:f,y:u}=qn(c,s,d),h=s,m={},g=0;for(let p=0;p<l.length;p++){const{name:b,fn:v}=l[p],{x:y,y:N,data:C,reset:M}=await v({x:f,y:u,initialPlacement:s,placement:h,strategy:r,middlewareData:m,rects:c,platform:a,elements:{reference:t,floating:e}});f=y??f,u=N??u,m={...m,[b]:{...m[b],...C}},M&&g<=50&&(g++,typeof M=="object"&&(M.placement&&(h=M.placement),M.rects&&(c=M.rects===!0?await a.getElementRects({reference:t,floating:e,strategy:r}):M.rects),{x:f,y:u}=qn(c,h,d)),p=-1)}return{x:f,y:u,placement:h,strategy:r,middlewareData:m}};async function tt(t,e){var n;e===void 0&&(e={});const{x:s,y:r,platform:i,rects:a,elements:l,strategy:d}=t,{boundary:c="clippingAncestors",rootBoundary:f="viewport",elementContext:u="floating",altBoundary:h=!1,padding:m=0}=Se(e,t),g=Gs(m),b=l[h?u==="floating"?"reference":"floating":u],v=St(await i.getClippingRect({element:(n=await(i.isElement==null?void 0:i.isElement(b)))==null||n?b:b.contextElement||await(i.getDocumentElement==null?void 0:i.getDocumentElement(l.floating)),boundary:c,rootBoundary:f,strategy:d})),y=u==="floating"?{x:s,y:r,width:a.floating.width,height:a.floating.height}:a.reference,N=await(i.getOffsetParent==null?void 0:i.getOffsetParent(l.floating)),C=await(i.isElement==null?void 0:i.isElement(N))?await(i.getScale==null?void 0:i.getScale(N))||{x:1,y:1}:{x:1,y:1},M=St(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:l,rect:y,offsetParent:N,strategy:d}):y);return{top:(v.top-M.top+g.top)/C.y,bottom:(M.bottom-v.bottom+g.bottom)/C.y,left:(v.left-M.left+g.left)/C.x,right:(M.right-v.right+g.right)/C.x}}const Ba=t=>({name:"arrow",options:t,async fn(e){const{x:n,y:s,placement:r,rects:i,platform:a,elements:l,middlewareData:d}=e,{element:c,padding:f=0}=Se(t,e)||{};if(c==null)return{};const u=Gs(f),h={x:n,y:s},m=vn(r),g=yn(m),p=await a.getDimensions(c),b=m==="y",v=b?"top":"left",y=b?"bottom":"right",N=b?"clientHeight":"clientWidth",C=i.reference[g]+i.reference[m]-h[m]-i.floating[g],M=h[m]-i.reference[m],P=await(a.getOffsetParent==null?void 0:a.getOffsetParent(c));let O=P?P[N]:0;(!O||!await(a.isElement==null?void 0:a.isElement(P)))&&(O=l.floating[N]||i.floating[g]);const w=C/2-M/2,H=O/2-p[g]/2-1,R=Re(u[v],H),D=Re(u[y],H),z=R,F=O-p[g]-D,T=O/2-p[g]/2+w,K=dn(z,T,F),L=!d.arrow&&We(r)!=null&&T!==K&&i.reference[g]/2-(T<z?R:D)-p[g]/2<0,E=L?T<z?T-z:T-F:0;return{[m]:h[m]+E,data:{[m]:K,centerOffset:T-K-E,...L&&{alignmentOffset:E}},reset:L}}}),Ka=function(t){return t===void 0&&(t={}),{name:"flip",options:t,async fn(e){var n,s;const{placement:r,middlewareData:i,rects:a,initialPlacement:l,platform:d,elements:c}=e,{mainAxis:f=!0,crossAxis:u=!0,fallbackPlacements:h,fallbackStrategy:m="bestFit",fallbackAxisSideDirection:g="none",flipAlignment:p=!0,...b}=Se(t,e);if((n=i.arrow)!=null&&n.alignmentOffset)return{};const v=ke(r),y=be(l),N=ke(l)===l,C=await(d.isRTL==null?void 0:d.isRTL(c.floating)),M=h||(N||!p?[Mt(l)]:Da(l)),P=g!=="none";!h&&P&&M.push(...$a(l,p,g,C));const O=[l,...M],w=await tt(e,b),H=[];let R=((s=i.flip)==null?void 0:s.overflows)||[];if(f&&H.push(w[v]),u){const T=Fa(r,a,C);H.push(w[T[0]],w[T[1]])}if(R=[...R,{placement:r,overflows:H}],!H.every(T=>T<=0)){var D,z;const T=(((D=i.flip)==null?void 0:D.index)||0)+1,K=O[T];if(K&&(!(u==="alignment"?y!==be(K):!1)||R.every(j=>j.overflows[0]>0&&be(j.placement)===y)))return{data:{index:T,overflows:R},reset:{placement:K}};let L=(z=R.filter(E=>E.overflows[0]<=0).sort((E,j)=>E.overflows[1]-j.overflows[1])[0])==null?void 0:z.placement;if(!L)switch(m){case"bestFit":{var F;const E=(F=R.filter(j=>{if(P){const S=be(j.placement);return S===y||S==="y"}return!0}).map(j=>[j.placement,j.overflows.filter(S=>S>0).reduce((S,Z)=>S+Z,0)]).sort((j,S)=>j[1]-S[1])[0])==null?void 0:F[0];E&&(L=E);break}case"initialPlacement":L=l;break}if(r!==L)return{reset:{placement:L}}}return{}}}};function Yn(t,e){return{top:t.top-e.height,right:t.right-e.width,bottom:t.bottom-e.height,left:t.left-e.width}}function Xn(t){return Ta.some(e=>t[e]>=0)}const Ua=function(t){return t===void 0&&(t={}),{name:"hide",options:t,async fn(e){const{rects:n}=e,{strategy:s="referenceHidden",...r}=Se(t,e);switch(s){case"referenceHidden":{const i=await tt(e,{...r,elementContext:"reference"}),a=Yn(i,n.reference);return{data:{referenceHiddenOffsets:a,referenceHidden:Xn(a)}}}case"escaped":{const i=await tt(e,{...r,altBoundary:!0}),a=Yn(i,n.floating);return{data:{escapedOffsets:a,escaped:Xn(a)}}}default:return{}}}}};async function Ga(t,e){const{placement:n,platform:s,elements:r}=t,i=await(s.isRTL==null?void 0:s.isRTL(r.floating)),a=ke(n),l=We(n),d=be(n)==="y",c=["left","top"].includes(a)?-1:1,f=i&&d?-1:1,u=Se(e,t);let{mainAxis:h,crossAxis:m,alignmentAxis:g}=typeof u=="number"?{mainAxis:u,crossAxis:0,alignmentAxis:null}:{mainAxis:u.mainAxis||0,crossAxis:u.crossAxis||0,alignmentAxis:u.alignmentAxis};return l&&typeof g=="number"&&(m=l==="end"?g*-1:g),d?{x:m*f,y:h*c}:{x:h*c,y:m*f}}const Wa=function(t){return t===void 0&&(t=0),{name:"offset",options:t,async fn(e){var n,s;const{x:r,y:i,placement:a,middlewareData:l}=e,d=await Ga(e,t);return a===((n=l.offset)==null?void 0:n.placement)&&(s=l.arrow)!=null&&s.alignmentOffset?{}:{x:r+d.x,y:i+d.y,data:{...d,placement:a}}}}},qa=function(t){return t===void 0&&(t={}),{name:"shift",options:t,async fn(e){const{x:n,y:s,placement:r}=e,{mainAxis:i=!0,crossAxis:a=!1,limiter:l={fn:b=>{let{x:v,y}=b;return{x:v,y}}},...d}=Se(t,e),c={x:n,y:s},f=await tt(e,d),u=be(ke(r)),h=bn(u);let m=c[h],g=c[u];if(i){const b=h==="y"?"top":"left",v=h==="y"?"bottom":"right",y=m+f[b],N=m-f[v];m=dn(y,m,N)}if(a){const b=u==="y"?"top":"left",v=u==="y"?"bottom":"right",y=g+f[b],N=g-f[v];g=dn(y,g,N)}const p=l.fn({...e,[h]:m,[u]:g});return{...p,data:{x:p.x-n,y:p.y-s,enabled:{[h]:i,[u]:a}}}}}},Ya=function(t){return t===void 0&&(t={}),{options:t,fn(e){const{x:n,y:s,placement:r,rects:i,middlewareData:a}=e,{offset:l=0,mainAxis:d=!0,crossAxis:c=!0}=Se(t,e),f={x:n,y:s},u=be(r),h=bn(u);let m=f[h],g=f[u];const p=Se(l,e),b=typeof p=="number"?{mainAxis:p,crossAxis:0}:{mainAxis:0,crossAxis:0,...p};if(d){const N=h==="y"?"height":"width",C=i.reference[h]-i.floating[N]+b.mainAxis,M=i.reference[h]+i.reference[N]-b.mainAxis;m<C?m=C:m>M&&(m=M)}if(c){var v,y;const N=h==="y"?"width":"height",C=["top","left"].includes(ke(r)),M=i.reference[u]-i.floating[N]+(C&&((v=a.offset)==null?void 0:v[u])||0)+(C?0:b.crossAxis),P=i.reference[u]+i.reference[N]+(C?0:((y=a.offset)==null?void 0:y[u])||0)-(C?b.crossAxis:0);g<M?g=M:g>P&&(g=P)}return{[h]:m,[u]:g}}}},Xa=function(t){return t===void 0&&(t={}),{name:"size",options:t,async fn(e){var n,s;const{placement:r,rects:i,platform:a,elements:l}=e,{apply:d=()=>{},...c}=Se(t,e),f=await tt(e,c),u=ke(r),h=We(r),m=be(r)==="y",{width:g,height:p}=i.floating;let b,v;u==="top"||u==="bottom"?(b=u,v=h===(await(a.isRTL==null?void 0:a.isRTL(l.floating))?"start":"end")?"left":"right"):(v=u,b=h==="end"?"top":"bottom");const y=p-f.top-f.bottom,N=g-f.left-f.right,C=Re(p-f[b],y),M=Re(g-f[v],N),P=!e.middlewareData.shift;let O=C,w=M;if((n=e.middlewareData.shift)!=null&&n.enabled.x&&(w=N),(s=e.middlewareData.shift)!=null&&s.enabled.y&&(O=y),P&&!h){const R=le(f.left,0),D=le(f.right,0),z=le(f.top,0),F=le(f.bottom,0);m?w=g-2*(R!==0||D!==0?R+D:le(f.left,f.right)):O=p-2*(z!==0||F!==0?z+F:le(f.top,f.bottom))}await d({...e,availableWidth:w,availableHeight:O});const H=await a.getDimensions(l.floating);return g!==H.width||p!==H.height?{reset:{rects:!0}}:{}}}};function Et(){return typeof window<"u"}function qe(t){return Ws(t)?(t.nodeName||"").toLowerCase():"#document"}function ce(t){var e;return(t==null||(e=t.ownerDocument)==null?void 0:e.defaultView)||window}function Ne(t){var e;return(e=(Ws(t)?t.ownerDocument:t.document)||window.document)==null?void 0:e.documentElement}function Ws(t){return Et()?t instanceof Node||t instanceof ce(t).Node:!1}function he(t){return Et()?t instanceof Element||t instanceof ce(t).Element:!1}function je(t){return Et()?t instanceof HTMLElement||t instanceof ce(t).HTMLElement:!1}function Jn(t){return!Et()||typeof ShadowRoot>"u"?!1:t instanceof ShadowRoot||t instanceof ce(t).ShadowRoot}function lt(t){const{overflow:e,overflowX:n,overflowY:s,display:r}=me(t);return/auto|scroll|overlay|hidden|clip/.test(e+s+n)&&!["inline","contents"].includes(r)}function Ja(t){return["table","td","th"].includes(qe(t))}function It(t){return[":popover-open",":modal"].some(e=>{try{return t.matches(e)}catch{return!1}})}function wn(t){const e=jn(),n=he(t)?me(t):t;return["transform","translate","scale","rotate","perspective"].some(s=>n[s]?n[s]!=="none":!1)||(n.containerType?n.containerType!=="normal":!1)||!e&&(n.backdropFilter?n.backdropFilter!=="none":!1)||!e&&(n.filter?n.filter!=="none":!1)||["transform","translate","scale","rotate","perspective","filter"].some(s=>(n.willChange||"").includes(s))||["paint","layout","strict","content"].some(s=>(n.contain||"").includes(s))}function Qa(t){let e=Pe(t);for(;je(e)&&!Ke(e);){if(wn(e))return e;if(It(e))return null;e=Pe(e)}return null}function jn(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}function Ke(t){return["html","body","#document"].includes(qe(t))}function me(t){return ce(t).getComputedStyle(t)}function Ft(t){return he(t)?{scrollLeft:t.scrollLeft,scrollTop:t.scrollTop}:{scrollLeft:t.scrollX,scrollTop:t.scrollY}}function Pe(t){if(qe(t)==="html")return t;const e=t.assignedSlot||t.parentNode||Jn(t)&&t.host||Ne(t);return Jn(e)?e.host:e}function qs(t){const e=Pe(t);return Ke(e)?t.ownerDocument?t.ownerDocument.body:t.body:je(e)&&lt(e)?e:qs(e)}function nt(t,e,n){var s;e===void 0&&(e=[]),n===void 0&&(n=!0);const r=qs(t),i=r===((s=t.ownerDocument)==null?void 0:s.body),a=ce(r);if(i){const l=fn(a);return e.concat(a,a.visualViewport||[],lt(r)?r:[],l&&n?nt(l):[])}return e.concat(r,nt(r,[],n))}function fn(t){return t.parent&&Object.getPrototypeOf(t.parent)?t.frameElement:null}function Ys(t){const e=me(t);let n=parseFloat(e.width)||0,s=parseFloat(e.height)||0;const r=je(t),i=r?t.offsetWidth:n,a=r?t.offsetHeight:s,l=Ct(n)!==i||Ct(s)!==a;return l&&(n=i,s=a),{width:n,height:s,$:l}}function Nn(t){return he(t)?t:t.contextElement}function Be(t){const e=Nn(t);if(!je(e))return ve(1);const n=e.getBoundingClientRect(),{width:s,height:r,$:i}=Ys(e);let a=(i?Ct(n.width):n.width)/s,l=(i?Ct(n.height):n.height)/r;return(!a||!Number.isFinite(a))&&(a=1),(!l||!Number.isFinite(l))&&(l=1),{x:a,y:l}}const el=ve(0);function Xs(t){const e=ce(t);return!jn()||!e.visualViewport?el:{x:e.visualViewport.offsetLeft,y:e.visualViewport.offsetTop}}function tl(t,e,n){return e===void 0&&(e=!1),!n||e&&n!==ce(t)?!1:e}function Ee(t,e,n,s){e===void 0&&(e=!1),n===void 0&&(n=!1);const r=t.getBoundingClientRect(),i=Nn(t);let a=ve(1);e&&(s?he(s)&&(a=Be(s)):a=Be(t));const l=tl(i,n,s)?Xs(i):ve(0);let d=(r.left+l.x)/a.x,c=(r.top+l.y)/a.y,f=r.width/a.x,u=r.height/a.y;if(i){const h=ce(i),m=s&&he(s)?ce(s):s;let g=h,p=fn(g);for(;p&&s&&m!==g;){const b=Be(p),v=p.getBoundingClientRect(),y=me(p),N=v.left+(p.clientLeft+parseFloat(y.paddingLeft))*b.x,C=v.top+(p.clientTop+parseFloat(y.paddingTop))*b.y;d*=b.x,c*=b.y,f*=b.x,u*=b.y,d+=N,c+=C,g=ce(p),p=fn(g)}}return St({width:f,height:u,x:d,y:c})}function Cn(t,e){const n=Ft(t).scrollLeft;return e?e.left+n:Ee(Ne(t)).left+n}function Js(t,e,n){n===void 0&&(n=!1);const s=t.getBoundingClientRect(),r=s.left+e.scrollLeft-(n?0:Cn(t,s)),i=s.top+e.scrollTop;return{x:r,y:i}}function nl(t){let{elements:e,rect:n,offsetParent:s,strategy:r}=t;const i=r==="fixed",a=Ne(s),l=e?It(e.floating):!1;if(s===a||l&&i)return n;let d={scrollLeft:0,scrollTop:0},c=ve(1);const f=ve(0),u=je(s);if((u||!u&&!i)&&((qe(s)!=="body"||lt(a))&&(d=Ft(s)),je(s))){const m=Ee(s);c=Be(s),f.x=m.x+s.clientLeft,f.y=m.y+s.clientTop}const h=a&&!u&&!i?Js(a,d,!0):ve(0);return{width:n.width*c.x,height:n.height*c.y,x:n.x*c.x-d.scrollLeft*c.x+f.x+h.x,y:n.y*c.y-d.scrollTop*c.y+f.y+h.y}}function sl(t){return Array.from(t.getClientRects())}function rl(t){const e=Ne(t),n=Ft(t),s=t.ownerDocument.body,r=le(e.scrollWidth,e.clientWidth,s.scrollWidth,s.clientWidth),i=le(e.scrollHeight,e.clientHeight,s.scrollHeight,s.clientHeight);let a=-n.scrollLeft+Cn(t);const l=-n.scrollTop;return me(s).direction==="rtl"&&(a+=le(e.clientWidth,s.clientWidth)-r),{width:r,height:i,x:a,y:l}}function ol(t,e){const n=ce(t),s=Ne(t),r=n.visualViewport;let i=s.clientWidth,a=s.clientHeight,l=0,d=0;if(r){i=r.width,a=r.height;const c=jn();(!c||c&&e==="fixed")&&(l=r.offsetLeft,d=r.offsetTop)}return{width:i,height:a,x:l,y:d}}function il(t,e){const n=Ee(t,!0,e==="fixed"),s=n.top+t.clientTop,r=n.left+t.clientLeft,i=je(t)?Be(t):ve(1),a=t.clientWidth*i.x,l=t.clientHeight*i.y,d=r*i.x,c=s*i.y;return{width:a,height:l,x:d,y:c}}function Qn(t,e,n){let s;if(e==="viewport")s=ol(t,n);else if(e==="document")s=rl(Ne(t));else if(he(e))s=il(e,n);else{const r=Xs(t);s={x:e.x-r.x,y:e.y-r.y,width:e.width,height:e.height}}return St(s)}function Qs(t,e){const n=Pe(t);return n===e||!he(n)||Ke(n)?!1:me(n).position==="fixed"||Qs(n,e)}function al(t,e){const n=e.get(t);if(n)return n;let s=nt(t,[],!1).filter(l=>he(l)&&qe(l)!=="body"),r=null;const i=me(t).position==="fixed";let a=i?Pe(t):t;for(;he(a)&&!Ke(a);){const l=me(a),d=wn(a);!d&&l.position==="fixed"&&(r=null),(i?!d&&!r:!d&&l.position==="static"&&!!r&&["absolute","fixed"].includes(r.position)||lt(a)&&!d&&Qs(t,a))?s=s.filter(f=>f!==a):r=l,a=Pe(a)}return e.set(t,s),s}function ll(t){let{element:e,boundary:n,rootBoundary:s,strategy:r}=t;const a=[...n==="clippingAncestors"?It(e)?[]:al(e,this._c):[].concat(n),s],l=a[0],d=a.reduce((c,f)=>{const u=Qn(e,f,r);return c.top=le(u.top,c.top),c.right=Re(u.right,c.right),c.bottom=Re(u.bottom,c.bottom),c.left=le(u.left,c.left),c},Qn(e,l,r));return{width:d.right-d.left,height:d.bottom-d.top,x:d.left,y:d.top}}function cl(t){const{width:e,height:n}=Ys(t);return{width:e,height:n}}function dl(t,e,n){const s=je(e),r=Ne(e),i=n==="fixed",a=Ee(t,!0,i,e);let l={scrollLeft:0,scrollTop:0};const d=ve(0);function c(){d.x=Cn(r)}if(s||!s&&!i)if((qe(e)!=="body"||lt(r))&&(l=Ft(e)),s){const m=Ee(e,!0,i,e);d.x=m.x+e.clientLeft,d.y=m.y+e.clientTop}else r&&c();i&&!s&&r&&c();const f=r&&!s&&!i?Js(r,l):ve(0),u=a.left+l.scrollLeft-d.x-f.x,h=a.top+l.scrollTop-d.y-f.y;return{x:u,y:h,width:a.width,height:a.height}}function Gt(t){return me(t).position==="static"}function es(t,e){if(!je(t)||me(t).position==="fixed")return null;if(e)return e(t);let n=t.offsetParent;return Ne(t)===n&&(n=n.ownerDocument.body),n}function er(t,e){const n=ce(t);if(It(t))return n;if(!je(t)){let r=Pe(t);for(;r&&!Ke(r);){if(he(r)&&!Gt(r))return r;r=Pe(r)}return n}let s=es(t,e);for(;s&&Ja(s)&&Gt(s);)s=es(s,e);return s&&Ke(s)&&Gt(s)&&!wn(s)?n:s||Qa(t)||n}const ul=async function(t){const e=this.getOffsetParent||er,n=this.getDimensions,s=await n(t.floating);return{reference:dl(t.reference,await e(t.floating),t.strategy),floating:{x:0,y:0,width:s.width,height:s.height}}};function fl(t){return me(t).direction==="rtl"}const hl={convertOffsetParentRelativeRectToViewportRelativeRect:nl,getDocumentElement:Ne,getClippingRect:ll,getOffsetParent:er,getElementRects:ul,getClientRects:sl,getDimensions:cl,getScale:Be,isElement:he,isRTL:fl};function tr(t,e){return t.x===e.x&&t.y===e.y&&t.width===e.width&&t.height===e.height}function ml(t,e){let n=null,s;const r=Ne(t);function i(){var l;clearTimeout(s),(l=n)==null||l.disconnect(),n=null}function a(l,d){l===void 0&&(l=!1),d===void 0&&(d=1),i();const c=t.getBoundingClientRect(),{left:f,top:u,width:h,height:m}=c;if(l||e(),!h||!m)return;const g=pt(u),p=pt(r.clientWidth-(f+h)),b=pt(r.clientHeight-(u+m)),v=pt(f),N={rootMargin:-g+"px "+-p+"px "+-b+"px "+-v+"px",threshold:le(0,Re(1,d))||1};let C=!0;function M(P){const O=P[0].intersectionRatio;if(O!==d){if(!C)return a();O?a(!1,O):s=setTimeout(()=>{a(!1,1e-7)},1e3)}O===1&&!tr(c,t.getBoundingClientRect())&&a(),C=!1}try{n=new IntersectionObserver(M,{...N,root:r.ownerDocument})}catch{n=new IntersectionObserver(M,N)}n.observe(t)}return a(!0),i}function gl(t,e,n,s){s===void 0&&(s={});const{ancestorScroll:r=!0,ancestorResize:i=!0,elementResize:a=typeof ResizeObserver=="function",layoutShift:l=typeof IntersectionObserver=="function",animationFrame:d=!1}=s,c=Nn(t),f=r||i?[...c?nt(c):[],...nt(e)]:[];f.forEach(v=>{r&&v.addEventListener("scroll",n,{passive:!0}),i&&v.addEventListener("resize",n)});const u=c&&l?ml(c,n):null;let h=-1,m=null;a&&(m=new ResizeObserver(v=>{let[y]=v;y&&y.target===c&&m&&(m.unobserve(e),cancelAnimationFrame(h),h=requestAnimationFrame(()=>{var N;(N=m)==null||N.observe(e)})),n()}),c&&!d&&m.observe(c),m.observe(e));let g,p=d?Ee(t):null;d&&b();function b(){const v=Ee(t);p&&!tr(p,v)&&n(),p=v,g=requestAnimationFrame(b)}return n(),()=>{var v;f.forEach(y=>{r&&y.removeEventListener("scroll",n),i&&y.removeEventListener("resize",n)}),u==null||u(),(v=m)==null||v.disconnect(),m=null,d&&cancelAnimationFrame(g)}}const pl=Wa,xl=qa,bl=Ka,yl=Xa,vl=Ua,ts=Ba,wl=Ya,jl=(t,e,n)=>{const s=new Map,r={platform:hl,...n},i={...r.platform,_c:s};return za(t,e,{...r,platform:i})};var Nl=typeof document<"u",Cl=function(){},vt=Nl?x.useLayoutEffect:Cl;function kt(t,e){if(t===e)return!0;if(typeof t!=typeof e)return!1;if(typeof t=="function"&&t.toString()===e.toString())return!0;let n,s,r;if(t&&e&&typeof t=="object"){if(Array.isArray(t)){if(n=t.length,n!==e.length)return!1;for(s=n;s--!==0;)if(!kt(t[s],e[s]))return!1;return!0}if(r=Object.keys(t),n=r.length,n!==Object.keys(e).length)return!1;for(s=n;s--!==0;)if(!{}.hasOwnProperty.call(e,r[s]))return!1;for(s=n;s--!==0;){const i=r[s];if(!(i==="_owner"&&t.$$typeof)&&!kt(t[i],e[i]))return!1}return!0}return t!==t&&e!==e}function nr(t){return typeof window>"u"?1:(t.ownerDocument.defaultView||window).devicePixelRatio||1}function ns(t,e){const n=nr(t);return Math.round(e*n)/n}function Wt(t){const e=x.useRef(t);return vt(()=>{e.current=t}),e}function Ml(t){t===void 0&&(t={});const{placement:e="bottom",strategy:n="absolute",middleware:s=[],platform:r,elements:{reference:i,floating:a}={},transform:l=!0,whileElementsMounted:d,open:c}=t,[f,u]=x.useState({x:0,y:0,strategy:n,placement:e,middlewareData:{},isPositioned:!1}),[h,m]=x.useState(s);kt(h,s)||m(s);const[g,p]=x.useState(null),[b,v]=x.useState(null),y=x.useCallback(j=>{j!==P.current&&(P.current=j,p(j))},[]),N=x.useCallback(j=>{j!==O.current&&(O.current=j,v(j))},[]),C=i||g,M=a||b,P=x.useRef(null),O=x.useRef(null),w=x.useRef(f),H=d!=null,R=Wt(d),D=Wt(r),z=Wt(c),F=x.useCallback(()=>{if(!P.current||!O.current)return;const j={placement:e,strategy:n,middleware:h};D.current&&(j.platform=D.current),jl(P.current,O.current,j).then(S=>{const Z={...S,isPositioned:z.current!==!1};T.current&&!kt(w.current,Z)&&(w.current=Z,Po.flushSync(()=>{u(Z)}))})},[h,e,n,D,z]);vt(()=>{c===!1&&w.current.isPositioned&&(w.current.isPositioned=!1,u(j=>({...j,isPositioned:!1})))},[c]);const T=x.useRef(!1);vt(()=>(T.current=!0,()=>{T.current=!1}),[]),vt(()=>{if(C&&(P.current=C),M&&(O.current=M),C&&M){if(R.current)return R.current(C,M,F);F()}},[C,M,F,R,H]);const K=x.useMemo(()=>({reference:P,floating:O,setReference:y,setFloating:N}),[y,N]),L=x.useMemo(()=>({reference:C,floating:M}),[C,M]),E=x.useMemo(()=>{const j={position:n,left:0,top:0};if(!L.floating)return j;const S=ns(L.floating,f.x),Z=ns(L.floating,f.y);return l?{...j,transform:"translate("+S+"px, "+Z+"px)",...nr(L.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:S,top:Z}},[n,l,L.floating,f.x,f.y]);return x.useMemo(()=>({...f,update:F,refs:K,elements:L,floatingStyles:E}),[f,F,K,L,E])}const Sl=t=>{function e(n){return{}.hasOwnProperty.call(n,"current")}return{name:"arrow",options:t,fn(n){const{element:s,padding:r}=typeof t=="function"?t(n):t;return s&&e(s)?s.current!=null?ts({element:s.current,padding:r}).fn(n):{}:s?ts({element:s,padding:r}).fn(n):{}}}},kl=(t,e)=>({...pl(t),options:[t,e]}),Vl=(t,e)=>({...xl(t),options:[t,e]}),Hl=(t,e)=>({...wl(t),options:[t,e]}),Rl=(t,e)=>({...bl(t),options:[t,e]}),Pl=(t,e)=>({...yl(t),options:[t,e]}),Ol=(t,e)=>({...vl(t),options:[t,e]}),Ll=(t,e)=>({...Sl(t),options:[t,e]});var Al="Arrow",sr=x.forwardRef((t,e)=>{const{children:n,width:s=10,height:r=5,...i}=t;return o.jsx(fe.svg,{...i,ref:e,width:s,height:r,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:t.asChild?n:o.jsx("polygon",{points:"0,0 30,0 15,10"})})});sr.displayName=Al;var Tl=sr;function El(t){const[e,n]=x.useState(void 0);return Jt(()=>{if(t){n({width:t.offsetWidth,height:t.offsetHeight});const s=new ResizeObserver(r=>{if(!Array.isArray(r)||!r.length)return;const i=r[0];let a,l;if("borderBoxSize"in i){const d=i.borderBoxSize,c=Array.isArray(d)?d[0]:d;a=c.inlineSize,l=c.blockSize}else a=t.offsetWidth,l=t.offsetHeight;n({width:a,height:l})});return s.observe(t,{box:"border-box"}),()=>s.unobserve(t)}else n(void 0)},[t]),e}var Mn="Popper",[rr,or]=at(Mn),[Il,ir]=rr(Mn),ar=t=>{const{__scopePopper:e,children:n}=t,[s,r]=x.useState(null);return o.jsx(Il,{scope:e,anchor:s,onAnchorChange:r,children:n})};ar.displayName=Mn;var lr="PopperAnchor",cr=x.forwardRef((t,e)=>{const{__scopePopper:n,virtualRef:s,...r}=t,i=ir(lr,n),a=x.useRef(null),l=we(e,a);return x.useEffect(()=>{i.onAnchorChange((s==null?void 0:s.current)||a.current)}),s?null:o.jsx(fe.div,{...r,ref:l})});cr.displayName=lr;var Sn="PopperContent",[Fl,Dl]=rr(Sn),dr=x.forwardRef((t,e)=>{var B,Ve,ne,pe,De,Le;const{__scopePopper:n,side:s="bottom",sideOffset:r=0,align:i="center",alignOffset:a=0,arrowPadding:l=0,avoidCollisions:d=!0,collisionBoundary:c=[],collisionPadding:f=0,sticky:u="partial",hideWhenDetached:h=!1,updatePositionStrategy:m="optimized",onPlaced:g,...p}=t,b=ir(Sn,n),[v,y]=x.useState(null),N=we(e,Ye=>y(Ye)),[C,M]=x.useState(null),P=El(C),O=(P==null?void 0:P.width)??0,w=(P==null?void 0:P.height)??0,H=s+(i!=="center"?"-"+i:""),R=typeof f=="number"?f:{top:0,right:0,bottom:0,left:0,...f},D=Array.isArray(c)?c:[c],z=D.length>0,F={padding:R,boundary:D.filter($l),altBoundary:z},{refs:T,floatingStyles:K,placement:L,isPositioned:E,middlewareData:j}=Ml({strategy:"fixed",placement:H,whileElementsMounted:(...Ye)=>gl(...Ye,{animationFrame:m==="always"}),elements:{reference:b.anchor},middleware:[kl({mainAxis:r+w,alignmentAxis:a}),d&&Vl({mainAxis:!0,crossAxis:!1,limiter:u==="partial"?Hl():void 0,...F}),d&&Rl({...F}),Pl({...F,apply:({elements:Ye,rects:An,availableWidth:Mo,availableHeight:So})=>{const{width:ko,height:Vo}=An.reference,ft=Ye.floating.style;ft.setProperty("--radix-popper-available-width",`${Mo}px`),ft.setProperty("--radix-popper-available-height",`${So}px`),ft.setProperty("--radix-popper-anchor-width",`${ko}px`),ft.setProperty("--radix-popper-anchor-height",`${Vo}px`)}}),C&&Ll({element:C,padding:l}),Zl({arrowWidth:O,arrowHeight:w}),h&&Ol({strategy:"referenceHidden",...F})]}),[S,Z]=hr(L),_=At(g);Jt(()=>{E&&(_==null||_())},[E,_]);const W=(B=j.arrow)==null?void 0:B.x,U=(Ve=j.arrow)==null?void 0:Ve.y,q=((ne=j.arrow)==null?void 0:ne.centerOffset)!==0,[de,ge]=x.useState();return Jt(()=>{v&&ge(window.getComputedStyle(v).zIndex)},[v]),o.jsx("div",{ref:T.setFloating,"data-radix-popper-content-wrapper":"",style:{...K,transform:E?K.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:de,"--radix-popper-transform-origin":[(pe=j.transformOrigin)==null?void 0:pe.x,(De=j.transformOrigin)==null?void 0:De.y].join(" "),...((Le=j.hide)==null?void 0:Le.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:t.dir,children:o.jsx(Fl,{scope:n,placedSide:S,onArrowChange:M,arrowX:W,arrowY:U,shouldHideArrow:q,children:o.jsx(fe.div,{"data-side":S,"data-align":Z,...p,ref:N,style:{...p.style,animation:E?void 0:"none"}})})})});dr.displayName=Sn;var ur="PopperArrow",_l={top:"bottom",right:"left",bottom:"top",left:"right"},fr=x.forwardRef(function(e,n){const{__scopePopper:s,...r}=e,i=Dl(ur,s),a=_l[i.placedSide];return o.jsx("span",{ref:i.onArrowChange,style:{position:"absolute",left:i.arrowX,top:i.arrowY,[a]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[i.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[i.placedSide],visibility:i.shouldHideArrow?"hidden":void 0},children:o.jsx(Tl,{...r,ref:n,style:{...r.style,display:"block"}})})});fr.displayName=ur;function $l(t){return t!==null}var Zl=t=>({name:"transformOrigin",options:t,fn(e){var b,v,y;const{placement:n,rects:s,middlewareData:r}=e,a=((b=r.arrow)==null?void 0:b.centerOffset)!==0,l=a?0:t.arrowWidth,d=a?0:t.arrowHeight,[c,f]=hr(n),u={start:"0%",center:"50%",end:"100%"}[f],h=(((v=r.arrow)==null?void 0:v.x)??0)+l/2,m=(((y=r.arrow)==null?void 0:y.y)??0)+d/2;let g="",p="";return c==="bottom"?(g=a?u:`${h}px`,p=`${-d}px`):c==="top"?(g=a?u:`${h}px`,p=`${s.floating.height+d}px`):c==="right"?(g=`${-d}px`,p=a?u:`${m}px`):c==="left"&&(g=`${s.floating.width+d}px`,p=a?u:`${m}px`),{data:{x:g,y:p}}}});function hr(t){const[e,n="center"]=t.split("-");return[e,n]}var zl=ar,Bl=cr,Kl=dr,Ul=fr,qt="rovingFocusGroup.onEntryFocus",Gl={bubbles:!1,cancelable:!0},ct="RovingFocusGroup",[hn,mr,Wl]=Ks(ct),[ql,gr]=at(ct,[Wl]),[Yl,Xl]=ql(ct),pr=x.forwardRef((t,e)=>o.jsx(hn.Provider,{scope:t.__scopeRovingFocusGroup,children:o.jsx(hn.Slot,{scope:t.__scopeRovingFocusGroup,children:o.jsx(Jl,{...t,ref:e})})}));pr.displayName=ct;var Jl=x.forwardRef((t,e)=>{const{__scopeRovingFocusGroup:n,orientation:s,loop:r=!1,dir:i,currentTabStopId:a,defaultCurrentTabStopId:l,onCurrentTabStopIdChange:d,onEntryFocus:c,preventScrollOnEntryFocus:f=!1,...u}=t,h=x.useRef(null),m=we(e,h),g=Us(i),[p,b]=Cs({prop:a,defaultProp:l??null,onChange:d,caller:ct}),[v,y]=x.useState(!1),N=At(c),C=mr(n),M=x.useRef(!1),[P,O]=x.useState(0);return x.useEffect(()=>{const w=h.current;if(w)return w.addEventListener(qt,N),()=>w.removeEventListener(qt,N)},[N]),o.jsx(Yl,{scope:n,orientation:s,dir:g,loop:r,currentTabStopId:p,onItemFocus:x.useCallback(w=>b(w),[b]),onItemShiftTab:x.useCallback(()=>y(!0),[]),onFocusableItemAdd:x.useCallback(()=>O(w=>w+1),[]),onFocusableItemRemove:x.useCallback(()=>O(w=>w-1),[]),children:o.jsx(fe.div,{tabIndex:v||P===0?-1:0,"data-orientation":s,...u,ref:m,style:{outline:"none",...t.style},onMouseDown:$(t.onMouseDown,()=>{M.current=!0}),onFocus:$(t.onFocus,w=>{const H=!M.current;if(w.target===w.currentTarget&&H&&!v){const R=new CustomEvent(qt,Gl);if(w.currentTarget.dispatchEvent(R),!R.defaultPrevented){const D=C().filter(L=>L.focusable),z=D.find(L=>L.active),F=D.find(L=>L.id===p),K=[z,F,...D].filter(Boolean).map(L=>L.ref.current);yr(K,f)}}M.current=!1}),onBlur:$(t.onBlur,()=>y(!1))})})}),xr="RovingFocusGroupItem",br=x.forwardRef((t,e)=>{const{__scopeRovingFocusGroup:n,focusable:s=!0,active:r=!1,tabStopId:i,children:a,...l}=t,d=Qt(),c=i||d,f=Xl(xr,n),u=f.currentTabStopId===c,h=mr(n),{onFocusableItemAdd:m,onFocusableItemRemove:g,currentTabStopId:p}=f;return x.useEffect(()=>{if(s)return m(),()=>g()},[s,m,g]),o.jsx(hn.ItemSlot,{scope:n,id:c,focusable:s,active:r,children:o.jsx(fe.span,{tabIndex:u?0:-1,"data-orientation":f.orientation,...l,ref:e,onMouseDown:$(t.onMouseDown,b=>{s?f.onItemFocus(c):b.preventDefault()}),onFocus:$(t.onFocus,()=>f.onItemFocus(c)),onKeyDown:$(t.onKeyDown,b=>{if(b.key==="Tab"&&b.shiftKey){f.onItemShiftTab();return}if(b.target!==b.currentTarget)return;const v=tc(b,f.orientation,f.dir);if(v!==void 0){if(b.metaKey||b.ctrlKey||b.altKey||b.shiftKey)return;b.preventDefault();let N=h().filter(C=>C.focusable).map(C=>C.ref.current);if(v==="last")N.reverse();else if(v==="prev"||v==="next"){v==="prev"&&N.reverse();const C=N.indexOf(b.currentTarget);N=f.loop?nc(N,C+1):N.slice(C+1)}setTimeout(()=>yr(N))}}),children:typeof a=="function"?a({isCurrentTabStop:u,hasTabStop:p!=null}):a})})});br.displayName=xr;var Ql={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function ec(t,e){return e!=="rtl"?t:t==="ArrowLeft"?"ArrowRight":t==="ArrowRight"?"ArrowLeft":t}function tc(t,e,n){const s=ec(t.key,n);if(!(e==="vertical"&&["ArrowLeft","ArrowRight"].includes(s))&&!(e==="horizontal"&&["ArrowUp","ArrowDown"].includes(s)))return Ql[s]}function yr(t,e=!1){const n=document.activeElement;for(const s of t)if(s===n||(s.focus({preventScroll:e}),document.activeElement!==n))return}function nc(t,e){return t.map((n,s)=>t[(e+s)%t.length])}var sc=pr,rc=br,mn=["Enter"," "],oc=["ArrowDown","PageUp","Home"],vr=["ArrowUp","PageDown","End"],ic=[...oc,...vr],ac={ltr:[...mn,"ArrowRight"],rtl:[...mn,"ArrowLeft"]},lc={ltr:["ArrowLeft"],rtl:["ArrowRight"]},dt="Menu",[st,cc,dc]=Ks(dt),[Ie,wr]=at(dt,[dc,or,gr]),Dt=or(),jr=gr(),[uc,Fe]=Ie(dt),[fc,ut]=Ie(dt),Nr=t=>{const{__scopeMenu:e,open:n=!1,children:s,dir:r,onOpenChange:i,modal:a=!0}=t,l=Dt(e),[d,c]=x.useState(null),f=x.useRef(!1),u=At(i),h=Us(r);return x.useEffect(()=>{const m=()=>{f.current=!0,document.addEventListener("pointerdown",g,{capture:!0,once:!0}),document.addEventListener("pointermove",g,{capture:!0,once:!0})},g=()=>f.current=!1;return document.addEventListener("keydown",m,{capture:!0}),()=>{document.removeEventListener("keydown",m,{capture:!0}),document.removeEventListener("pointerdown",g,{capture:!0}),document.removeEventListener("pointermove",g,{capture:!0})}},[]),o.jsx(zl,{...l,children:o.jsx(uc,{scope:e,open:n,onOpenChange:u,content:d,onContentChange:c,children:o.jsx(fc,{scope:e,onClose:x.useCallback(()=>u(!1),[u]),isUsingKeyboardRef:f,dir:h,modal:a,children:s})})})};Nr.displayName=dt;var hc="MenuAnchor",kn=x.forwardRef((t,e)=>{const{__scopeMenu:n,...s}=t,r=Dt(n);return o.jsx(Bl,{...r,...s,ref:e})});kn.displayName=hc;var Vn="MenuPortal",[mc,Cr]=Ie(Vn,{forceMount:void 0}),Mr=t=>{const{__scopeMenu:e,forceMount:n,children:s,container:r}=t,i=Fe(Vn,e);return o.jsx(mc,{scope:e,forceMount:n,children:o.jsx(Tt,{present:n||i.open,children:o.jsx(Fo,{asChild:!0,container:r,children:s})})})};Mr.displayName=Vn;var ue="MenuContent",[gc,Hn]=Ie(ue),Sr=x.forwardRef((t,e)=>{const n=Cr(ue,t.__scopeMenu),{forceMount:s=n.forceMount,...r}=t,i=Fe(ue,t.__scopeMenu),a=ut(ue,t.__scopeMenu);return o.jsx(st.Provider,{scope:t.__scopeMenu,children:o.jsx(Tt,{present:s||i.open,children:o.jsx(st.Slot,{scope:t.__scopeMenu,children:a.modal?o.jsx(pc,{...r,ref:e}):o.jsx(xc,{...r,ref:e})})})})}),pc=x.forwardRef((t,e)=>{const n=Fe(ue,t.__scopeMenu),s=x.useRef(null),r=we(e,s);return x.useEffect(()=>{const i=s.current;if(i)return zo(i)},[]),o.jsx(Rn,{...t,ref:r,trapFocus:n.open,disableOutsidePointerEvents:n.open,disableOutsideScroll:!0,onFocusOutside:$(t.onFocusOutside,i=>i.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>n.onOpenChange(!1)})}),xc=x.forwardRef((t,e)=>{const n=Fe(ue,t.__scopeMenu);return o.jsx(Rn,{...t,ref:e,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>n.onOpenChange(!1)})}),bc=Xt("MenuContent.ScrollLock"),Rn=x.forwardRef((t,e)=>{const{__scopeMenu:n,loop:s=!1,trapFocus:r,onOpenAutoFocus:i,onCloseAutoFocus:a,disableOutsidePointerEvents:l,onEntryFocus:d,onEscapeKeyDown:c,onPointerDownOutside:f,onFocusOutside:u,onInteractOutside:h,onDismiss:m,disableOutsideScroll:g,...p}=t,b=Fe(ue,n),v=ut(ue,n),y=Dt(n),N=jr(n),C=cc(n),[M,P]=x.useState(null),O=x.useRef(null),w=we(e,O,b.onContentChange),H=x.useRef(0),R=x.useRef(""),D=x.useRef(0),z=x.useRef(null),F=x.useRef("right"),T=x.useRef(0),K=g?_o:x.Fragment,L=g?{as:bc,allowPinchZoom:!0}:void 0,E=S=>{var B,Ve;const Z=R.current+S,_=C().filter(ne=>!ne.disabled),W=document.activeElement,U=(B=_.find(ne=>ne.ref.current===W))==null?void 0:B.textValue,q=_.map(ne=>ne.textValue),de=Rc(q,Z,U),ge=(Ve=_.find(ne=>ne.textValue===de))==null?void 0:Ve.ref.current;(function ne(pe){R.current=pe,window.clearTimeout(H.current),pe!==""&&(H.current=window.setTimeout(()=>ne(""),1e3))})(Z),ge&&setTimeout(()=>ge.focus())};x.useEffect(()=>()=>window.clearTimeout(H.current),[]),Do();const j=x.useCallback(S=>{var _,W;return F.current===((_=z.current)==null?void 0:_.side)&&Oc(S,(W=z.current)==null?void 0:W.area)},[]);return o.jsx(gc,{scope:n,searchRef:R,onItemEnter:x.useCallback(S=>{j(S)&&S.preventDefault()},[j]),onItemLeave:x.useCallback(S=>{var Z;j(S)||((Z=O.current)==null||Z.focus(),P(null))},[j]),onTriggerLeave:x.useCallback(S=>{j(S)&&S.preventDefault()},[j]),pointerGraceTimerRef:D,onPointerGraceIntentChange:x.useCallback(S=>{z.current=S},[]),children:o.jsx(K,{...L,children:o.jsx($o,{asChild:!0,trapped:r,onMountAutoFocus:$(i,S=>{var Z;S.preventDefault(),(Z=O.current)==null||Z.focus({preventScroll:!0})}),onUnmountAutoFocus:a,children:o.jsx(Zo,{asChild:!0,disableOutsidePointerEvents:l,onEscapeKeyDown:c,onPointerDownOutside:f,onFocusOutside:u,onInteractOutside:h,onDismiss:m,children:o.jsx(sc,{asChild:!0,...N,dir:v.dir,orientation:"vertical",loop:s,currentTabStopId:M,onCurrentTabStopIdChange:P,onEntryFocus:$(d,S=>{v.isUsingKeyboardRef.current||S.preventDefault()}),preventScrollOnEntryFocus:!0,children:o.jsx(Kl,{role:"menu","aria-orientation":"vertical","data-state":Zr(b.open),"data-radix-menu-content":"",dir:v.dir,...y,...p,ref:w,style:{outline:"none",...p.style},onKeyDown:$(p.onKeyDown,S=>{const _=S.target.closest("[data-radix-menu-content]")===S.currentTarget,W=S.ctrlKey||S.altKey||S.metaKey,U=S.key.length===1;_&&(S.key==="Tab"&&S.preventDefault(),!W&&U&&E(S.key));const q=O.current;if(S.target!==q||!ic.includes(S.key))return;S.preventDefault();const ge=C().filter(B=>!B.disabled).map(B=>B.ref.current);vr.includes(S.key)&&ge.reverse(),Vc(ge)}),onBlur:$(t.onBlur,S=>{S.currentTarget.contains(S.target)||(window.clearTimeout(H.current),R.current="")}),onPointerMove:$(t.onPointerMove,rt(S=>{const Z=S.target,_=T.current!==S.clientX;if(S.currentTarget.contains(Z)&&_){const W=S.clientX>T.current?"right":"left";F.current=W,T.current=S.clientX}}))})})})})})})});Sr.displayName=ue;var yc="MenuGroup",Pn=x.forwardRef((t,e)=>{const{__scopeMenu:n,...s}=t;return o.jsx(fe.div,{role:"group",...s,ref:e})});Pn.displayName=yc;var vc="MenuLabel",kr=x.forwardRef((t,e)=>{const{__scopeMenu:n,...s}=t;return o.jsx(fe.div,{...s,ref:e})});kr.displayName=vc;var Vt="MenuItem",ss="menu.itemSelect",_t=x.forwardRef((t,e)=>{const{disabled:n=!1,onSelect:s,...r}=t,i=x.useRef(null),a=ut(Vt,t.__scopeMenu),l=Hn(Vt,t.__scopeMenu),d=we(e,i),c=x.useRef(!1),f=()=>{const u=i.current;if(!n&&u){const h=new CustomEvent(ss,{bubbles:!0,cancelable:!0});u.addEventListener(ss,m=>s==null?void 0:s(m),{once:!0}),Bo(u,h),h.defaultPrevented?c.current=!1:a.onClose()}};return o.jsx(Vr,{...r,ref:d,disabled:n,onClick:$(t.onClick,f),onPointerDown:u=>{var h;(h=t.onPointerDown)==null||h.call(t,u),c.current=!0},onPointerUp:$(t.onPointerUp,u=>{var h;c.current||(h=u.currentTarget)==null||h.click()}),onKeyDown:$(t.onKeyDown,u=>{const h=l.searchRef.current!=="";n||h&&u.key===" "||mn.includes(u.key)&&(u.currentTarget.click(),u.preventDefault())})})});_t.displayName=Vt;var Vr=x.forwardRef((t,e)=>{const{__scopeMenu:n,disabled:s=!1,textValue:r,...i}=t,a=Hn(Vt,n),l=jr(n),d=x.useRef(null),c=we(e,d),[f,u]=x.useState(!1),[h,m]=x.useState("");return x.useEffect(()=>{const g=d.current;g&&m((g.textContent??"").trim())},[i.children]),o.jsx(st.ItemSlot,{scope:n,disabled:s,textValue:r??h,children:o.jsx(rc,{asChild:!0,...l,focusable:!s,children:o.jsx(fe.div,{role:"menuitem","data-highlighted":f?"":void 0,"aria-disabled":s||void 0,"data-disabled":s?"":void 0,...i,ref:c,onPointerMove:$(t.onPointerMove,rt(g=>{s?a.onItemLeave(g):(a.onItemEnter(g),g.defaultPrevented||g.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:$(t.onPointerLeave,rt(g=>a.onItemLeave(g))),onFocus:$(t.onFocus,()=>u(!0)),onBlur:$(t.onBlur,()=>u(!1))})})})}),wc="MenuCheckboxItem",Hr=x.forwardRef((t,e)=>{const{checked:n=!1,onCheckedChange:s,...r}=t;return o.jsx(Ar,{scope:t.__scopeMenu,checked:n,children:o.jsx(_t,{role:"menuitemcheckbox","aria-checked":Ht(n)?"mixed":n,...r,ref:e,"data-state":Ln(n),onSelect:$(r.onSelect,()=>s==null?void 0:s(Ht(n)?!0:!n),{checkForDefaultPrevented:!1})})})});Hr.displayName=wc;var Rr="MenuRadioGroup",[jc,Nc]=Ie(Rr,{value:void 0,onValueChange:()=>{}}),Pr=x.forwardRef((t,e)=>{const{value:n,onValueChange:s,...r}=t,i=At(s);return o.jsx(jc,{scope:t.__scopeMenu,value:n,onValueChange:i,children:o.jsx(Pn,{...r,ref:e})})});Pr.displayName=Rr;var Or="MenuRadioItem",Lr=x.forwardRef((t,e)=>{const{value:n,...s}=t,r=Nc(Or,t.__scopeMenu),i=n===r.value;return o.jsx(Ar,{scope:t.__scopeMenu,checked:i,children:o.jsx(_t,{role:"menuitemradio","aria-checked":i,...s,ref:e,"data-state":Ln(i),onSelect:$(s.onSelect,()=>{var a;return(a=r.onValueChange)==null?void 0:a.call(r,n)},{checkForDefaultPrevented:!1})})})});Lr.displayName=Or;var On="MenuItemIndicator",[Ar,Cc]=Ie(On,{checked:!1}),Tr=x.forwardRef((t,e)=>{const{__scopeMenu:n,forceMount:s,...r}=t,i=Cc(On,n);return o.jsx(Tt,{present:s||Ht(i.checked)||i.checked===!0,children:o.jsx(fe.span,{...r,ref:e,"data-state":Ln(i.checked)})})});Tr.displayName=On;var Mc="MenuSeparator",Er=x.forwardRef((t,e)=>{const{__scopeMenu:n,...s}=t;return o.jsx(fe.div,{role:"separator","aria-orientation":"horizontal",...s,ref:e})});Er.displayName=Mc;var Sc="MenuArrow",Ir=x.forwardRef((t,e)=>{const{__scopeMenu:n,...s}=t,r=Dt(n);return o.jsx(Ul,{...r,...s,ref:e})});Ir.displayName=Sc;var kc="MenuSub",[vf,Fr]=Ie(kc),Qe="MenuSubTrigger",Dr=x.forwardRef((t,e)=>{const n=Fe(Qe,t.__scopeMenu),s=ut(Qe,t.__scopeMenu),r=Fr(Qe,t.__scopeMenu),i=Hn(Qe,t.__scopeMenu),a=x.useRef(null),{pointerGraceTimerRef:l,onPointerGraceIntentChange:d}=i,c={__scopeMenu:t.__scopeMenu},f=x.useCallback(()=>{a.current&&window.clearTimeout(a.current),a.current=null},[]);return x.useEffect(()=>f,[f]),x.useEffect(()=>{const u=l.current;return()=>{window.clearTimeout(u),d(null)}},[l,d]),o.jsx(kn,{asChild:!0,...c,children:o.jsx(Vr,{id:r.triggerId,"aria-haspopup":"menu","aria-expanded":n.open,"aria-controls":r.contentId,"data-state":Zr(n.open),...t,ref:Ms(e,r.onTriggerChange),onClick:u=>{var h;(h=t.onClick)==null||h.call(t,u),!(t.disabled||u.defaultPrevented)&&(u.currentTarget.focus(),n.open||n.onOpenChange(!0))},onPointerMove:$(t.onPointerMove,rt(u=>{i.onItemEnter(u),!u.defaultPrevented&&!t.disabled&&!n.open&&!a.current&&(i.onPointerGraceIntentChange(null),a.current=window.setTimeout(()=>{n.onOpenChange(!0),f()},100))})),onPointerLeave:$(t.onPointerLeave,rt(u=>{var m,g;f();const h=(m=n.content)==null?void 0:m.getBoundingClientRect();if(h){const p=(g=n.content)==null?void 0:g.dataset.side,b=p==="right",v=b?-5:5,y=h[b?"left":"right"],N=h[b?"right":"left"];i.onPointerGraceIntentChange({area:[{x:u.clientX+v,y:u.clientY},{x:y,y:h.top},{x:N,y:h.top},{x:N,y:h.bottom},{x:y,y:h.bottom}],side:p}),window.clearTimeout(l.current),l.current=window.setTimeout(()=>i.onPointerGraceIntentChange(null),300)}else{if(i.onTriggerLeave(u),u.defaultPrevented)return;i.onPointerGraceIntentChange(null)}})),onKeyDown:$(t.onKeyDown,u=>{var m;const h=i.searchRef.current!=="";t.disabled||h&&u.key===" "||ac[s.dir].includes(u.key)&&(n.onOpenChange(!0),(m=n.content)==null||m.focus(),u.preventDefault())})})})});Dr.displayName=Qe;var _r="MenuSubContent",$r=x.forwardRef((t,e)=>{const n=Cr(ue,t.__scopeMenu),{forceMount:s=n.forceMount,...r}=t,i=Fe(ue,t.__scopeMenu),a=ut(ue,t.__scopeMenu),l=Fr(_r,t.__scopeMenu),d=x.useRef(null),c=we(e,d);return o.jsx(st.Provider,{scope:t.__scopeMenu,children:o.jsx(Tt,{present:s||i.open,children:o.jsx(st.Slot,{scope:t.__scopeMenu,children:o.jsx(Rn,{id:l.contentId,"aria-labelledby":l.triggerId,...r,ref:c,align:"start",side:a.dir==="rtl"?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:f=>{var u;a.isUsingKeyboardRef.current&&((u=d.current)==null||u.focus()),f.preventDefault()},onCloseAutoFocus:f=>f.preventDefault(),onFocusOutside:$(t.onFocusOutside,f=>{f.target!==l.trigger&&i.onOpenChange(!1)}),onEscapeKeyDown:$(t.onEscapeKeyDown,f=>{a.onClose(),f.preventDefault()}),onKeyDown:$(t.onKeyDown,f=>{var m;const u=f.currentTarget.contains(f.target),h=lc[a.dir].includes(f.key);u&&h&&(i.onOpenChange(!1),(m=l.trigger)==null||m.focus(),f.preventDefault())})})})})})});$r.displayName=_r;function Zr(t){return t?"open":"closed"}function Ht(t){return t==="indeterminate"}function Ln(t){return Ht(t)?"indeterminate":t?"checked":"unchecked"}function Vc(t){const e=document.activeElement;for(const n of t)if(n===e||(n.focus(),document.activeElement!==e))return}function Hc(t,e){return t.map((n,s)=>t[(e+s)%t.length])}function Rc(t,e,n){const r=e.length>1&&Array.from(e).every(c=>c===e[0])?e[0]:e,i=n?t.indexOf(n):-1;let a=Hc(t,Math.max(i,0));r.length===1&&(a=a.filter(c=>c!==n));const d=a.find(c=>c.toLowerCase().startsWith(r.toLowerCase()));return d!==n?d:void 0}function Pc(t,e){const{x:n,y:s}=t;let r=!1;for(let i=0,a=e.length-1;i<e.length;a=i++){const l=e[i],d=e[a],c=l.x,f=l.y,u=d.x,h=d.y;f>s!=h>s&&n<(u-c)*(s-f)/(h-f)+c&&(r=!r)}return r}function Oc(t,e){if(!e)return!1;const n={x:t.clientX,y:t.clientY};return Pc(n,e)}function rt(t){return e=>e.pointerType==="mouse"?t(e):void 0}var Lc=Nr,Ac=kn,Tc=Mr,Ec=Sr,Ic=Pn,Fc=kr,Dc=_t,_c=Hr,$c=Pr,Zc=Lr,zc=Tr,Bc=Er,Kc=Ir,Uc=Dr,Gc=$r,$t="DropdownMenu",[Wc,wf]=at($t,[wr]),ae=wr(),[qc,zr]=Wc($t),Br=t=>{const{__scopeDropdownMenu:e,children:n,dir:s,open:r,defaultOpen:i,onOpenChange:a,modal:l=!0}=t,d=ae(e),c=x.useRef(null),[f,u]=Cs({prop:r,defaultProp:i??!1,onChange:a,caller:$t});return o.jsx(qc,{scope:e,triggerId:Qt(),triggerRef:c,contentId:Qt(),open:f,onOpenChange:u,onOpenToggle:x.useCallback(()=>u(h=>!h),[u]),modal:l,children:o.jsx(Lc,{...d,open:f,onOpenChange:u,dir:s,modal:l,children:n})})};Br.displayName=$t;var Kr="DropdownMenuTrigger",Ur=x.forwardRef((t,e)=>{const{__scopeDropdownMenu:n,disabled:s=!1,...r}=t,i=zr(Kr,n),a=ae(n);return o.jsx(Ac,{asChild:!0,...a,children:o.jsx(fe.button,{type:"button",id:i.triggerId,"aria-haspopup":"menu","aria-expanded":i.open,"aria-controls":i.open?i.contentId:void 0,"data-state":i.open?"open":"closed","data-disabled":s?"":void 0,disabled:s,...r,ref:Ms(e,i.triggerRef),onPointerDown:$(t.onPointerDown,l=>{!s&&l.button===0&&l.ctrlKey===!1&&(i.onOpenToggle(),i.open||l.preventDefault())}),onKeyDown:$(t.onKeyDown,l=>{s||(["Enter"," "].includes(l.key)&&i.onOpenToggle(),l.key==="ArrowDown"&&i.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(l.key)&&l.preventDefault())})})})});Ur.displayName=Kr;var Yc="DropdownMenuPortal",Gr=t=>{const{__scopeDropdownMenu:e,...n}=t,s=ae(e);return o.jsx(Tc,{...s,...n})};Gr.displayName=Yc;var Wr="DropdownMenuContent",qr=x.forwardRef((t,e)=>{const{__scopeDropdownMenu:n,...s}=t,r=zr(Wr,n),i=ae(n),a=x.useRef(!1);return o.jsx(Ec,{id:r.contentId,"aria-labelledby":r.triggerId,...i,...s,ref:e,onCloseAutoFocus:$(t.onCloseAutoFocus,l=>{var d;a.current||(d=r.triggerRef.current)==null||d.focus(),a.current=!1,l.preventDefault()}),onInteractOutside:$(t.onInteractOutside,l=>{const d=l.detail.originalEvent,c=d.button===0&&d.ctrlKey===!0,f=d.button===2||c;(!r.modal||f)&&(a.current=!0)}),style:{...t.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});qr.displayName=Wr;var Xc="DropdownMenuGroup",Jc=x.forwardRef((t,e)=>{const{__scopeDropdownMenu:n,...s}=t,r=ae(n);return o.jsx(Ic,{...r,...s,ref:e})});Jc.displayName=Xc;var Qc="DropdownMenuLabel",Yr=x.forwardRef((t,e)=>{const{__scopeDropdownMenu:n,...s}=t,r=ae(n);return o.jsx(Fc,{...r,...s,ref:e})});Yr.displayName=Qc;var ed="DropdownMenuItem",Xr=x.forwardRef((t,e)=>{const{__scopeDropdownMenu:n,...s}=t,r=ae(n);return o.jsx(Dc,{...r,...s,ref:e})});Xr.displayName=ed;var td="DropdownMenuCheckboxItem",Jr=x.forwardRef((t,e)=>{const{__scopeDropdownMenu:n,...s}=t,r=ae(n);return o.jsx(_c,{...r,...s,ref:e})});Jr.displayName=td;var nd="DropdownMenuRadioGroup",sd=x.forwardRef((t,e)=>{const{__scopeDropdownMenu:n,...s}=t,r=ae(n);return o.jsx($c,{...r,...s,ref:e})});sd.displayName=nd;var rd="DropdownMenuRadioItem",Qr=x.forwardRef((t,e)=>{const{__scopeDropdownMenu:n,...s}=t,r=ae(n);return o.jsx(Zc,{...r,...s,ref:e})});Qr.displayName=rd;var od="DropdownMenuItemIndicator",eo=x.forwardRef((t,e)=>{const{__scopeDropdownMenu:n,...s}=t,r=ae(n);return o.jsx(zc,{...r,...s,ref:e})});eo.displayName=od;var id="DropdownMenuSeparator",to=x.forwardRef((t,e)=>{const{__scopeDropdownMenu:n,...s}=t,r=ae(n);return o.jsx(Bc,{...r,...s,ref:e})});to.displayName=id;var ad="DropdownMenuArrow",ld=x.forwardRef((t,e)=>{const{__scopeDropdownMenu:n,...s}=t,r=ae(n);return o.jsx(Kc,{...r,...s,ref:e})});ld.displayName=ad;var cd="DropdownMenuSubTrigger",no=x.forwardRef((t,e)=>{const{__scopeDropdownMenu:n,...s}=t,r=ae(n);return o.jsx(Uc,{...r,...s,ref:e})});no.displayName=cd;var dd="DropdownMenuSubContent",so=x.forwardRef((t,e)=>{const{__scopeDropdownMenu:n,...s}=t,r=ae(n);return o.jsx(Gc,{...r,...s,ref:e,style:{...t.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});so.displayName=dd;var ud=Br,fd=Ur,hd=Gr,ro=qr,oo=Yr,io=Xr,ao=Jr,lo=Qr,co=eo,uo=to,fo=no,ho=so;const md=ud,gd=fd,pd=x.forwardRef(({className:t,inset:e,children:n,...s},r)=>o.jsxs(fo,{ref:r,className:Y("flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent",e&&"pl-8",t),...s,children:[n,o.jsx(wi,{className:"ml-auto h-4 w-4"})]}));pd.displayName=fo.displayName;const xd=x.forwardRef(({className:t,...e},n)=>o.jsx(ho,{ref:n,className:Y("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",t),...e}));xd.displayName=ho.displayName;const mo=x.forwardRef(({className:t,sideOffset:e=4,...n},s)=>o.jsx(hd,{children:o.jsx(ro,{ref:s,sideOffset:e,className:Y("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",t),...n})}));mo.displayName=ro.displayName;const wt=x.forwardRef(({className:t,inset:e,...n},s)=>o.jsx(io,{ref:s,className:Y("relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e&&"pl-8",t),...n}));wt.displayName=io.displayName;const bd=x.forwardRef(({className:t,children:e,checked:n,...s},r)=>o.jsxs(ao,{ref:r,className:Y("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t),checked:n,...s,children:[o.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:o.jsx(co,{children:o.jsx(yi,{className:"h-4 w-4"})})}),e]}));bd.displayName=ao.displayName;const yd=x.forwardRef(({className:t,children:e,...n},s)=>o.jsxs(lo,{ref:s,className:Y("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t),...n,children:[o.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:o.jsx(co,{children:o.jsx(Ni,{className:"h-2 w-2 fill-current"})})}),e]}));yd.displayName=lo.displayName;const vd=x.forwardRef(({className:t,inset:e,...n},s)=>o.jsx(oo,{ref:s,className:Y("px-2 py-1.5 text-sm font-semibold",e&&"pl-8",t),...n}));vd.displayName=oo.displayName;const wd=x.forwardRef(({className:t,...e},n)=>o.jsx(uo,{ref:n,className:Y("-mx-1 my-1 h-px bg-muted",t),...e}));wd.displayName=uo.displayName;const go=()=>{const{theme:t,setTheme:e}=cn();return o.jsxs(md,{children:[o.jsx(gd,{asChild:!0,children:o.jsxs(G,{variant:"ghost",size:"icon",className:"h-9 w-9",children:[o.jsx($n,{className:"h-4 w-4 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0"}),o.jsx(Dn,{className:"absolute h-4 w-4 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100"}),o.jsx("span",{className:"sr-only",children:"Toggle theme"})]})}),o.jsxs(mo,{align:"end",children:[o.jsxs(wt,{onClick:()=>e("light"),className:t==="light"?"bg-accent":"",children:[o.jsx($n,{className:"mr-2 h-4 w-4"}),o.jsx("span",{children:"Light"})]}),o.jsxs(wt,{onClick:()=>e("dark"),className:t==="dark"?"bg-accent":"",children:[o.jsx(Dn,{className:"mr-2 h-4 w-4"}),o.jsx("span",{children:"Dark"})]}),o.jsxs(wt,{onClick:()=>e("system"),className:t==="system"?"bg-accent":"",children:[o.jsx(Ti,{className:"mr-2 h-4 w-4"}),o.jsx("span",{children:"System"})]})]})]})},Zt=zs()(Bs((t,e)=>({user:null,isAuthenticated:!1,isLoading:!1,login:async n=>{t({isLoading:!0});try{console.log("Login with:",n);const s={id:"1",username:"testuser",email:n.email,role:"reader",createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};t({user:s,isAuthenticated:!0,isLoading:!1})}catch(s){throw console.error("Login failed:",s),t({isLoading:!1}),s}},logout:()=>{t({user:null,isAuthenticated:!1})},updateProfile:async n=>{const{user:s}=e();if(s){t({isLoading:!0});try{const r={...s,...n};t({user:r,isLoading:!1})}catch(r){throw console.error("Profile update failed:",r),t({isLoading:!1}),r}}},setUser:n=>{t({user:n,isAuthenticated:!!n})}}),{name:"auth-storage",partialize:t=>({user:t.user,isAuthenticated:t.isAuthenticated})})),xe=({className:t,size:e="md",variant:n="default",showText:s=!0})=>{const r={sm:"h-6",md:"h-8",lg:"h-12",xl:"h-16"},a=(()=>{switch(n){case"brand":return{text:"#E40066",icon:"#FAFCFC",iconStroke:"#E40066"};case"white":return{text:"#FFFFFF",icon:"#FFFFFF",iconStroke:"#FFFFFF"};case"dark":return{text:"#1F1F1F",icon:"#FAFCFC",iconStroke:"#1F1F1F"};default:return{text:"currentColor",icon:"#FAFCFC",iconStroke:"currentColor"}}})();return o.jsxs("div",{className:Y("flex items-center gap-2",t),children:[o.jsxs("svg",{width:"127",height:"46",viewBox:"0 0 127 46",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:Y(r[e],"flex-shrink-0"),children:[o.jsx("path",{d:"M14.42 27.58V39H10.54V27.58L0.04 10.34H3.44C3.78667 10.34 4.06 10.4267 4.26 10.6C4.46 10.76 4.63333 10.9733 4.78 11.24L11.34 22.38C11.58 22.8467 11.8 23.2867 12 23.7C12.2 24.1 12.3733 24.5067 12.52 24.92C12.6667 24.5067 12.8333 24.1 13.02 23.7C13.2067 23.2867 13.4133 22.8467 13.64 22.38L20.18 11.24C20.3 11.0133 20.4667 10.8067 20.68 10.62C20.8933 10.4333 21.1667 10.34 21.5 10.34H24.92L14.42 27.58ZM43.0816 18.74V39H40.9616C40.4549 39 40.1349 38.7533 40.0016 38.26L39.7216 36.08C38.8416 37.0533 37.8549 37.84 36.7616 38.44C35.6682 39.0267 34.4149 39.32 33.0016 39.32C31.8949 39.32 30.9149 39.14 30.0616 38.78C29.2216 38.4067 28.5149 37.8867 27.9416 37.22C27.3682 36.5533 26.9349 35.7467 26.6416 34.8C26.3616 33.8533 26.2216 32.8067 26.2216 31.66V18.74H29.7816V31.66C29.7816 33.1933 30.1282 34.38 30.8216 35.22C31.5282 36.06 32.6016 36.48 34.0416 36.48C35.0949 36.48 36.0749 36.2333 36.9816 35.74C37.9016 35.2333 38.7482 34.54 39.5216 33.66V18.74H43.0816ZM54.3063 9.54V26.88H55.2263C55.4929 26.88 55.7129 26.8467 55.8863 26.78C56.0729 26.7 56.2663 26.5467 56.4663 26.32L62.8663 19.46C63.0529 19.2333 63.2463 19.06 63.4463 18.94C63.6596 18.8067 63.9396 18.74 64.2863 18.74H67.5063L60.0463 26.68C59.6863 27.1333 59.2996 27.4867 58.8863 27.74C59.1263 27.9 59.3396 28.0867 59.5263 28.3C59.7263 28.5 59.9129 28.7333 60.0863 29L68.0062 39H64.8263C64.5196 39 64.2529 38.9533 64.0263 38.86C63.8129 38.7533 63.6263 38.5667 63.4663 38.3L56.8063 30C56.6063 29.72 56.4063 29.54 56.2063 29.46C56.0196 29.3667 55.7263 29.32 55.3263 29.32H54.3063V39H50.7263V9.54H54.3063ZM76.7988 18.74V39H73.2388V18.74H76.7988ZM77.5588 12.38C77.5588 12.7267 77.4854 13.0533 77.3388 13.36C77.2054 13.6533 77.0188 13.92 76.7788 14.16C76.5521 14.3867 76.2788 14.5667 75.9588 14.7C75.6521 14.8333 75.3254 14.9 74.9788 14.9C74.6321 14.9 74.3054 14.8333 73.9988 14.7C73.7054 14.5667 73.4454 14.3867 73.2188 14.16C72.9921 13.92 72.8121 13.6533 72.6788 13.36C72.5454 13.0533 72.4788 12.7267 72.4788 12.38C72.4788 12.0333 72.5454 11.7067 72.6788 11.4C72.8121 11.08 72.9921 10.8067 73.2188 10.58C73.4454 10.34 73.7054 10.1533 73.9988 10.02C74.3054 9.88666 74.6321 9.82 74.9788 9.82C75.3254 9.82 75.6521 9.88666 75.9588 10.02C76.2788 10.1533 76.5521 10.34 76.7788 10.58C77.0188 10.8067 77.2054 11.08 77.3388 11.4C77.4854 11.7067 77.5588 12.0333 77.5588 12.38Z",fill:a.text}),o.jsxs("mask",{id:"path-2-outside-1_136_8018",maskUnits:"userSpaceOnUse",x:"89.0435",y:"4",width:"38",height:"39",fill:"black",children:[o.jsx("rect",{fill:"white",x:"89.0435",y:"4",width:"38",height:"39"}),o.jsx("path",{d:"M93.1235 5.36V9.12H122.803V5.36H93.1235ZM95.9635 15.96V19.08H104.283V15.96H95.9635ZM95.0835 20.56V23.68H104.323V20.56H95.0835ZM111.523 20.56V23.68H120.883V20.56H111.523ZM111.523 15.96V19.08H119.923V15.96H111.523ZM94.9635 31V34.6H118.923V31H94.9635ZM93.4835 36.6V40.44H119.243V36.6H93.4835ZM105.563 6.8V24.12H110.243V6.8H105.563ZM94.0435 25.32V29.08H116.923V41.72H121.683V25.32H94.0435ZM90.4035 10.76V19.92H94.6435V14.44H121.283V19.92H125.723V10.76H90.4035Z"})]}),o.jsx("path",{d:"M93.1235 5.36V9.12H122.803V5.36H93.1235ZM95.9635 15.96V19.08H104.283V15.96H95.9635ZM95.0835 20.56V23.68H104.323V20.56H95.0835ZM111.523 20.56V23.68H120.883V20.56H111.523ZM111.523 15.96V19.08H119.923V15.96H111.523ZM94.9635 31V34.6H118.923V31H94.9635ZM93.4835 36.6V40.44H119.243V36.6H93.4835ZM105.563 6.8V24.12H110.243V6.8H105.563ZM94.0435 25.32V29.08H116.923V41.72H121.683V25.32H94.0435ZM90.4035 10.76V19.92H94.6435V14.44H121.283V19.92H125.723V10.76H90.4035Z",fill:a.icon}),o.jsx("path",{d:"M93.1235 5.36V4.36H92.1235V5.36H93.1235ZM93.1235 9.12H92.1235V10.12H93.1235V9.12ZM122.803 9.12V10.12H123.803V9.12H122.803ZM122.803 5.36H123.803V4.36H122.803V5.36ZM95.9635 15.96V14.96H94.9635V15.96H95.9635ZM95.9635 19.08H94.9635V20.08H95.9635V19.08ZM104.283 19.08V20.08H105.283V19.08H104.283ZM104.283 15.96H105.283V14.96H104.283V15.96ZM95.0835 20.56V19.56H94.0835V20.56H95.0835ZM95.0835 23.68H94.0835V24.68H95.0835V23.68ZM104.323 23.68V24.68H105.323V23.68H104.323ZM104.323 20.56H105.323V19.56H104.323V20.56ZM111.523 20.56V19.56H110.523V20.56H111.523ZM111.523 23.68H110.523V24.68H111.523V23.68ZM120.883 23.68V24.68H121.883V23.68H120.883ZM120.883 20.56H121.883V19.56H120.883V20.56ZM111.523 15.96V14.96H110.523V15.96H111.523ZM111.523 19.08H110.523V20.08H111.523V19.08ZM119.923 19.08V20.08H120.923V19.08H119.923ZM119.923 15.96H120.923V14.96H119.923V15.96ZM94.9635 31V30H93.9635V31H94.9635ZM94.9635 34.6H93.9635V35.6H94.9635V34.6ZM118.923 34.6V35.6H119.923V34.6H118.923ZM118.923 31H119.923V30H118.923V31ZM93.4835 36.6V35.6H92.4835V36.6H93.4835ZM93.4835 40.44H92.4835V41.44H93.4835V40.44ZM119.243 40.44V41.44H120.243V40.44H119.243ZM119.243 36.6H120.243V35.6H119.243V36.6ZM105.563 6.8V5.8H104.563V6.8H105.563ZM105.563 24.12H104.563V25.12H105.563V24.12ZM110.243 24.12V25.12H111.243V24.12H110.243ZM110.243 6.8H111.243V5.8H110.243V6.8ZM94.0435 25.32V24.32H93.0435V25.32H94.0435ZM94.0435 29.08H93.0435V30.08H94.0435V29.08ZM116.923 29.08H117.923V28.08H116.923V29.08ZM116.923 41.72H115.923V42.72H116.923V41.72ZM121.683 41.72V42.72H122.683V41.72H121.683ZM121.683 25.32H122.683V24.32H121.683V25.32ZM90.4035 10.76V9.76H89.4035V10.76H90.4035ZM90.4035 19.92H89.4035V20.92H90.4035V19.92ZM94.6435 19.92V20.92H95.6435V19.92H94.6435ZM94.6435 14.44V13.44H93.6435V14.44H94.6435ZM121.283 14.44H122.283V13.44H121.283V14.44ZM121.283 19.92H120.283V20.92H121.283V19.92ZM125.723 19.92V20.92H126.723V19.92H125.723ZM125.723 10.76H126.723V9.76H125.723V10.76ZM92.1235 5.36V9.12H94.1235V5.36H92.1235ZM93.1235 10.12H122.803V8.12H93.1235V10.12ZM123.803 9.12V5.36H121.803V9.12H123.803ZM122.803 4.36H93.1235V6.36H122.803V4.36ZM94.9635 15.96V19.08H96.9635V15.96H94.9635ZM95.9635 20.08H104.283V18.08H95.9635V20.08ZM105.283 19.08V15.96H103.283V19.08H105.283ZM104.283 14.96H95.9635V16.96H104.283V14.96ZM94.0835 20.56V23.68H96.0835V20.56H94.0835ZM95.0835 24.68H104.323V22.68H95.0835V24.68ZM105.323 23.68V20.56H103.323V23.68H105.323ZM104.323 19.56H95.0835V21.56H104.323V19.56ZM110.523 20.56V23.68H112.523V20.56H110.523ZM111.523 24.68H120.883V22.68H111.523V24.68ZM121.883 23.68V20.56H119.883V23.68H121.883ZM120.883 19.56H111.523V21.56H120.883V19.56ZM110.523 15.96V19.08H112.523V15.96H110.523ZM111.523 20.08H119.923V18.08H111.523V20.08ZM120.923 19.08V15.96H118.923V19.08H120.923ZM119.923 14.96H111.523V16.96H119.923V14.96ZM93.9635 31V34.6H95.9635V31H93.9635ZM94.9635 35.6H118.923V33.6H94.9635V35.6ZM119.923 34.6V31H117.923V34.6H119.923ZM118.923 30H94.9635V32H118.923V30ZM92.4835 36.6V40.44H94.4835V36.6H92.4835ZM93.4835 41.44H119.243V39.44H93.4835V41.44ZM120.243 40.44V36.6H118.243V40.44H120.243ZM119.243 35.6H93.4835V37.6H119.243V35.6ZM104.563 6.8V24.12H106.563V6.8H104.563ZM105.563 25.12H110.243V23.12H105.563V25.12ZM111.243 24.12V6.8H109.243V24.12H111.243ZM110.243 5.8H105.563V7.8H110.243V5.8ZM93.0435 25.32V29.08H95.0435V25.32H93.0435ZM94.0435 30.08H116.923V28.08H94.0435V30.08ZM115.923 29.08V41.72H117.923V29.08H115.923ZM116.923 42.72H121.683V40.72H116.923V42.72ZM122.683 41.72V25.32H120.683V41.72H122.683ZM121.683 24.32H94.0435V26.32H121.683V24.32ZM89.4035 10.76V19.92H91.4035V10.76H89.4035ZM90.4035 20.92H94.6435V18.92H90.4035V20.92ZM95.6435 19.92V14.44H93.6435V19.92H95.6435ZM94.6435 15.44H121.283V13.44H94.6435V15.44ZM120.283 14.44V19.92H122.283V14.44H120.283ZM121.283 20.92H125.723V18.92H121.283V20.92ZM126.723 19.92V10.76H124.723V19.92H126.723ZM125.723 9.76H90.4035V11.76H125.723V9.76Z",fill:a.iconStroke,mask:"url(#path-2-outside-1_136_8018)"})]}),s&&o.jsx("span",{className:Y("font-bold tracking-tight",e==="sm"&&"text-lg",e==="md"&&"text-xl",e==="lg"&&"text-2xl",e==="xl"&&"text-3xl"),style:{color:a.text},children:"BlogTruyen"})]})},po=()=>{const{t}=Oe("common"),{user:e,isAuthenticated:n,logout:s}=Zt();return o.jsx("header",{className:"sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60",children:o.jsxs("div",{className:"container flex h-16 items-center",children:[o.jsx(ie,{to:"/",className:"flex items-center",children:o.jsx(xe,{size:"md",variant:"default"})}),o.jsxs("nav",{className:"hidden md:flex items-center space-x-6 ml-8",children:[o.jsx(ie,{to:"/",className:"text-sm font-medium transition-colors hover:text-primary",children:t("navigation.home")}),o.jsx(ie,{to:"/browse",className:"text-sm font-medium transition-colors hover:text-primary",children:t("navigation.browse")}),n&&o.jsxs(o.Fragment,{children:[o.jsx(ie,{to:"/user/bookmarks",className:"text-sm font-medium transition-colors hover:text-primary",children:t("navigation.bookmarks")}),o.jsx(ie,{to:"/user/history",className:"text-sm font-medium transition-colors hover:text-primary",children:t("navigation.history")}),((e==null?void 0:e.role)==="admin"||(e==null?void 0:e.role)==="moderator")&&o.jsx(ie,{to:"/admin",className:"text-sm font-medium transition-colors hover:text-primary",children:t("navigation.admin")})]})]}),o.jsx("div",{className:"flex-1 flex justify-center px-4",children:o.jsxs("div",{className:"relative w-full max-w-sm",children:[o.jsx(Fi,{className:"absolute left-2 top-2.5 h-4 w-4 text-muted-foreground"}),o.jsx(Nt,{placeholder:t("actions.search"),className:"pl-8"})]})}),o.jsxs("div",{className:"flex items-center space-x-2",children:[o.jsx(go,{}),n?o.jsxs("div",{className:"flex items-center space-x-2",children:[o.jsx(ie,{to:"/user/profile",children:o.jsxs(G,{variant:"ghost",size:"sm",children:[o.jsx(Ui,{className:"h-4 w-4 mr-2"}),e==null?void 0:e.username]})}),o.jsx(G,{variant:"ghost",size:"sm",onClick:s,children:t("navigation.logout")})]}):o.jsxs("div",{className:"flex items-center space-x-2",children:[o.jsx(ie,{to:"/auth/login",children:o.jsx(G,{variant:"ghost",size:"sm",children:t("navigation.login")})}),o.jsx(ie,{to:"/auth/register",children:o.jsx(G,{size:"sm",children:t("navigation.register")})})]}),o.jsx(G,{variant:"ghost",size:"icon",className:"md:hidden",children:o.jsx(Li,{className:"h-4 w-4"})})]})]})})},jd=()=>{const{t}=Oe("common");return o.jsx("footer",{className:"border-t bg-background",children:o.jsxs("div",{className:"container py-8",children:[o.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-8",children:[o.jsxs("div",{className:"space-y-4",children:[o.jsx(xe,{size:"md",variant:"default"}),o.jsx("p",{className:"text-sm text-muted-foreground",children:"Nền tảng đọc truyện tranh online hiện đại với trải nghiệm đọc tuyệt vời."})]}),o.jsxs("div",{className:"space-y-4",children:[o.jsx("h3",{className:"font-semibold",children:"Liên kết nhanh"}),o.jsxs("ul",{className:"space-y-2 text-sm",children:[o.jsx("li",{children:o.jsx("a",{href:"/",className:"text-muted-foreground hover:text-primary transition-colors",children:t("navigation.home")})}),o.jsx("li",{children:o.jsx("a",{href:"/browse",className:"text-muted-foreground hover:text-primary transition-colors",children:t("navigation.browse")})}),o.jsx("li",{children:o.jsx("a",{href:"/about",className:"text-muted-foreground hover:text-primary transition-colors",children:"Về chúng tôi"})}),o.jsx("li",{children:o.jsx("a",{href:"/contact",className:"text-muted-foreground hover:text-primary transition-colors",children:"Liên hệ"})}),o.jsx("li",{children:o.jsx("a",{href:"/theme",className:"text-muted-foreground hover:text-primary transition-colors",children:"Theme"})}),o.jsx("li",{children:o.jsx("a",{href:"/logo",className:"text-muted-foreground hover:text-primary transition-colors",children:"Logo"})})]})]}),o.jsxs("div",{className:"space-y-4",children:[o.jsx("h3",{className:"font-semibold",children:"Hỗ trợ"}),o.jsxs("ul",{className:"space-y-2 text-sm",children:[o.jsx("li",{children:o.jsx("a",{href:"/help",className:"text-muted-foreground hover:text-primary transition-colors",children:"Trợ giúp"})}),o.jsx("li",{children:o.jsx("a",{href:"/faq",className:"text-muted-foreground hover:text-primary transition-colors",children:"FAQ"})}),o.jsx("li",{children:o.jsx("a",{href:"/privacy",className:"text-muted-foreground hover:text-primary transition-colors",children:"Chính sách bảo mật"})}),o.jsx("li",{children:o.jsx("a",{href:"/terms",className:"text-muted-foreground hover:text-primary transition-colors",children:"Điều khoản sử dụng"})})]})]}),o.jsxs("div",{className:"space-y-4",children:[o.jsx("h3",{className:"font-semibold",children:"Kết nối"}),o.jsxs("div",{className:"flex space-x-4",children:[o.jsx("a",{href:"#",className:"text-muted-foreground hover:text-primary transition-colors",children:o.jsx(ki,{className:"h-5 w-5"})}),o.jsx("a",{href:"#",className:"text-muted-foreground hover:text-primary transition-colors",children:o.jsx(Bi,{className:"h-5 w-5"})}),o.jsx("a",{href:"#",className:"text-muted-foreground hover:text-primary transition-colors",children:o.jsx(Pi,{className:"h-5 w-5"})})]})]})]}),o.jsx("div",{className:"border-t mt-8 pt-8 text-center text-sm text-muted-foreground",children:o.jsx("p",{children:"© 2024 BlogTruyen. Tất cả quyền được bảo lưu."})})]})})},rs=({showHeader:t=!0,showFooter:e=!0,className:n=""})=>o.jsxs("div",{className:`min-h-screen flex flex-col ${n}`,children:[t&&o.jsx(po,{}),o.jsx("main",{className:"flex-1",children:o.jsx(Lt,{})}),e&&o.jsx(jd,{})]}),os=({requireAuth:t=!1,redirectTo:e="/"})=>{const{isAuthenticated:n}=Zt(),s=Oo();return t&&!n?o.jsx(jt,{to:"/auth/login",replace:!0}):!t&&n&&(s.pathname==="/auth/login"||s.pathname==="/auth/register")?o.jsx(jt,{to:e,replace:!0}):o.jsx(Lt,{})},Nd=({className:t=""})=>o.jsx("div",{className:`min-h-screen bg-background ${t}`,children:o.jsx(Lt,{})}),Cd=({className:t=""})=>{const{user:e,isAuthenticated:n}=Zt();return n?(e==null?void 0:e.role)!=="admin"&&(e==null?void 0:e.role)!=="moderator"?o.jsx(jt,{to:"/",replace:!0}):o.jsxs("div",{className:`min-h-screen flex flex-col ${t}`,children:[o.jsx(po,{}),o.jsxs("div",{className:"flex flex-1",children:[o.jsx("aside",{className:"w-64 bg-muted border-r",children:o.jsxs("nav",{className:"p-4",children:[o.jsx("h2",{className:"font-semibold text-lg mb-4",children:"Admin Panel"}),o.jsxs("ul",{className:"space-y-2",children:[o.jsx("li",{children:o.jsx("a",{href:"/admin/dashboard",className:"block p-2 rounded hover:bg-accent",children:"Dashboard"})}),o.jsx("li",{children:o.jsx("a",{href:"/admin/manga",className:"block p-2 rounded hover:bg-accent",children:"Quản lý truyện"})}),o.jsx("li",{children:o.jsx("a",{href:"/admin/users",className:"block p-2 rounded hover:bg-accent",children:"Quản lý người dùng"})}),o.jsx("li",{children:o.jsx("a",{href:"/admin/comments",className:"block p-2 rounded hover:bg-accent",children:"Quản lý bình luận"})})]})]})}),o.jsx("main",{className:"flex-1 p-6",children:o.jsx(Lt,{})})]})]}):o.jsx(jt,{to:"/login",replace:!0})},X=x.forwardRef(({className:t,...e},n)=>o.jsx("div",{ref:n,className:Y("rounded-lg border bg-card text-card-foreground shadow-sm",t),...e}));X.displayName="Card";const se=x.forwardRef(({className:t,...e},n)=>o.jsx("div",{ref:n,className:Y("flex flex-col space-y-1.5 p-6",t),...e}));se.displayName="CardHeader";const ee=x.forwardRef(({className:t,...e},n)=>o.jsx("h3",{ref:n,className:Y("text-2xl font-semibold leading-none tracking-tight",t),...e}));ee.displayName="CardTitle";const re=x.forwardRef(({className:t,...e},n)=>o.jsx("p",{ref:n,className:Y("text-sm text-muted-foreground",t),...e}));re.displayName="CardDescription";const J=x.forwardRef(({className:t,...e},n)=>o.jsx("div",{ref:n,className:Y("p-6 pt-0",t),...e}));J.displayName="CardContent";const Md=x.forwardRef(({className:t,...e},n)=>o.jsx("div",{ref:n,className:Y("flex items-center p-6 pt-0",t),...e}));Md.displayName="CardFooter";const xo=()=>o.jsxs("div",{className:"space-y-8",children:[o.jsxs(X,{children:[o.jsxs(se,{children:[o.jsx(ee,{children:"Brand Colors"}),o.jsx(re,{children:"Màu sắc chính của BlogTruyen"})]}),o.jsx(J,{children:o.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[o.jsxs("div",{className:"space-y-2",children:[o.jsx("div",{className:"h-20 bg-brand-primary rounded-lg shadow-primary"}),o.jsxs("div",{className:"text-center",children:[o.jsx("p",{className:"font-semibold",children:"Primary"}),o.jsx("p",{className:"text-sm text-muted-foreground",children:"#E40066"}),o.jsx("p",{className:"text-xs text-muted-foreground",children:"Pink/Magenta"})]})]}),o.jsxs("div",{className:"space-y-2",children:[o.jsx("div",{className:"h-20 bg-brand-secondary rounded-lg shadow-secondary"}),o.jsxs("div",{className:"text-center",children:[o.jsx("p",{className:"font-semibold",children:"Secondary"}),o.jsx("p",{className:"text-sm text-muted-foreground",children:"#03CEA4"}),o.jsx("p",{className:"text-xs text-muted-foreground",children:"Teal/Cyan"})]})]}),o.jsxs("div",{className:"space-y-2",children:[o.jsx("div",{className:"h-20 bg-brand-accent rounded-lg shadow-accent"}),o.jsxs("div",{className:"text-center",children:[o.jsx("p",{className:"font-semibold",children:"Accent"}),o.jsx("p",{className:"text-sm text-muted-foreground",children:"#9747FF"}),o.jsx("p",{className:"text-xs text-muted-foreground",children:"Purple"})]})]})]})})]}),o.jsxs(X,{children:[o.jsxs(se,{children:[o.jsx(ee,{children:"Brand Gradients"}),o.jsx(re,{children:"Gradient combinations cho UI elements"})]}),o.jsx(J,{children:o.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[o.jsxs("div",{className:"space-y-2",children:[o.jsx("div",{className:"h-20 rounded-lg",style:{background:"linear-gradient(135deg, #E40066 0%, #9747FF 100%)"}}),o.jsxs("div",{className:"text-center",children:[o.jsx("p",{className:"font-semibold",children:"Primary Gradient"}),o.jsx("p",{className:"text-xs text-muted-foreground",children:"Pink → Purple"})]})]}),o.jsxs("div",{className:"space-y-2",children:[o.jsx("div",{className:"h-20 rounded-lg",style:{background:"linear-gradient(135deg, #03CEA4 0%, #9747FF 100%)"}}),o.jsxs("div",{className:"text-center",children:[o.jsx("p",{className:"font-semibold",children:"Secondary Gradient"}),o.jsx("p",{className:"text-xs text-muted-foreground",children:"Teal → Purple"})]})]}),o.jsxs("div",{className:"space-y-2 md:col-span-2",children:[o.jsx("div",{className:"h-20 rounded-lg",style:{background:"linear-gradient(135deg, #E40066 0%, #03CEA4 50%, #9747FF 100%)"}}),o.jsxs("div",{className:"text-center",children:[o.jsx("p",{className:"font-semibold",children:"Hero Gradient"}),o.jsx("p",{className:"text-xs text-muted-foreground",children:"Pink → Teal → Purple"})]})]})]})})]}),o.jsxs(X,{children:[o.jsxs(se,{children:[o.jsx(ee,{children:"Semantic Colors"}),o.jsx(re,{children:"Màu sắc cho các trạng thái và thông báo"})]}),o.jsx(J,{children:o.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[o.jsxs("div",{className:"space-y-2",children:[o.jsx("div",{className:"h-16 bg-success rounded-lg"}),o.jsxs("div",{className:"text-center",children:[o.jsx("p",{className:"font-semibold text-sm",children:"Success"}),o.jsx("p",{className:"text-xs text-muted-foreground",children:"#03CEA4"})]})]}),o.jsxs("div",{className:"space-y-2",children:[o.jsx("div",{className:"h-16 bg-warning rounded-lg"}),o.jsxs("div",{className:"text-center",children:[o.jsx("p",{className:"font-semibold text-sm",children:"Warning"}),o.jsx("p",{className:"text-xs text-muted-foreground",children:"#FFB800"})]})]}),o.jsxs("div",{className:"space-y-2",children:[o.jsx("div",{className:"h-16 bg-error rounded-lg"}),o.jsxs("div",{className:"text-center",children:[o.jsx("p",{className:"font-semibold text-sm",children:"Error"}),o.jsx("p",{className:"text-xs text-muted-foreground",children:"#E40066"})]})]}),o.jsxs("div",{className:"space-y-2",children:[o.jsx("div",{className:"h-16 bg-info rounded-lg"}),o.jsxs("div",{className:"text-center",children:[o.jsx("p",{className:"font-semibold text-sm",children:"Info"}),o.jsx("p",{className:"text-xs text-muted-foreground",children:"#9747FF"})]})]})]})})]}),o.jsxs(X,{children:[o.jsxs(se,{children:[o.jsx(ee,{children:"Component Examples"}),o.jsx(re,{children:"Ví dụ sử dụng theme trong components"})]}),o.jsx(J,{children:o.jsxs("div",{className:"space-y-4",children:[o.jsxs("div",{className:"flex flex-wrap gap-2",children:[o.jsx("button",{className:"px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors",children:"Primary Button"}),o.jsx("button",{className:"px-4 py-2 bg-secondary text-secondary-foreground rounded-md hover:bg-secondary/90 transition-colors",children:"Secondary Button"}),o.jsx("button",{className:"px-4 py-2 bg-accent text-accent-foreground rounded-md hover:bg-accent/90 transition-colors",children:"Accent Button"})]}),o.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[o.jsxs("div",{className:"p-4 border-l-4 border-brand-primary bg-card rounded-lg",children:[o.jsx("h4",{className:"font-semibold text-brand-primary",children:"Primary Card"}),o.jsx("p",{className:"text-sm text-muted-foreground",children:"Card với accent primary color"})]}),o.jsxs("div",{className:"p-4 border-l-4 border-brand-secondary bg-card rounded-lg",children:[o.jsx("h4",{className:"font-semibold text-brand-secondary",children:"Secondary Card"}),o.jsx("p",{className:"text-sm text-muted-foreground",children:"Card với accent secondary color"})]}),o.jsxs("div",{className:"p-4 border-l-4 border-brand-accent bg-card rounded-lg",children:[o.jsx("h4",{className:"font-semibold text-brand-accent",children:"Accent Card"}),o.jsx("p",{className:"text-sm text-muted-foreground",children:"Card với accent color"})]})]})]})})]})]}),Sd=()=>o.jsxs("div",{className:"container py-8",children:[o.jsxs("section",{className:"text-center py-12 relative overflow-hidden",children:[o.jsx("div",{className:"absolute inset-0 opacity-10",style:{background:"linear-gradient(135deg, #E40066 0%, #03CEA4 50%, #9747FF 100%)"}}),o.jsxs("div",{className:"relative z-10",children:[o.jsx("h1",{className:"text-4xl font-bold tracking-tight lg:text-6xl mb-6 bg-gradient-to-r from-brand-primary via-brand-secondary to-brand-accent bg-clip-text text-transparent",children:"Chào mừng đến với BlogTruyen"}),o.jsx("p",{className:"text-xl text-muted-foreground mb-8 max-w-2xl mx-auto",children:"Khám phá thế giới truyện tranh với hàng ngàn bộ manga chất lượng cao. Đọc miễn phí, không quảng cáo, trải nghiệm tuyệt vời."}),o.jsxs("div",{className:"flex gap-4 justify-center",children:[o.jsx(ie,{to:"/browse",children:o.jsxs(G,{size:"lg",children:[o.jsx(sn,{className:"mr-2 h-4 w-4"}),"Bắt đầu đọc"]})}),o.jsx(ie,{to:"/register",children:o.jsx(G,{variant:"outline",size:"lg",children:"Đăng ký miễn phí"})})]})]})]}),o.jsxs("section",{className:"py-12",children:[o.jsx("h2",{className:"text-3xl font-bold text-center mb-8",children:"Tính năng nổi bật"}),o.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[o.jsxs(X,{children:[o.jsxs(se,{className:"text-center",children:[o.jsx(Zi,{className:"h-8 w-8 mx-auto mb-2 text-primary"}),o.jsx(ee,{className:"text-lg",children:"Xu hướng"})]}),o.jsx(J,{children:o.jsx(re,{className:"text-center",children:"Theo dõi những bộ truyện hot nhất, được cập nhật liên tục"})})]}),o.jsxs(X,{children:[o.jsxs(se,{className:"text-center",children:[o.jsx(Mi,{className:"h-8 w-8 mx-auto mb-2 text-primary"}),o.jsx(ee,{className:"text-lg",children:"Lịch sử đọc"})]}),o.jsx(J,{children:o.jsx(re,{className:"text-center",children:"Lưu lại tiến trình đọc, tiếp tục từ nơi bạn đã dừng lại"})})]}),o.jsxs(X,{children:[o.jsxs(se,{className:"text-center",children:[o.jsx(_n,{className:"h-8 w-8 mx-auto mb-2 text-primary"}),o.jsx(ee,{className:"text-lg",children:"Đánh giá"})]}),o.jsx(J,{children:o.jsx(re,{className:"text-center",children:"Đánh giá và bình luận về những bộ truyện yêu thích"})})]}),o.jsxs(X,{children:[o.jsxs(se,{className:"text-center",children:[o.jsx(sn,{className:"h-8 w-8 mx-auto mb-2 text-primary"}),o.jsx(ee,{className:"text-lg",children:"Đa nền tảng"})]}),o.jsx(J,{children:o.jsx(re,{className:"text-center",children:"Đọc mọi lúc mọi nơi trên điện thoại, máy tính bảng, laptop"})})]})]})]}),o.jsxs("section",{className:"py-12",children:[o.jsxs("div",{className:"flex justify-between items-center mb-8",children:[o.jsx("h2",{className:"text-3xl font-bold",children:"Truyện phổ biến"}),o.jsx(ie,{to:"/browse",children:o.jsx(G,{variant:"outline",children:"Xem tất cả"})})]}),o.jsx("div",{className:"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4",children:Array.from({length:6}).map((t,e)=>o.jsxs(X,{className:"overflow-hidden hover:shadow-lg transition-shadow",children:[o.jsx("div",{className:"aspect-[3/4] bg-muted"}),o.jsxs(J,{className:"p-3",children:[o.jsxs("h3",{className:"font-semibold text-sm truncate",children:["Tên truyện ",e+1]}),o.jsx("p",{className:"text-xs text-muted-foreground",children:"Tác giả"}),o.jsxs("div",{className:"flex items-center mt-1",children:[o.jsx(_n,{className:"h-3 w-3 fill-yellow-400 text-yellow-400"}),o.jsx("span",{className:"text-xs ml-1",children:"4.5"})]})]})]},e))})]}),o.jsxs("section",{className:"py-12",children:[o.jsxs("div",{className:"flex justify-between items-center mb-8",children:[o.jsx("h2",{className:"text-3xl font-bold",children:"Cập nhật mới nhất"}),o.jsx(ie,{to:"/browse?sort=updated",children:o.jsx(G,{variant:"outline",children:"Xem tất cả"})})]}),o.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:Array.from({length:6}).map((t,e)=>o.jsx(X,{className:"hover:shadow-lg transition-shadow",children:o.jsx(J,{className:"p-4",children:o.jsxs("div",{className:"flex space-x-4",children:[o.jsx("div",{className:"w-16 h-20 bg-muted rounded"}),o.jsxs("div",{className:"flex-1",children:[o.jsxs("h3",{className:"font-semibold mb-1",children:["Tên truyện ",e+1]}),o.jsx("p",{className:"text-sm text-muted-foreground mb-2",children:"Chapter 123"}),o.jsx("p",{className:"text-xs text-muted-foreground",children:"2 giờ trước"})]})]})})},e))})]}),o.jsxs("section",{className:"py-12",children:[o.jsx("h2",{className:"text-3xl font-bold text-center mb-8",children:"Theme Showcase"}),o.jsx(xo,{})]})]});var kd="Label",bo=x.forwardRef((t,e)=>o.jsx(fe.label,{...t,ref:e,onMouseDown:n=>{var r;n.target.closest("button, input, select, textarea")||((r=t.onMouseDown)==null||r.call(t,n),!n.defaultPrevented&&n.detail>1&&n.preventDefault())}}));bo.displayName=kd;var yo=bo;const Vd=Os("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),gn=x.forwardRef(({className:t,...e},n)=>o.jsx(yo,{ref:n,className:Y(Vd(),t),...e}));gn.displayName=yo.displayName;const Hd=()=>{const{t}=Oe("common"),e=Lo(),{login:n,isLoading:s}=Zt(),[r,i]=x.useState({email:"",password:""}),[a,l]=x.useState({}),d=u=>{const{name:h,value:m}=u.target;i(g=>({...g,[h]:m})),a[h]&&l(g=>({...g,[h]:""}))},c=()=>{const u={};return r.email?/\S+@\S+\.\S+/.test(r.email)||(u.email=t("forms.invalidEmail")):u.email=t("forms.required"),r.password||(u.password=t("forms.required")),l(u),Object.keys(u).length===0},f=async u=>{if(u.preventDefault(),!!c())try{await n(r),e("/")}catch(h){console.error("Login failed:",h),l({general:"Đăng nhập thất bại. Vui lòng kiểm tra lại thông tin."})}};return o.jsx("div",{className:"container flex items-center justify-center min-h-[calc(100vh-8rem)] py-8",children:o.jsxs(X,{className:"w-full max-w-md",children:[o.jsxs(se,{className:"text-center",children:[o.jsx("div",{className:"flex justify-center mb-4",children:o.jsx(sn,{className:"h-8 w-8 text-primary"})}),o.jsx(ee,{className:"text-2xl",children:t("navigation.login")}),o.jsx(re,{children:"Đăng nhập để truy cập tài khoản của bạn"})]}),o.jsxs(J,{children:[o.jsxs("form",{onSubmit:f,className:"space-y-4",children:[a.general&&o.jsx("div",{className:"text-sm text-destructive text-center p-2 bg-destructive/10 rounded",children:a.general}),o.jsxs("div",{className:"space-y-2",children:[o.jsx(gn,{htmlFor:"email",children:t("forms.email")}),o.jsx(Nt,{id:"email",name:"email",type:"email",value:r.email,onChange:d,placeholder:"<EMAIL>",className:a.email?"border-destructive":""}),a.email&&o.jsx("p",{className:"text-sm text-destructive",children:a.email})]}),o.jsxs("div",{className:"space-y-2",children:[o.jsx(gn,{htmlFor:"password",children:t("forms.password")}),o.jsx(Nt,{id:"password",name:"password",type:"password",value:r.password,onChange:d,className:a.password?"border-destructive":""}),a.password&&o.jsx("p",{className:"text-sm text-destructive",children:a.password})]}),o.jsx(G,{type:"submit",className:"w-full",disabled:s,children:s?"Đang đăng nhập...":t("navigation.login")})]}),o.jsx("div",{className:"mt-6 text-center text-sm",children:o.jsxs("p",{className:"text-muted-foreground",children:["Chưa có tài khoản?"," ",o.jsx(ie,{to:"/register",className:"text-primary hover:underline",children:t("navigation.register")})]})})]})]})})},Rd=()=>{const{t}=Oe(["common","manga"]);return o.jsxs("div",{className:"container py-8",children:[o.jsx("h1",{className:"text-3xl font-bold mb-8",children:t("navigation.browse")}),o.jsx("div",{className:"text-center py-12",children:o.jsx("p",{className:"text-muted-foreground",children:"Trang duyệt truyện đang được phát triển..."})})]})},Pd=()=>o.jsxs("div",{className:"container py-8",children:[o.jsx("h1",{className:"text-3xl font-bold mb-8",children:"Chi tiết truyện"}),o.jsx("div",{className:"text-center py-12",children:o.jsx("p",{className:"text-muted-foreground",children:"Trang chi tiết truyện đang được phát triển..."})})]}),Od=()=>o.jsxs("div",{className:"container py-8",children:[o.jsx("h1",{className:"text-3xl font-bold mb-8",children:"Đọc truyện"}),o.jsx("div",{className:"text-center py-12",children:o.jsx("p",{className:"text-muted-foreground",children:"Trang đọc truyện đang được phát triển..."})})]}),Ld=()=>{const{t}=Oe("common");return o.jsxs("div",{className:"container py-8",children:[o.jsx("h1",{className:"text-3xl font-bold mb-8",children:t("navigation.register")}),o.jsx("div",{className:"text-center py-12",children:o.jsx("p",{className:"text-muted-foreground",children:"Trang đăng ký đang được phát triển..."})})]})},Ad=()=>{const{t}=Oe("common");return o.jsxs("div",{className:"container py-8",children:[o.jsx("h1",{className:"text-3xl font-bold mb-8",children:t("navigation.bookmarks")}),o.jsx("div",{className:"text-center py-12",children:o.jsx("p",{className:"text-muted-foreground",children:"Trang đánh dấu đang được phát triển..."})})]})},Td=()=>{const{t}=Oe("common");return o.jsxs("div",{className:"container py-8",children:[o.jsx("h1",{className:"text-3xl font-bold mb-8",children:t("navigation.history")}),o.jsx("div",{className:"text-center py-12",children:o.jsx("p",{className:"text-muted-foreground",children:"Trang lịch sử đang được phát triển..."})})]})},Ed=()=>{const{t}=Oe("common");return o.jsxs("div",{className:"container py-8",children:[o.jsx("h1",{className:"text-3xl font-bold mb-8",children:t("navigation.profile")}),o.jsx("div",{className:"text-center py-12",children:o.jsx("p",{className:"text-muted-foreground",children:"Trang hồ sơ đang được phát triển..."})})]})},Id=()=>o.jsx("div",{className:"container flex items-center justify-center min-h-[calc(100vh-8rem)] py-8",children:o.jsxs(X,{className:"w-full max-w-md text-center",children:[o.jsxs(se,{children:[o.jsx(ee,{className:"text-6xl font-bold text-muted-foreground mb-4",children:"404"}),o.jsx(ee,{className:"text-2xl",children:"Trang không tồn tại"}),o.jsx(re,{children:"Trang bạn đang tìm kiếm không tồn tại hoặc đã bị di chuyển."})]}),o.jsx(J,{className:"space-y-4",children:o.jsxs("div",{className:"flex flex-col sm:flex-row gap-2 justify-center",children:[o.jsx(ie,{to:"/",children:o.jsxs(G,{className:"w-full sm:w-auto",children:[o.jsx(Hi,{className:"h-4 w-4 mr-2"}),"Về trang chủ"]})}),o.jsxs(G,{variant:"outline",onClick:()=>window.history.back(),className:"w-full sm:w-auto",children:[o.jsx(pi,{className:"h-4 w-4 mr-2"}),"Quay lại"]})]})})]})}),Fd=()=>o.jsxs("div",{className:"container py-8",children:[o.jsxs("div",{className:"flex justify-between items-center mb-8",children:[o.jsxs("div",{children:[o.jsx("h1",{className:"text-4xl font-bold mb-2",children:"BlogTruyen Theme System"}),o.jsx("p",{className:"text-muted-foreground",children:"Hệ thống màu sắc và theme cho BlogTruyen"})]}),o.jsx(go,{})]}),o.jsx("section",{className:"mb-12",children:o.jsxs(X,{className:"relative overflow-hidden",children:[o.jsx("div",{className:"absolute inset-0 opacity-20",style:{background:"linear-gradient(135deg, #E40066 0%, #03CEA4 50%, #9747FF 100%)"}}),o.jsxs(J,{className:"relative z-10 p-8 text-center",children:[o.jsx("h2",{className:"text-3xl font-bold mb-4 bg-gradient-to-r from-brand-primary via-brand-secondary to-brand-accent bg-clip-text text-transparent",children:"Brand Colors & Design System"}),o.jsx("p",{className:"text-lg text-muted-foreground mb-6",children:"Khám phá hệ thống màu sắc và thiết kế của BlogTruyen"}),o.jsxs("div",{className:"flex justify-center gap-4",children:[o.jsx(G,{className:"bg-brand-primary hover:bg-brand-primary/90",children:"Primary Action"}),o.jsx(G,{variant:"outline",className:"border-brand-secondary text-brand-secondary hover:bg-brand-secondary hover:text-white",children:"Secondary Action"})]})]})]})}),o.jsx(xo,{}),o.jsx("section",{className:"mt-12",children:o.jsxs(X,{children:[o.jsxs(se,{children:[o.jsx(ee,{children:"Interactive Examples"}),o.jsx(re,{children:"Các ví dụ tương tác với theme colors"})]}),o.jsx(J,{children:o.jsxs("div",{className:"space-y-6",children:[o.jsxs("div",{children:[o.jsx("h4",{className:"font-semibold mb-3",children:"Buttons"}),o.jsxs("div",{className:"flex flex-wrap gap-2",children:[o.jsx(G,{children:"Default"}),o.jsx(G,{variant:"secondary",children:"Secondary"}),o.jsx(G,{variant:"outline",children:"Outline"}),o.jsx(G,{variant:"ghost",children:"Ghost"}),o.jsx(G,{variant:"destructive",children:"Destructive"}),o.jsx(G,{className:"bg-brand-primary hover:bg-brand-primary/90",children:"Brand Primary"}),o.jsx(G,{className:"bg-brand-secondary hover:bg-brand-secondary/90",children:"Brand Secondary"}),o.jsx(G,{className:"bg-brand-accent hover:bg-brand-accent/90",children:"Brand Accent"})]})]}),o.jsxs("div",{children:[o.jsx("h4",{className:"font-semibold mb-3",children:"Status Cards"}),o.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[o.jsxs("div",{className:"p-4 border border-success bg-success/10 rounded-lg",children:[o.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[o.jsx("div",{className:"w-3 h-3 bg-success rounded-full"}),o.jsx("span",{className:"font-semibold text-success",children:"Success"})]}),o.jsx("p",{className:"text-sm",children:"Operation completed successfully"})]}),o.jsxs("div",{className:"p-4 border border-warning bg-warning/10 rounded-lg",children:[o.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[o.jsx("div",{className:"w-3 h-3 bg-warning rounded-full"}),o.jsx("span",{className:"font-semibold text-warning",children:"Warning"})]}),o.jsx("p",{className:"text-sm",children:"Please review this action"})]}),o.jsxs("div",{className:"p-4 border border-error bg-error/10 rounded-lg",children:[o.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[o.jsx("div",{className:"w-3 h-3 bg-error rounded-full"}),o.jsx("span",{className:"font-semibold text-error",children:"Error"})]}),o.jsx("p",{className:"text-sm",children:"Something went wrong"})]}),o.jsxs("div",{className:"p-4 border border-info bg-info/10 rounded-lg",children:[o.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[o.jsx("div",{className:"w-3 h-3 bg-info rounded-full"}),o.jsx("span",{className:"font-semibold text-info",children:"Info"})]}),o.jsx("p",{className:"text-sm",children:"Additional information"})]})]})]}),o.jsxs("div",{children:[o.jsx("h4",{className:"font-semibold mb-3",children:"Gradient Backgrounds"}),o.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[o.jsx("div",{className:"h-24 bg-gradient-primary rounded-lg flex items-center justify-center text-white font-semibold",children:"Primary Gradient"}),o.jsx("div",{className:"h-24 bg-gradient-secondary rounded-lg flex items-center justify-center text-white font-semibold",children:"Secondary Gradient"}),o.jsx("div",{className:"h-24 bg-gradient-hero rounded-lg flex items-center justify-center text-white font-semibold",children:"Hero Gradient"})]})]})]})})]})})]}),xt=({className:t,size:e=32,variant:n="default"})=>{const r=(()=>{switch(n){case"brand":return{text:"#E40066",icon:"#FAFCFC",iconStroke:"#E40066"};case"white":return{text:"#FFFFFF",icon:"#FFFFFF",iconStroke:"#FFFFFF"};case"dark":return{text:"#1F1F1F",icon:"#FAFCFC",iconStroke:"#1F1F1F"};default:return{text:"currentColor",icon:"#FAFCFC",iconStroke:"currentColor"}}})();return o.jsxs("svg",{width:e,height:e,viewBox:"0 0 127 46",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:Y("flex-shrink-0",t),children:[o.jsx("path",{d:"M14.42 27.58V39H10.54V27.58L0.04 10.34H3.44C3.78667 10.34 4.06 10.4267 4.26 10.6C4.46 10.76 4.63333 10.9733 4.78 11.24L11.34 22.38C11.58 22.8467 11.8 23.2867 12 23.7C12.2 24.1 12.3733 24.5067 12.52 24.92C12.6667 24.5067 12.8333 24.1 13.02 23.7C13.2067 23.2867 13.4133 22.8467 13.64 22.38L20.18 11.24C20.3 11.0133 20.4667 10.8067 20.68 10.62C20.8933 10.4333 21.1667 10.34 21.5 10.34H24.92L14.42 27.58ZM43.0816 18.74V39H40.9616C40.4549 39 40.1349 38.7533 40.0016 38.26L39.7216 36.08C38.8416 37.0533 37.8549 37.84 36.7616 38.44C35.6682 39.0267 34.4149 39.32 33.0016 39.32C31.8949 39.32 30.9149 39.14 30.0616 38.78C29.2216 38.4067 28.5149 37.8867 27.9416 37.22C27.3682 36.5533 26.9349 35.7467 26.6416 34.8C26.3616 33.8533 26.2216 32.8067 26.2216 31.66V18.74H29.7816V31.66C29.7816 33.1933 30.1282 34.38 30.8216 35.22C31.5282 36.06 32.6016 36.48 34.0416 36.48C35.0949 36.48 36.0749 36.2333 36.9816 35.74C37.9016 35.2333 38.7482 34.54 39.5216 33.66V18.74H43.0816ZM54.3063 9.54V26.88H55.2263C55.4929 26.88 55.7129 26.8467 55.8863 26.78C56.0729 26.7 56.2663 26.5467 56.4663 26.32L62.8663 19.46C63.0529 19.2333 63.2463 19.06 63.4463 18.94C63.6596 18.8067 63.9396 18.74 64.2863 18.74H67.5063L60.0463 26.68C59.6863 27.1333 59.2996 27.4867 58.8863 27.74C59.1263 27.9 59.3396 28.0867 59.5263 28.3C59.7263 28.5 59.9129 28.7333 60.0863 29L68.0062 39H64.8263C64.5196 39 64.2529 38.9533 64.0263 38.86C63.8129 38.7533 63.6263 38.5667 63.4663 38.3L56.8063 30C56.6063 29.72 56.4063 29.54 56.2063 29.46C56.0196 29.3667 55.7263 29.32 55.3263 29.32H54.3063V39H50.7263V9.54H54.3063ZM76.7988 18.74V39H73.2388V18.74H76.7988ZM77.5588 12.38C77.5588 12.7267 77.4854 13.0533 77.3388 13.36C77.2054 13.6533 77.0188 13.92 76.7788 14.16C76.5521 14.3867 76.2788 14.5667 75.9588 14.7C75.6521 14.8333 75.3254 14.9 74.9788 14.9C74.6321 14.9 74.3054 14.8333 73.9988 14.7C73.7054 14.5667 73.4454 14.3867 73.2188 14.16C72.9921 13.92 72.8121 13.6533 72.6788 13.36C72.5454 13.0533 72.4788 12.7267 72.4788 12.38C72.4788 12.0333 72.5454 11.7067 72.6788 11.4C72.8121 11.08 72.9921 10.8067 73.2188 10.58C73.4454 10.34 73.7054 10.1533 73.9988 10.02C74.3054 9.88666 74.6321 9.82 74.9788 9.82C75.3254 9.82 75.6521 9.88666 75.9588 10.02C76.2788 10.1533 76.5521 10.34 76.7788 10.58C77.0188 10.8067 77.2054 11.08 77.3388 11.4C77.4854 11.7067 77.5588 12.0333 77.5588 12.38Z",fill:r.text}),o.jsxs("mask",{id:"logo-icon-mask",maskUnits:"userSpaceOnUse",x:"89.0435",y:"4",width:"38",height:"39",fill:"black",children:[o.jsx("rect",{fill:"white",x:"89.0435",y:"4",width:"38",height:"39"}),o.jsx("path",{d:"M93.1235 5.36V9.12H122.803V5.36H93.1235ZM95.9635 15.96V19.08H104.283V15.96H95.9635ZM95.0835 20.56V23.68H104.323V20.56H95.0835ZM111.523 20.56V23.68H120.883V20.56H111.523ZM111.523 15.96V19.08H119.923V15.96H111.523ZM94.9635 31V34.6H118.923V31H94.9635ZM93.4835 36.6V40.44H119.243V36.6H93.4835ZM105.563 6.8V24.12H110.243V6.8H105.563ZM94.0435 25.32V29.08H116.923V41.72H121.683V25.32H94.0435ZM90.4035 10.76V19.92H94.6435V14.44H121.283V19.92H125.723V10.76H90.4035Z"})]}),o.jsx("path",{d:"M93.1235 5.36V9.12H122.803V5.36H93.1235ZM95.9635 15.96V19.08H104.283V15.96H95.9635ZM95.0835 20.56V23.68H104.323V20.56H95.0835ZM111.523 20.56V23.68H120.883V20.56H111.523ZM111.523 15.96V19.08H119.923V15.96H111.523ZM94.9635 31V34.6H118.923V31H94.9635ZM93.4835 36.6V40.44H119.243V36.6H93.4835ZM105.563 6.8V24.12H110.243V6.8H105.563ZM94.0435 25.32V29.08H116.923V41.72H121.683V25.32H94.0435ZM90.4035 10.76V19.92H94.6435V14.44H121.283V19.92H125.723V10.76H90.4035Z",fill:r.icon}),o.jsx("path",{d:"M93.1235 5.36V4.36H92.1235V5.36H93.1235ZM93.1235 9.12H92.1235V10.12H93.1235V9.12ZM122.803 9.12V10.12H123.803V9.12H122.803ZM122.803 5.36H123.803V4.36H122.803V5.36ZM95.9635 15.96V14.96H94.9635V15.96H95.9635ZM95.9635 19.08H94.9635V20.08H95.9635V19.08ZM104.283 19.08V20.08H105.283V19.08H104.283ZM104.283 15.96H105.283V14.96H104.283V15.96ZM95.0835 20.56V19.56H94.0835V20.56H95.0835ZM95.0835 23.68H94.0835V24.68H95.0835V23.68ZM104.323 23.68V24.68H105.323V23.68H104.323ZM104.323 20.56H105.323V19.56H104.323V20.56ZM111.523 20.56V19.56H110.523V20.56H111.523ZM111.523 23.68H110.523V24.68H111.523V23.68ZM120.883 23.68V24.68H121.883V23.68H120.883ZM120.883 20.56H121.883V19.56H120.883V20.56ZM111.523 15.96V14.96H110.523V15.96H111.523ZM111.523 19.08H110.523V20.08H111.523V19.08ZM119.923 19.08V20.08H120.923V19.08H119.923ZM119.923 15.96H120.923V14.96H119.923V15.96ZM94.9635 31V30H93.9635V31H94.9635ZM94.9635 34.6H93.9635V35.6H94.9635V34.6ZM118.923 34.6V35.6H119.923V34.6H118.923ZM118.923 31H119.923V30H118.923V31ZM93.4835 36.6V35.6H92.4835V36.6H93.4835ZM93.4835 40.44H92.4835V41.44H93.4835V40.44ZM119.243 40.44V41.44H120.243V40.44H119.243ZM119.243 36.6H120.243V35.6H119.243V36.6ZM105.563 6.8V5.8H104.563V6.8H105.563ZM105.563 24.12H104.563V25.12H105.563V24.12ZM110.243 24.12V25.12H111.243V24.12H110.243ZM110.243 6.8H111.243V5.8H110.243V6.8ZM94.0435 25.32V24.32H93.0435V25.32H94.0435ZM94.0435 29.08H93.0435V30.08H94.0435V29.08ZM116.923 29.08H117.923V28.08H116.923V29.08ZM116.923 41.72H115.923V42.72H116.923V41.72ZM121.683 41.72V42.72H122.683V41.72H121.683ZM121.683 25.32H122.683V24.32H121.683V25.32ZM90.4035 10.76V9.76H89.4035V10.76H90.4035ZM90.4035 19.92H89.4035V20.92H90.4035V19.92ZM94.6435 19.92V20.92H95.6435V19.92H94.6435ZM94.6435 14.44V13.44H93.6435V14.44H94.6435ZM121.283 14.44H122.283V13.44H121.283V14.44ZM121.283 19.92H120.283V20.92H121.283V19.92ZM125.723 19.92V20.92H126.723V19.92H125.723ZM125.723 10.76H126.723V9.76H125.723V10.76ZM92.1235 5.36V9.12H94.1235V5.36H92.1235ZM93.1235 10.12H122.803V8.12H93.1235V10.12ZM123.803 9.12V5.36H121.803V9.12H123.803ZM122.803 4.36H93.1235V6.36H122.803V4.36ZM94.9635 15.96V19.08H96.9635V15.96H94.9635ZM95.9635 20.08H104.283V18.08H95.9635V20.08ZM105.283 19.08V15.96H103.283V19.08H105.283ZM104.283 14.96H95.9635V16.96H104.283V14.96ZM94.0835 20.56V23.68H96.0835V20.56H94.0835ZM95.0835 24.68H104.323V22.68H95.0835V24.68ZM105.323 23.68V20.56H103.323V23.68H105.323ZM104.323 19.56H95.0835V21.56H104.323V19.56ZM110.523 20.56V23.68H112.523V20.56H110.523ZM111.523 24.68H120.883V22.68H111.523V24.68ZM121.883 23.68V20.56H119.883V23.68H121.883ZM120.883 19.56H111.523V21.56H120.883V19.56ZM110.523 15.96V19.08H112.523V15.96H110.523ZM111.523 20.08H119.923V18.08H111.523V20.08ZM120.923 19.08V15.96H118.923V19.08H120.923ZM119.923 14.96H111.523V16.96H119.923V14.96ZM93.9635 31V34.6H95.9635V31H93.9635ZM94.9635 35.6H118.923V33.6H94.9635V35.6ZM119.923 34.6V31H117.923V34.6H119.923ZM118.923 30H94.9635V32H118.923V30ZM92.4835 36.6V40.44H94.4835V36.6H92.4835ZM93.4835 41.44H119.243V39.44H93.4835V41.44ZM120.243 40.44V36.6H118.243V40.44H120.243ZM119.243 35.6H93.4835V37.6H119.243V35.6ZM104.563 6.8V24.12H106.563V6.8H104.563ZM105.563 25.12H110.243V23.12H105.563V25.12ZM111.243 24.12V6.8H109.243V24.12H111.243ZM110.243 5.8H105.563V7.8H110.243V5.8ZM93.0435 25.32V29.08H95.0435V25.32H93.0435ZM94.0435 30.08H116.923V28.08H94.0435V30.08ZM115.923 29.08V41.72H117.923V29.08H115.923ZM116.923 42.72H121.683V40.72H116.923V42.72ZM122.683 41.72V25.32H120.683V41.72H122.683ZM121.683 24.32H94.0435V26.32H121.683V24.32ZM89.4035 10.76V19.92H91.4035V10.76H89.4035ZM90.4035 20.92H94.6435V18.92H90.4035V20.92ZM95.6435 19.92V14.44H93.6435V19.92H95.6435ZM94.6435 15.44H121.283V13.44H94.6435V15.44ZM120.283 14.44V19.92H122.283V14.44H120.283ZM121.283 20.92H125.723V18.92H121.283V20.92ZM126.723 19.92V10.76H124.723V19.92H126.723ZM125.723 9.76H90.4035V11.76H125.723V9.76Z",fill:r.iconStroke,mask:"url(#logo-icon-mask)"})]})},Dd=()=>o.jsxs("div",{className:"container py-8",children:[o.jsxs("div",{className:"mb-8",children:[o.jsx("h1",{className:"text-4xl font-bold mb-2",children:"BlogTruyen Logo System"}),o.jsx("p",{className:"text-muted-foreground",children:"Logo và branding elements cho BlogTruyen"})]}),o.jsx("section",{className:"mb-12",children:o.jsxs(X,{children:[o.jsxs(se,{children:[o.jsx(ee,{children:"Logo Variants"}),o.jsx(re,{children:"Các biến thể logo cho different contexts"})]}),o.jsx(J,{children:o.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8",children:[o.jsxs("div",{className:"space-y-4",children:[o.jsx("h3",{className:"font-semibold",children:"Default"}),o.jsx("div",{className:"p-6 border rounded-lg bg-background",children:o.jsx(xe,{variant:"default",size:"lg"})}),o.jsx("p",{className:"text-sm text-muted-foreground",children:"Sử dụng cho general purposes, follows current text color"})]}),o.jsxs("div",{className:"space-y-4",children:[o.jsx("h3",{className:"font-semibold",children:"Brand"}),o.jsx("div",{className:"p-6 border rounded-lg bg-background",children:o.jsx(xe,{variant:"brand",size:"lg"})}),o.jsx("p",{className:"text-sm text-muted-foreground",children:"Sử dụng brand colors (#E40066), ideal cho marketing materials"})]}),o.jsxs("div",{className:"space-y-4",children:[o.jsx("h3",{className:"font-semibold",children:"White"}),o.jsx("div",{className:"p-6 border rounded-lg bg-gray-900",children:o.jsx(xe,{variant:"white",size:"lg"})}),o.jsx("p",{className:"text-sm text-muted-foreground",children:"Cho dark backgrounds, all white colors"})]}),o.jsxs("div",{className:"space-y-4",children:[o.jsx("h3",{className:"font-semibold",children:"Dark"}),o.jsx("div",{className:"p-6 border rounded-lg bg-gray-100",children:o.jsx(xe,{variant:"dark",size:"lg"})}),o.jsx("p",{className:"text-sm text-muted-foreground",children:"Cho light backgrounds, dark colors"})]})]})})]})}),o.jsx("section",{className:"mb-12",children:o.jsxs(X,{children:[o.jsxs(se,{children:[o.jsx(ee,{children:"Logo Sizes"}),o.jsx(re,{children:"Các kích thước logo khác nhau"})]}),o.jsx(J,{children:o.jsxs("div",{className:"space-y-6",children:[o.jsxs("div",{className:"flex items-center gap-4",children:[o.jsx("span",{className:"w-16 text-sm font-medium",children:"Small:"}),o.jsx(xe,{variant:"brand",size:"sm"})]}),o.jsxs("div",{className:"flex items-center gap-4",children:[o.jsx("span",{className:"w-16 text-sm font-medium",children:"Medium:"}),o.jsx(xe,{variant:"brand",size:"md"})]}),o.jsxs("div",{className:"flex items-center gap-4",children:[o.jsx("span",{className:"w-16 text-sm font-medium",children:"Large:"}),o.jsx(xe,{variant:"brand",size:"lg"})]}),o.jsxs("div",{className:"flex items-center gap-4",children:[o.jsx("span",{className:"w-16 text-sm font-medium",children:"XLarge:"}),o.jsx(xe,{variant:"brand",size:"xl"})]})]})})]})}),o.jsx("section",{className:"mb-12",children:o.jsxs(X,{children:[o.jsxs(se,{children:[o.jsx(ee,{children:"Logo Icon"}),o.jsx(re,{children:"Icon-only version cho compact spaces"})]}),o.jsx(J,{children:o.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-6",children:[o.jsxs("div",{className:"text-center space-y-2",children:[o.jsx("div",{className:"p-4 border rounded-lg bg-background flex justify-center",children:o.jsx(xt,{variant:"default",size:32})}),o.jsx("p",{className:"text-xs text-muted-foreground",children:"Default"})]}),o.jsxs("div",{className:"text-center space-y-2",children:[o.jsx("div",{className:"p-4 border rounded-lg bg-background flex justify-center",children:o.jsx(xt,{variant:"brand",size:32})}),o.jsx("p",{className:"text-xs text-muted-foreground",children:"Brand"})]}),o.jsxs("div",{className:"text-center space-y-2",children:[o.jsx("div",{className:"p-4 border rounded-lg bg-gray-900 flex justify-center",children:o.jsx(xt,{variant:"white",size:32})}),o.jsx("p",{className:"text-xs text-muted-foreground",children:"White"})]}),o.jsxs("div",{className:"text-center space-y-2",children:[o.jsx("div",{className:"p-4 border rounded-lg bg-gray-100 flex justify-center",children:o.jsx(xt,{variant:"dark",size:32})}),o.jsx("p",{className:"text-xs text-muted-foreground",children:"Dark"})]})]})})]})}),o.jsx("section",{className:"mb-12",children:o.jsxs(X,{children:[o.jsxs(se,{children:[o.jsx(ee,{children:"Usage Guidelines"}),o.jsx(re,{children:"Hướng dẫn sử dụng logo đúng cách"})]}),o.jsx(J,{children:o.jsxs("div",{className:"space-y-6",children:[o.jsxs("div",{children:[o.jsx("h4",{className:"font-semibold mb-2",children:"✅ Do's"}),o.jsxs("ul",{className:"space-y-1 text-sm text-muted-foreground",children:[o.jsx("li",{children:"• Sử dụng brand variant cho marketing materials"}),o.jsx("li",{children:"• Maintain adequate clear space around logo"}),o.jsx("li",{children:"• Sử dụng appropriate size cho context"}),o.jsx("li",{children:"• Sử dụng white variant trên dark backgrounds"}),o.jsx("li",{children:"• Keep logo proportions intact"})]})]}),o.jsxs("div",{children:[o.jsx("h4",{className:"font-semibold mb-2",children:"❌ Don'ts"}),o.jsxs("ul",{className:"space-y-1 text-sm text-muted-foreground",children:[o.jsx("li",{children:"• Không stretch hoặc distort logo"}),o.jsx("li",{children:"• Không thay đổi colors ngoài provided variants"}),o.jsx("li",{children:"• Không place logo trên busy backgrounds"}),o.jsx("li",{children:"• Không sử dụng logo quá nhỏ (minimum 24px height)"}),o.jsx("li",{children:"• Không rotate hoặc skew logo"})]})]})]})})]})}),o.jsx("section",{children:o.jsxs(X,{children:[o.jsxs(se,{children:[o.jsx(ee,{children:"Code Examples"}),o.jsx(re,{children:"Cách sử dụng logo components trong code"})]}),o.jsx(J,{children:o.jsxs("div",{className:"space-y-4",children:[o.jsxs("div",{children:[o.jsx("h4",{className:"font-semibold mb-2",children:"Basic Usage"}),o.jsx("pre",{className:"p-4 bg-muted rounded-lg text-sm overflow-x-auto",children:`// Default logo
<Logo />

// Brand variant with large size
<Logo variant="brand" size="lg" />

// Icon only
<LogoIcon variant="brand" size={32} />

// Without text
<Logo showText={false} />`})]}),o.jsxs("div",{children:[o.jsx("h4",{className:"font-semibold mb-2",children:"Props"}),o.jsx("div",{className:"overflow-x-auto",children:o.jsxs("table",{className:"w-full text-sm",children:[o.jsx("thead",{children:o.jsxs("tr",{className:"border-b",children:[o.jsx("th",{className:"text-left p-2",children:"Prop"}),o.jsx("th",{className:"text-left p-2",children:"Type"}),o.jsx("th",{className:"text-left p-2",children:"Default"}),o.jsx("th",{className:"text-left p-2",children:"Description"})]})}),o.jsxs("tbody",{className:"text-muted-foreground",children:[o.jsxs("tr",{className:"border-b",children:[o.jsx("td",{className:"p-2",children:"variant"}),o.jsx("td",{className:"p-2",children:"'default' | 'brand' | 'white' | 'dark'"}),o.jsx("td",{className:"p-2",children:"'default'"}),o.jsx("td",{className:"p-2",children:"Color variant"})]}),o.jsxs("tr",{className:"border-b",children:[o.jsx("td",{className:"p-2",children:"size"}),o.jsx("td",{className:"p-2",children:"'sm' | 'md' | 'lg' | 'xl'"}),o.jsx("td",{className:"p-2",children:"'md'"}),o.jsx("td",{className:"p-2",children:"Logo size"})]}),o.jsxs("tr",{className:"border-b",children:[o.jsx("td",{className:"p-2",children:"showText"}),o.jsx("td",{className:"p-2",children:"boolean"}),o.jsx("td",{className:"p-2",children:"true"}),o.jsx("td",{className:"p-2",children:"Show text label"})]}),o.jsxs("tr",{children:[o.jsx("td",{className:"p-2",children:"className"}),o.jsx("td",{className:"p-2",children:"string"}),o.jsx("td",{className:"p-2",children:"-"}),o.jsx("td",{className:"p-2",children:"Additional CSS classes"})]})]})]})})]})]})})]})})]}),_d=Ao([{path:"/",element:o.jsx(rs,{}),children:[{index:!0,element:o.jsx(Sd,{})},{path:"browse",element:o.jsx(Rd,{})},{path:"manga/:id",element:o.jsx(Pd,{})},{path:"theme",element:o.jsx(Fd,{})},{path:"logo",element:o.jsx(Dd,{})},{path:"auth",element:o.jsx(os,{requireAuth:!1}),children:[{path:"login",element:o.jsx(Hd,{})},{path:"register",element:o.jsx(Ld,{})}]},{path:"user",element:o.jsx(os,{requireAuth:!0}),children:[{path:"bookmarks",element:o.jsx(Ad,{})},{path:"history",element:o.jsx(Td,{})},{path:"profile",element:o.jsx(Ed,{})}]}]},{path:"/read",element:o.jsx(Nd,{}),children:[{path:":chapterId",element:o.jsx(Od,{})}]},{path:"/admin",element:o.jsx(Cd,{}),children:[{index:!0,element:o.jsx("div",{children:"Admin Dashboard"})},{path:"manga",element:o.jsx("div",{children:"Manga Management"})},{path:"users",element:o.jsx("div",{children:"User Management"})},{path:"comments",element:o.jsx("div",{children:"Comment Management"})}]},{path:"*",element:o.jsx(rs,{}),children:[{path:"*",element:o.jsx(Id,{})}]}]),$d=()=>o.jsx(To,{router:_d}),A=t=>typeof t=="string",Je=()=>{let t,e;const n=new Promise((s,r)=>{t=s,e=r});return n.resolve=t,n.reject=e,n},is=t=>t==null?"":""+t,Zd=(t,e,n)=>{t.forEach(s=>{e[s]&&(n[s]=e[s])})},zd=/###/g,as=t=>t&&t.indexOf("###")>-1?t.replace(zd,"."):t,ls=t=>!t||A(t),et=(t,e,n)=>{const s=A(e)?e.split("."):e;let r=0;for(;r<s.length-1;){if(ls(t))return{};const i=as(s[r]);!t[i]&&n&&(t[i]=new n),Object.prototype.hasOwnProperty.call(t,i)?t=t[i]:t={},++r}return ls(t)?{}:{obj:t,k:as(s[r])}},cs=(t,e,n)=>{const{obj:s,k:r}=et(t,e,Object);if(s!==void 0||e.length===1){s[r]=n;return}let i=e[e.length-1],a=e.slice(0,e.length-1),l=et(t,a,Object);for(;l.obj===void 0&&a.length;)i=`${a[a.length-1]}.${i}`,a=a.slice(0,a.length-1),l=et(t,a,Object),l!=null&&l.obj&&typeof l.obj[`${l.k}.${i}`]<"u"&&(l.obj=void 0);l.obj[`${l.k}.${i}`]=n},Bd=(t,e,n,s)=>{const{obj:r,k:i}=et(t,e,Object);r[i]=r[i]||[],r[i].push(n)},Rt=(t,e)=>{const{obj:n,k:s}=et(t,e);if(n&&Object.prototype.hasOwnProperty.call(n,s))return n[s]},Kd=(t,e,n)=>{const s=Rt(t,n);return s!==void 0?s:Rt(e,n)},vo=(t,e,n)=>{for(const s in e)s!=="__proto__"&&s!=="constructor"&&(s in t?A(t[s])||t[s]instanceof String||A(e[s])||e[s]instanceof String?n&&(t[s]=e[s]):vo(t[s],e[s],n):t[s]=e[s]);return t},$e=t=>t.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,"\\$&");var Ud={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;","/":"&#x2F;"};const Gd=t=>A(t)?t.replace(/[&<>"'\/]/g,e=>Ud[e]):t;class Wd{constructor(e){this.capacity=e,this.regExpMap=new Map,this.regExpQueue=[]}getRegExp(e){const n=this.regExpMap.get(e);if(n!==void 0)return n;const s=new RegExp(e);return this.regExpQueue.length===this.capacity&&this.regExpMap.delete(this.regExpQueue.shift()),this.regExpMap.set(e,s),this.regExpQueue.push(e),s}}const qd=[" ",",","?","!",";"],Yd=new Wd(20),Xd=(t,e,n)=>{e=e||"",n=n||"";const s=qd.filter(a=>e.indexOf(a)<0&&n.indexOf(a)<0);if(s.length===0)return!0;const r=Yd.getRegExp(`(${s.map(a=>a==="?"?"\\?":a).join("|")})`);let i=!r.test(t);if(!i){const a=t.indexOf(n);a>0&&!r.test(t.substring(0,a))&&(i=!0)}return i},pn=(t,e,n=".")=>{if(!t)return;if(t[e])return Object.prototype.hasOwnProperty.call(t,e)?t[e]:void 0;const s=e.split(n);let r=t;for(let i=0;i<s.length;){if(!r||typeof r!="object")return;let a,l="";for(let d=i;d<s.length;++d)if(d!==i&&(l+=n),l+=s[d],a=r[l],a!==void 0){if(["string","number","boolean"].indexOf(typeof a)>-1&&d<s.length-1)continue;i+=d-i+1;break}r=a}return r},ot=t=>t==null?void 0:t.replace("_","-"),Jd={type:"logger",log(t){this.output("log",t)},warn(t){this.output("warn",t)},error(t){this.output("error",t)},output(t,e){var n,s;(s=(n=console==null?void 0:console[t])==null?void 0:n.apply)==null||s.call(n,console,e)}};class Pt{constructor(e,n={}){this.init(e,n)}init(e,n={}){this.prefix=n.prefix||"i18next:",this.logger=e||Jd,this.options=n,this.debug=n.debug}log(...e){return this.forward(e,"log","",!0)}warn(...e){return this.forward(e,"warn","",!0)}error(...e){return this.forward(e,"error","")}deprecate(...e){return this.forward(e,"warn","WARNING DEPRECATED: ",!0)}forward(e,n,s,r){return r&&!this.debug?null:(A(e[0])&&(e[0]=`${s}${this.prefix} ${e[0]}`),this.logger[n](e))}create(e){return new Pt(this.logger,{prefix:`${this.prefix}:${e}:`,...this.options})}clone(e){return e=e||this.options,e.prefix=e.prefix||this.prefix,new Pt(this.logger,e)}}var ye=new Pt;class zt{constructor(){this.observers={}}on(e,n){return e.split(" ").forEach(s=>{this.observers[s]||(this.observers[s]=new Map);const r=this.observers[s].get(n)||0;this.observers[s].set(n,r+1)}),this}off(e,n){if(this.observers[e]){if(!n){delete this.observers[e];return}this.observers[e].delete(n)}}emit(e,...n){this.observers[e]&&Array.from(this.observers[e].entries()).forEach(([r,i])=>{for(let a=0;a<i;a++)r(...n)}),this.observers["*"]&&Array.from(this.observers["*"].entries()).forEach(([r,i])=>{for(let a=0;a<i;a++)r.apply(r,[e,...n])})}}class ds extends zt{constructor(e,n={ns:["translation"],defaultNS:"translation"}){super(),this.data=e||{},this.options=n,this.options.keySeparator===void 0&&(this.options.keySeparator="."),this.options.ignoreJSONStructure===void 0&&(this.options.ignoreJSONStructure=!0)}addNamespaces(e){this.options.ns.indexOf(e)<0&&this.options.ns.push(e)}removeNamespaces(e){const n=this.options.ns.indexOf(e);n>-1&&this.options.ns.splice(n,1)}getResource(e,n,s,r={}){var c,f;const i=r.keySeparator!==void 0?r.keySeparator:this.options.keySeparator,a=r.ignoreJSONStructure!==void 0?r.ignoreJSONStructure:this.options.ignoreJSONStructure;let l;e.indexOf(".")>-1?l=e.split("."):(l=[e,n],s&&(Array.isArray(s)?l.push(...s):A(s)&&i?l.push(...s.split(i)):l.push(s)));const d=Rt(this.data,l);return!d&&!n&&!s&&e.indexOf(".")>-1&&(e=l[0],n=l[1],s=l.slice(2).join(".")),d||!a||!A(s)?d:pn((f=(c=this.data)==null?void 0:c[e])==null?void 0:f[n],s,i)}addResource(e,n,s,r,i={silent:!1}){const a=i.keySeparator!==void 0?i.keySeparator:this.options.keySeparator;let l=[e,n];s&&(l=l.concat(a?s.split(a):s)),e.indexOf(".")>-1&&(l=e.split("."),r=n,n=l[1]),this.addNamespaces(n),cs(this.data,l,r),i.silent||this.emit("added",e,n,s,r)}addResources(e,n,s,r={silent:!1}){for(const i in s)(A(s[i])||Array.isArray(s[i]))&&this.addResource(e,n,i,s[i],{silent:!0});r.silent||this.emit("added",e,n,s)}addResourceBundle(e,n,s,r,i,a={silent:!1,skipCopy:!1}){let l=[e,n];e.indexOf(".")>-1&&(l=e.split("."),r=s,s=n,n=l[1]),this.addNamespaces(n);let d=Rt(this.data,l)||{};a.skipCopy||(s=JSON.parse(JSON.stringify(s))),r?vo(d,s,i):d={...d,...s},cs(this.data,l,d),a.silent||this.emit("added",e,n,s)}removeResourceBundle(e,n){this.hasResourceBundle(e,n)&&delete this.data[e][n],this.removeNamespaces(n),this.emit("removed",e,n)}hasResourceBundle(e,n){return this.getResource(e,n)!==void 0}getResourceBundle(e,n){return n||(n=this.options.defaultNS),this.getResource(e,n)}getDataByLanguage(e){return this.data[e]}hasLanguageSomeTranslations(e){const n=this.getDataByLanguage(e);return!!(n&&Object.keys(n)||[]).find(r=>n[r]&&Object.keys(n[r]).length>0)}toJSON(){return this.data}}var wo={processors:{},addPostProcessor(t){this.processors[t.name]=t},handle(t,e,n,s,r){return t.forEach(i=>{var a;e=((a=this.processors[i])==null?void 0:a.process(e,n,s,r))??e}),e}};const us={},fs=t=>!A(t)&&typeof t!="boolean"&&typeof t!="number";class Ot extends zt{constructor(e,n={}){super(),Zd(["resourceStore","languageUtils","pluralResolver","interpolator","backendConnector","i18nFormat","utils"],e,this),this.options=n,this.options.keySeparator===void 0&&(this.options.keySeparator="."),this.logger=ye.create("translator")}changeLanguage(e){e&&(this.language=e)}exists(e,n={interpolation:{}}){const s={...n};if(e==null)return!1;const r=this.resolve(e,s);return(r==null?void 0:r.res)!==void 0}extractFromKey(e,n){let s=n.nsSeparator!==void 0?n.nsSeparator:this.options.nsSeparator;s===void 0&&(s=":");const r=n.keySeparator!==void 0?n.keySeparator:this.options.keySeparator;let i=n.ns||this.options.defaultNS||[];const a=s&&e.indexOf(s)>-1,l=!this.options.userDefinedKeySeparator&&!n.keySeparator&&!this.options.userDefinedNsSeparator&&!n.nsSeparator&&!Xd(e,s,r);if(a&&!l){const d=e.match(this.interpolator.nestingRegexp);if(d&&d.length>0)return{key:e,namespaces:A(i)?[i]:i};const c=e.split(s);(s!==r||s===r&&this.options.ns.indexOf(c[0])>-1)&&(i=c.shift()),e=c.join(r)}return{key:e,namespaces:A(i)?[i]:i}}translate(e,n,s){let r=typeof n=="object"?{...n}:n;if(typeof r!="object"&&this.options.overloadTranslationOptionHandler&&(r=this.options.overloadTranslationOptionHandler(arguments)),typeof options=="object"&&(r={...r}),r||(r={}),e==null)return"";Array.isArray(e)||(e=[String(e)]);const i=r.returnDetails!==void 0?r.returnDetails:this.options.returnDetails,a=r.keySeparator!==void 0?r.keySeparator:this.options.keySeparator,{key:l,namespaces:d}=this.extractFromKey(e[e.length-1],r),c=d[d.length-1];let f=r.nsSeparator!==void 0?r.nsSeparator:this.options.nsSeparator;f===void 0&&(f=":");const u=r.lng||this.language,h=r.appendNamespaceToCIMode||this.options.appendNamespaceToCIMode;if((u==null?void 0:u.toLowerCase())==="cimode")return h?i?{res:`${c}${f}${l}`,usedKey:l,exactUsedKey:l,usedLng:u,usedNS:c,usedParams:this.getUsedParamsDetails(r)}:`${c}${f}${l}`:i?{res:l,usedKey:l,exactUsedKey:l,usedLng:u,usedNS:c,usedParams:this.getUsedParamsDetails(r)}:l;const m=this.resolve(e,r);let g=m==null?void 0:m.res;const p=(m==null?void 0:m.usedKey)||l,b=(m==null?void 0:m.exactUsedKey)||l,v=["[object Number]","[object Function]","[object RegExp]"],y=r.joinArrays!==void 0?r.joinArrays:this.options.joinArrays,N=!this.i18nFormat||this.i18nFormat.handleAsObject,C=r.count!==void 0&&!A(r.count),M=Ot.hasDefaultValue(r),P=C?this.pluralResolver.getSuffix(u,r.count,r):"",O=r.ordinal&&C?this.pluralResolver.getSuffix(u,r.count,{ordinal:!1}):"",w=C&&!r.ordinal&&r.count===0,H=w&&r[`defaultValue${this.options.pluralSeparator}zero`]||r[`defaultValue${P}`]||r[`defaultValue${O}`]||r.defaultValue;let R=g;N&&!g&&M&&(R=H);const D=fs(R),z=Object.prototype.toString.apply(R);if(N&&R&&D&&v.indexOf(z)<0&&!(A(y)&&Array.isArray(R))){if(!r.returnObjects&&!this.options.returnObjects){this.options.returnedObjectHandler||this.logger.warn("accessing an object - but returnObjects options is not enabled!");const F=this.options.returnedObjectHandler?this.options.returnedObjectHandler(p,R,{...r,ns:d}):`key '${l} (${this.language})' returned an object instead of string.`;return i?(m.res=F,m.usedParams=this.getUsedParamsDetails(r),m):F}if(a){const F=Array.isArray(R),T=F?[]:{},K=F?b:p;for(const L in R)if(Object.prototype.hasOwnProperty.call(R,L)){const E=`${K}${a}${L}`;M&&!g?T[L]=this.translate(E,{...r,defaultValue:fs(H)?H[L]:void 0,joinArrays:!1,ns:d}):T[L]=this.translate(E,{...r,joinArrays:!1,ns:d}),T[L]===E&&(T[L]=R[L])}g=T}}else if(N&&A(y)&&Array.isArray(g))g=g.join(y),g&&(g=this.extendTranslation(g,e,r,s));else{let F=!1,T=!1;!this.isValidLookup(g)&&M&&(F=!0,g=H),this.isValidLookup(g)||(T=!0,g=l);const L=(r.missingKeyNoValueFallbackToKey||this.options.missingKeyNoValueFallbackToKey)&&T?void 0:g,E=M&&H!==g&&this.options.updateMissing;if(T||F||E){if(this.logger.log(E?"updateKey":"missingKey",u,c,l,E?H:g),a){const _=this.resolve(l,{...r,keySeparator:!1});_&&_.res&&this.logger.warn("Seems the loaded translations were in flat JSON format instead of nested. Either set keySeparator: false on init or make sure your translations are published in nested format.")}let j=[];const S=this.languageUtils.getFallbackCodes(this.options.fallbackLng,r.lng||this.language);if(this.options.saveMissingTo==="fallback"&&S&&S[0])for(let _=0;_<S.length;_++)j.push(S[_]);else this.options.saveMissingTo==="all"?j=this.languageUtils.toResolveHierarchy(r.lng||this.language):j.push(r.lng||this.language);const Z=(_,W,U)=>{var de;const q=M&&U!==g?U:L;this.options.missingKeyHandler?this.options.missingKeyHandler(_,c,W,q,E,r):(de=this.backendConnector)!=null&&de.saveMissing&&this.backendConnector.saveMissing(_,c,W,q,E,r),this.emit("missingKey",_,c,W,g)};this.options.saveMissing&&(this.options.saveMissingPlurals&&C?j.forEach(_=>{const W=this.pluralResolver.getSuffixes(_,r);w&&r[`defaultValue${this.options.pluralSeparator}zero`]&&W.indexOf(`${this.options.pluralSeparator}zero`)<0&&W.push(`${this.options.pluralSeparator}zero`),W.forEach(U=>{Z([_],l+U,r[`defaultValue${U}`]||H)})}):Z(j,l,H))}g=this.extendTranslation(g,e,r,m,s),T&&g===l&&this.options.appendNamespaceToMissingKey&&(g=`${c}${f}${l}`),(T||F)&&this.options.parseMissingKeyHandler&&(g=this.options.parseMissingKeyHandler(this.options.appendNamespaceToMissingKey?`${c}${f}${l}`:l,F?g:void 0,r))}return i?(m.res=g,m.usedParams=this.getUsedParamsDetails(r),m):g}extendTranslation(e,n,s,r,i){var d,c;if((d=this.i18nFormat)!=null&&d.parse)e=this.i18nFormat.parse(e,{...this.options.interpolation.defaultVariables,...s},s.lng||this.language||r.usedLng,r.usedNS,r.usedKey,{resolved:r});else if(!s.skipInterpolation){s.interpolation&&this.interpolator.init({...s,interpolation:{...this.options.interpolation,...s.interpolation}});const f=A(e)&&(((c=s==null?void 0:s.interpolation)==null?void 0:c.skipOnVariables)!==void 0?s.interpolation.skipOnVariables:this.options.interpolation.skipOnVariables);let u;if(f){const m=e.match(this.interpolator.nestingRegexp);u=m&&m.length}let h=s.replace&&!A(s.replace)?s.replace:s;if(this.options.interpolation.defaultVariables&&(h={...this.options.interpolation.defaultVariables,...h}),e=this.interpolator.interpolate(e,h,s.lng||this.language||r.usedLng,s),f){const m=e.match(this.interpolator.nestingRegexp),g=m&&m.length;u<g&&(s.nest=!1)}!s.lng&&r&&r.res&&(s.lng=this.language||r.usedLng),s.nest!==!1&&(e=this.interpolator.nest(e,(...m)=>(i==null?void 0:i[0])===m[0]&&!s.context?(this.logger.warn(`It seems you are nesting recursively key: ${m[0]} in key: ${n[0]}`),null):this.translate(...m,n),s)),s.interpolation&&this.interpolator.reset()}const a=s.postProcess||this.options.postProcess,l=A(a)?[a]:a;return e!=null&&(l!=null&&l.length)&&s.applyPostProcessor!==!1&&(e=wo.handle(l,e,n,this.options&&this.options.postProcessPassResolved?{i18nResolved:{...r,usedParams:this.getUsedParamsDetails(s)},...s}:s,this)),e}resolve(e,n={}){let s,r,i,a,l;return A(e)&&(e=[e]),e.forEach(d=>{if(this.isValidLookup(s))return;const c=this.extractFromKey(d,n),f=c.key;r=f;let u=c.namespaces;this.options.fallbackNS&&(u=u.concat(this.options.fallbackNS));const h=n.count!==void 0&&!A(n.count),m=h&&!n.ordinal&&n.count===0,g=n.context!==void 0&&(A(n.context)||typeof n.context=="number")&&n.context!=="",p=n.lngs?n.lngs:this.languageUtils.toResolveHierarchy(n.lng||this.language,n.fallbackLng);u.forEach(b=>{var v,y;this.isValidLookup(s)||(l=b,!us[`${p[0]}-${b}`]&&((v=this.utils)!=null&&v.hasLoadedNamespace)&&!((y=this.utils)!=null&&y.hasLoadedNamespace(l))&&(us[`${p[0]}-${b}`]=!0,this.logger.warn(`key "${r}" for languages "${p.join(", ")}" won't get resolved as namespace "${l}" was not yet loaded`,"This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!")),p.forEach(N=>{var P;if(this.isValidLookup(s))return;a=N;const C=[f];if((P=this.i18nFormat)!=null&&P.addLookupKeys)this.i18nFormat.addLookupKeys(C,f,N,b,n);else{let O;h&&(O=this.pluralResolver.getSuffix(N,n.count,n));const w=`${this.options.pluralSeparator}zero`,H=`${this.options.pluralSeparator}ordinal${this.options.pluralSeparator}`;if(h&&(C.push(f+O),n.ordinal&&O.indexOf(H)===0&&C.push(f+O.replace(H,this.options.pluralSeparator)),m&&C.push(f+w)),g){const R=`${f}${this.options.contextSeparator}${n.context}`;C.push(R),h&&(C.push(R+O),n.ordinal&&O.indexOf(H)===0&&C.push(R+O.replace(H,this.options.pluralSeparator)),m&&C.push(R+w))}}let M;for(;M=C.pop();)this.isValidLookup(s)||(i=M,s=this.getResource(N,b,M,n))}))})}),{res:s,usedKey:r,exactUsedKey:i,usedLng:a,usedNS:l}}isValidLookup(e){return e!==void 0&&!(!this.options.returnNull&&e===null)&&!(!this.options.returnEmptyString&&e==="")}getResource(e,n,s,r={}){var i;return(i=this.i18nFormat)!=null&&i.getResource?this.i18nFormat.getResource(e,n,s,r):this.resourceStore.getResource(e,n,s,r)}getUsedParamsDetails(e={}){const n=["defaultValue","ordinal","context","replace","lng","lngs","fallbackLng","ns","keySeparator","nsSeparator","returnObjects","returnDetails","joinArrays","postProcess","interpolation"],s=e.replace&&!A(e.replace);let r=s?e.replace:e;if(s&&typeof e.count<"u"&&(r.count=e.count),this.options.interpolation.defaultVariables&&(r={...this.options.interpolation.defaultVariables,...r}),!s){r={...r};for(const i of n)delete r[i]}return r}static hasDefaultValue(e){const n="defaultValue";for(const s in e)if(Object.prototype.hasOwnProperty.call(e,s)&&n===s.substring(0,n.length)&&e[s]!==void 0)return!0;return!1}}class hs{constructor(e){this.options=e,this.supportedLngs=this.options.supportedLngs||!1,this.logger=ye.create("languageUtils")}getScriptPartFromCode(e){if(e=ot(e),!e||e.indexOf("-")<0)return null;const n=e.split("-");return n.length===2||(n.pop(),n[n.length-1].toLowerCase()==="x")?null:this.formatLanguageCode(n.join("-"))}getLanguagePartFromCode(e){if(e=ot(e),!e||e.indexOf("-")<0)return e;const n=e.split("-");return this.formatLanguageCode(n[0])}formatLanguageCode(e){if(A(e)&&e.indexOf("-")>-1){let n;try{n=Intl.getCanonicalLocales(e)[0]}catch{}return n&&this.options.lowerCaseLng&&(n=n.toLowerCase()),n||(this.options.lowerCaseLng?e.toLowerCase():e)}return this.options.cleanCode||this.options.lowerCaseLng?e.toLowerCase():e}isSupportedCode(e){return(this.options.load==="languageOnly"||this.options.nonExplicitSupportedLngs)&&(e=this.getLanguagePartFromCode(e)),!this.supportedLngs||!this.supportedLngs.length||this.supportedLngs.indexOf(e)>-1}getBestMatchFromCodes(e){if(!e)return null;let n;return e.forEach(s=>{if(n)return;const r=this.formatLanguageCode(s);(!this.options.supportedLngs||this.isSupportedCode(r))&&(n=r)}),!n&&this.options.supportedLngs&&e.forEach(s=>{if(n)return;const r=this.getScriptPartFromCode(s);if(this.isSupportedCode(r))return n=r;const i=this.getLanguagePartFromCode(s);if(this.isSupportedCode(i))return n=i;n=this.options.supportedLngs.find(a=>{if(a===i)return a;if(!(a.indexOf("-")<0&&i.indexOf("-")<0)&&(a.indexOf("-")>0&&i.indexOf("-")<0&&a.substring(0,a.indexOf("-"))===i||a.indexOf(i)===0&&i.length>1))return a})}),n||(n=this.getFallbackCodes(this.options.fallbackLng)[0]),n}getFallbackCodes(e,n){if(!e)return[];if(typeof e=="function"&&(e=e(n)),A(e)&&(e=[e]),Array.isArray(e))return e;if(!n)return e.default||[];let s=e[n];return s||(s=e[this.getScriptPartFromCode(n)]),s||(s=e[this.formatLanguageCode(n)]),s||(s=e[this.getLanguagePartFromCode(n)]),s||(s=e.default),s||[]}toResolveHierarchy(e,n){const s=this.getFallbackCodes((n===!1?[]:n)||this.options.fallbackLng||[],e),r=[],i=a=>{a&&(this.isSupportedCode(a)?r.push(a):this.logger.warn(`rejecting language code not found in supportedLngs: ${a}`))};return A(e)&&(e.indexOf("-")>-1||e.indexOf("_")>-1)?(this.options.load!=="languageOnly"&&i(this.formatLanguageCode(e)),this.options.load!=="languageOnly"&&this.options.load!=="currentOnly"&&i(this.getScriptPartFromCode(e)),this.options.load!=="currentOnly"&&i(this.getLanguagePartFromCode(e))):A(e)&&i(this.formatLanguageCode(e)),s.forEach(a=>{r.indexOf(a)<0&&i(this.formatLanguageCode(a))}),r}}const ms={zero:0,one:1,two:2,few:3,many:4,other:5},gs={select:t=>t===1?"one":"other",resolvedOptions:()=>({pluralCategories:["one","other"]})};class Qd{constructor(e,n={}){this.languageUtils=e,this.options=n,this.logger=ye.create("pluralResolver"),this.pluralRulesCache={}}addRule(e,n){this.rules[e]=n}clearCache(){this.pluralRulesCache={}}getRule(e,n={}){const s=ot(e==="dev"?"en":e),r=n.ordinal?"ordinal":"cardinal",i=JSON.stringify({cleanedCode:s,type:r});if(i in this.pluralRulesCache)return this.pluralRulesCache[i];let a;try{a=new Intl.PluralRules(s,{type:r})}catch{if(!Intl)return this.logger.error("No Intl support, please use an Intl polyfill!"),gs;if(!e.match(/-|_/))return gs;const d=this.languageUtils.getLanguagePartFromCode(e);a=this.getRule(d,n)}return this.pluralRulesCache[i]=a,a}needsPlural(e,n={}){let s=this.getRule(e,n);return s||(s=this.getRule("dev",n)),(s==null?void 0:s.resolvedOptions().pluralCategories.length)>1}getPluralFormsOfKey(e,n,s={}){return this.getSuffixes(e,s).map(r=>`${n}${r}`)}getSuffixes(e,n={}){let s=this.getRule(e,n);return s||(s=this.getRule("dev",n)),s?s.resolvedOptions().pluralCategories.sort((r,i)=>ms[r]-ms[i]).map(r=>`${this.options.prepend}${n.ordinal?`ordinal${this.options.prepend}`:""}${r}`):[]}getSuffix(e,n,s={}){const r=this.getRule(e,s);return r?`${this.options.prepend}${s.ordinal?`ordinal${this.options.prepend}`:""}${r.select(n)}`:(this.logger.warn(`no plural rule found for: ${e}`),this.getSuffix("dev",n,s))}}const ps=(t,e,n,s=".",r=!0)=>{let i=Kd(t,e,n);return!i&&r&&A(n)&&(i=pn(t,n,s),i===void 0&&(i=pn(e,n,s))),i},Yt=t=>t.replace(/\$/g,"$$$$");class eu{constructor(e={}){var n;this.logger=ye.create("interpolator"),this.options=e,this.format=((n=e==null?void 0:e.interpolation)==null?void 0:n.format)||(s=>s),this.init(e)}init(e={}){e.interpolation||(e.interpolation={escapeValue:!0});const{escape:n,escapeValue:s,useRawValueToEscape:r,prefix:i,prefixEscaped:a,suffix:l,suffixEscaped:d,formatSeparator:c,unescapeSuffix:f,unescapePrefix:u,nestingPrefix:h,nestingPrefixEscaped:m,nestingSuffix:g,nestingSuffixEscaped:p,nestingOptionsSeparator:b,maxReplaces:v,alwaysFormat:y}=e.interpolation;this.escape=n!==void 0?n:Gd,this.escapeValue=s!==void 0?s:!0,this.useRawValueToEscape=r!==void 0?r:!1,this.prefix=i?$e(i):a||"{{",this.suffix=l?$e(l):d||"}}",this.formatSeparator=c||",",this.unescapePrefix=f?"":u||"-",this.unescapeSuffix=this.unescapePrefix?"":f||"",this.nestingPrefix=h?$e(h):m||$e("$t("),this.nestingSuffix=g?$e(g):p||$e(")"),this.nestingOptionsSeparator=b||",",this.maxReplaces=v||1e3,this.alwaysFormat=y!==void 0?y:!1,this.resetRegExp()}reset(){this.options&&this.init(this.options)}resetRegExp(){const e=(n,s)=>(n==null?void 0:n.source)===s?(n.lastIndex=0,n):new RegExp(s,"g");this.regexp=e(this.regexp,`${this.prefix}(.+?)${this.suffix}`),this.regexpUnescape=e(this.regexpUnescape,`${this.prefix}${this.unescapePrefix}(.+?)${this.unescapeSuffix}${this.suffix}`),this.nestingRegexp=e(this.nestingRegexp,`${this.nestingPrefix}(.+?)${this.nestingSuffix}`)}interpolate(e,n,s,r){var m;let i,a,l;const d=this.options&&this.options.interpolation&&this.options.interpolation.defaultVariables||{},c=g=>{if(g.indexOf(this.formatSeparator)<0){const y=ps(n,d,g,this.options.keySeparator,this.options.ignoreJSONStructure);return this.alwaysFormat?this.format(y,void 0,s,{...r,...n,interpolationkey:g}):y}const p=g.split(this.formatSeparator),b=p.shift().trim(),v=p.join(this.formatSeparator).trim();return this.format(ps(n,d,b,this.options.keySeparator,this.options.ignoreJSONStructure),v,s,{...r,...n,interpolationkey:b})};this.resetRegExp();const f=(r==null?void 0:r.missingInterpolationHandler)||this.options.missingInterpolationHandler,u=((m=r==null?void 0:r.interpolation)==null?void 0:m.skipOnVariables)!==void 0?r.interpolation.skipOnVariables:this.options.interpolation.skipOnVariables;return[{regex:this.regexpUnescape,safeValue:g=>Yt(g)},{regex:this.regexp,safeValue:g=>this.escapeValue?Yt(this.escape(g)):Yt(g)}].forEach(g=>{for(l=0;i=g.regex.exec(e);){const p=i[1].trim();if(a=c(p),a===void 0)if(typeof f=="function"){const v=f(e,i,r);a=A(v)?v:""}else if(r&&Object.prototype.hasOwnProperty.call(r,p))a="";else if(u){a=i[0];continue}else this.logger.warn(`missed to pass in variable ${p} for interpolating ${e}`),a="";else!A(a)&&!this.useRawValueToEscape&&(a=is(a));const b=g.safeValue(a);if(e=e.replace(i[0],b),u?(g.regex.lastIndex+=a.length,g.regex.lastIndex-=i[0].length):g.regex.lastIndex=0,l++,l>=this.maxReplaces)break}}),e}nest(e,n,s={}){let r,i,a;const l=(d,c)=>{const f=this.nestingOptionsSeparator;if(d.indexOf(f)<0)return d;const u=d.split(new RegExp(`${f}[ ]*{`));let h=`{${u[1]}`;d=u[0],h=this.interpolate(h,a);const m=h.match(/'/g),g=h.match(/"/g);(((m==null?void 0:m.length)??0)%2===0&&!g||g.length%2!==0)&&(h=h.replace(/'/g,'"'));try{a=JSON.parse(h),c&&(a={...c,...a})}catch(p){return this.logger.warn(`failed parsing options string in nesting for key ${d}`,p),`${d}${f}${h}`}return a.defaultValue&&a.defaultValue.indexOf(this.prefix)>-1&&delete a.defaultValue,d};for(;r=this.nestingRegexp.exec(e);){let d=[];a={...s},a=a.replace&&!A(a.replace)?a.replace:a,a.applyPostProcessor=!1,delete a.defaultValue;let c=!1;if(r[0].indexOf(this.formatSeparator)!==-1&&!/{.*}/.test(r[1])){const f=r[1].split(this.formatSeparator).map(u=>u.trim());r[1]=f.shift(),d=f,c=!0}if(i=n(l.call(this,r[1].trim(),a),a),i&&r[0]===e&&!A(i))return i;A(i)||(i=is(i)),i||(this.logger.warn(`missed to resolve ${r[1]} for nesting ${e}`),i=""),c&&(i=d.reduce((f,u)=>this.format(f,u,s.lng,{...s,interpolationkey:r[1].trim()}),i.trim())),e=e.replace(r[0],i),this.regexp.lastIndex=0}return e}}const tu=t=>{let e=t.toLowerCase().trim();const n={};if(t.indexOf("(")>-1){const s=t.split("(");e=s[0].toLowerCase().trim();const r=s[1].substring(0,s[1].length-1);e==="currency"&&r.indexOf(":")<0?n.currency||(n.currency=r.trim()):e==="relativetime"&&r.indexOf(":")<0?n.range||(n.range=r.trim()):r.split(";").forEach(a=>{if(a){const[l,...d]=a.split(":"),c=d.join(":").trim().replace(/^'+|'+$/g,""),f=l.trim();n[f]||(n[f]=c),c==="false"&&(n[f]=!1),c==="true"&&(n[f]=!0),isNaN(c)||(n[f]=parseInt(c,10))}})}return{formatName:e,formatOptions:n}},xs=t=>{const e={};return(n,s,r)=>{let i=r;r&&r.interpolationkey&&r.formatParams&&r.formatParams[r.interpolationkey]&&r[r.interpolationkey]&&(i={...i,[r.interpolationkey]:void 0});const a=s+JSON.stringify(i);let l=e[a];return l||(l=t(ot(s),r),e[a]=l),l(n)}},nu=t=>(e,n,s)=>t(ot(n),s)(e);class su{constructor(e={}){this.logger=ye.create("formatter"),this.options=e,this.init(e)}init(e,n={interpolation:{}}){this.formatSeparator=n.interpolation.formatSeparator||",";const s=n.cacheInBuiltFormats?xs:nu;this.formats={number:s((r,i)=>{const a=new Intl.NumberFormat(r,{...i});return l=>a.format(l)}),currency:s((r,i)=>{const a=new Intl.NumberFormat(r,{...i,style:"currency"});return l=>a.format(l)}),datetime:s((r,i)=>{const a=new Intl.DateTimeFormat(r,{...i});return l=>a.format(l)}),relativetime:s((r,i)=>{const a=new Intl.RelativeTimeFormat(r,{...i});return l=>a.format(l,i.range||"day")}),list:s((r,i)=>{const a=new Intl.ListFormat(r,{...i});return l=>a.format(l)})}}add(e,n){this.formats[e.toLowerCase().trim()]=n}addCached(e,n){this.formats[e.toLowerCase().trim()]=xs(n)}format(e,n,s,r={}){const i=n.split(this.formatSeparator);if(i.length>1&&i[0].indexOf("(")>1&&i[0].indexOf(")")<0&&i.find(l=>l.indexOf(")")>-1)){const l=i.findIndex(d=>d.indexOf(")")>-1);i[0]=[i[0],...i.splice(1,l)].join(this.formatSeparator)}return i.reduce((l,d)=>{var u;const{formatName:c,formatOptions:f}=tu(d);if(this.formats[c]){let h=l;try{const m=((u=r==null?void 0:r.formatParams)==null?void 0:u[r.interpolationkey])||{},g=m.locale||m.lng||r.locale||r.lng||s;h=this.formats[c](l,g,{...f,...r,...m})}catch(m){this.logger.warn(m)}return h}else this.logger.warn(`there was no format function for ${c}`);return l},e)}}const ru=(t,e)=>{t.pending[e]!==void 0&&(delete t.pending[e],t.pendingCount--)};class ou extends zt{constructor(e,n,s,r={}){var i,a;super(),this.backend=e,this.store=n,this.services=s,this.languageUtils=s.languageUtils,this.options=r,this.logger=ye.create("backendConnector"),this.waitingReads=[],this.maxParallelReads=r.maxParallelReads||10,this.readingCalls=0,this.maxRetries=r.maxRetries>=0?r.maxRetries:5,this.retryTimeout=r.retryTimeout>=1?r.retryTimeout:350,this.state={},this.queue=[],(a=(i=this.backend)==null?void 0:i.init)==null||a.call(i,s,r.backend,r)}queueLoad(e,n,s,r){const i={},a={},l={},d={};return e.forEach(c=>{let f=!0;n.forEach(u=>{const h=`${c}|${u}`;!s.reload&&this.store.hasResourceBundle(c,u)?this.state[h]=2:this.state[h]<0||(this.state[h]===1?a[h]===void 0&&(a[h]=!0):(this.state[h]=1,f=!1,a[h]===void 0&&(a[h]=!0),i[h]===void 0&&(i[h]=!0),d[u]===void 0&&(d[u]=!0)))}),f||(l[c]=!0)}),(Object.keys(i).length||Object.keys(a).length)&&this.queue.push({pending:a,pendingCount:Object.keys(a).length,loaded:{},errors:[],callback:r}),{toLoad:Object.keys(i),pending:Object.keys(a),toLoadLanguages:Object.keys(l),toLoadNamespaces:Object.keys(d)}}loaded(e,n,s){const r=e.split("|"),i=r[0],a=r[1];n&&this.emit("failedLoading",i,a,n),!n&&s&&this.store.addResourceBundle(i,a,s,void 0,void 0,{skipCopy:!0}),this.state[e]=n?-1:2,n&&s&&(this.state[e]=0);const l={};this.queue.forEach(d=>{Bd(d.loaded,[i],a),ru(d,e),n&&d.errors.push(n),d.pendingCount===0&&!d.done&&(Object.keys(d.loaded).forEach(c=>{l[c]||(l[c]={});const f=d.loaded[c];f.length&&f.forEach(u=>{l[c][u]===void 0&&(l[c][u]=!0)})}),d.done=!0,d.errors.length?d.callback(d.errors):d.callback())}),this.emit("loaded",l),this.queue=this.queue.filter(d=>!d.done)}read(e,n,s,r=0,i=this.retryTimeout,a){if(!e.length)return a(null,{});if(this.readingCalls>=this.maxParallelReads){this.waitingReads.push({lng:e,ns:n,fcName:s,tried:r,wait:i,callback:a});return}this.readingCalls++;const l=(c,f)=>{if(this.readingCalls--,this.waitingReads.length>0){const u=this.waitingReads.shift();this.read(u.lng,u.ns,u.fcName,u.tried,u.wait,u.callback)}if(c&&f&&r<this.maxRetries){setTimeout(()=>{this.read.call(this,e,n,s,r+1,i*2,a)},i);return}a(c,f)},d=this.backend[s].bind(this.backend);if(d.length===2){try{const c=d(e,n);c&&typeof c.then=="function"?c.then(f=>l(null,f)).catch(l):l(null,c)}catch(c){l(c)}return}return d(e,n,l)}prepareLoading(e,n,s={},r){if(!this.backend)return this.logger.warn("No backend was added via i18next.use. Will not load resources."),r&&r();A(e)&&(e=this.languageUtils.toResolveHierarchy(e)),A(n)&&(n=[n]);const i=this.queueLoad(e,n,s,r);if(!i.toLoad.length)return i.pending.length||r(),null;i.toLoad.forEach(a=>{this.loadOne(a)})}load(e,n,s){this.prepareLoading(e,n,{},s)}reload(e,n,s){this.prepareLoading(e,n,{reload:!0},s)}loadOne(e,n=""){const s=e.split("|"),r=s[0],i=s[1];this.read(r,i,"read",void 0,void 0,(a,l)=>{a&&this.logger.warn(`${n}loading namespace ${i} for language ${r} failed`,a),!a&&l&&this.logger.log(`${n}loaded namespace ${i} for language ${r}`,l),this.loaded(e,a,l)})}saveMissing(e,n,s,r,i,a={},l=()=>{}){var d,c,f,u,h;if((c=(d=this.services)==null?void 0:d.utils)!=null&&c.hasLoadedNamespace&&!((u=(f=this.services)==null?void 0:f.utils)!=null&&u.hasLoadedNamespace(n))){this.logger.warn(`did not save key "${s}" as the namespace "${n}" was not yet loaded`,"This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!");return}if(!(s==null||s==="")){if((h=this.backend)!=null&&h.create){const m={...a,isUpdate:i},g=this.backend.create.bind(this.backend);if(g.length<6)try{let p;g.length===5?p=g(e,n,s,r,m):p=g(e,n,s,r),p&&typeof p.then=="function"?p.then(b=>l(null,b)).catch(l):l(null,p)}catch(p){l(p)}else g(e,n,s,r,l,m)}!e||!e[0]||this.store.addResource(e[0],n,s,r)}}}const bs=()=>({debug:!1,initAsync:!0,ns:["translation"],defaultNS:["translation"],fallbackLng:["dev"],fallbackNS:!1,supportedLngs:!1,nonExplicitSupportedLngs:!1,load:"all",preload:!1,simplifyPluralSuffix:!0,keySeparator:".",nsSeparator:":",pluralSeparator:"_",contextSeparator:"_",partialBundledLanguages:!1,saveMissing:!1,updateMissing:!1,saveMissingTo:"fallback",saveMissingPlurals:!0,missingKeyHandler:!1,missingInterpolationHandler:!1,postProcess:!1,postProcessPassResolved:!1,returnNull:!1,returnEmptyString:!0,returnObjects:!1,joinArrays:!1,returnedObjectHandler:!1,parseMissingKeyHandler:!1,appendNamespaceToMissingKey:!1,appendNamespaceToCIMode:!1,overloadTranslationOptionHandler:t=>{let e={};if(typeof t[1]=="object"&&(e=t[1]),A(t[1])&&(e.defaultValue=t[1]),A(t[2])&&(e.tDescription=t[2]),typeof t[2]=="object"||typeof t[3]=="object"){const n=t[3]||t[2];Object.keys(n).forEach(s=>{e[s]=n[s]})}return e},interpolation:{escapeValue:!0,format:t=>t,prefix:"{{",suffix:"}}",formatSeparator:",",unescapePrefix:"-",nestingPrefix:"$t(",nestingSuffix:")",nestingOptionsSeparator:",",maxReplaces:1e3,skipOnVariables:!0},cacheInBuiltFormats:!0}),ys=t=>{var e,n;return A(t.ns)&&(t.ns=[t.ns]),A(t.fallbackLng)&&(t.fallbackLng=[t.fallbackLng]),A(t.fallbackNS)&&(t.fallbackNS=[t.fallbackNS]),((n=(e=t.supportedLngs)==null?void 0:e.indexOf)==null?void 0:n.call(e,"cimode"))<0&&(t.supportedLngs=t.supportedLngs.concat(["cimode"])),typeof t.initImmediate=="boolean"&&(t.initAsync=t.initImmediate),t},bt=()=>{},iu=t=>{Object.getOwnPropertyNames(Object.getPrototypeOf(t)).forEach(n=>{typeof t[n]=="function"&&(t[n]=t[n].bind(t))})};class it extends zt{constructor(e={},n){if(super(),this.options=ys(e),this.services={},this.logger=ye,this.modules={external:[]},iu(this),n&&!this.isInitialized&&!e.isClone){if(!this.options.initAsync)return this.init(e,n),this;setTimeout(()=>{this.init(e,n)},0)}}init(e={},n){this.isInitializing=!0,typeof e=="function"&&(n=e,e={}),e.defaultNS==null&&e.ns&&(A(e.ns)?e.defaultNS=e.ns:e.ns.indexOf("translation")<0&&(e.defaultNS=e.ns[0]));const s=bs();this.options={...s,...this.options,...ys(e)},this.options.interpolation={...s.interpolation,...this.options.interpolation},e.keySeparator!==void 0&&(this.options.userDefinedKeySeparator=e.keySeparator),e.nsSeparator!==void 0&&(this.options.userDefinedNsSeparator=e.nsSeparator);const r=c=>c?typeof c=="function"?new c:c:null;if(!this.options.isClone){this.modules.logger?ye.init(r(this.modules.logger),this.options):ye.init(null,this.options);let c;this.modules.formatter?c=this.modules.formatter:c=su;const f=new hs(this.options);this.store=new ds(this.options.resources,this.options);const u=this.services;u.logger=ye,u.resourceStore=this.store,u.languageUtils=f,u.pluralResolver=new Qd(f,{prepend:this.options.pluralSeparator,simplifyPluralSuffix:this.options.simplifyPluralSuffix}),c&&(!this.options.interpolation.format||this.options.interpolation.format===s.interpolation.format)&&(u.formatter=r(c),u.formatter.init(u,this.options),this.options.interpolation.format=u.formatter.format.bind(u.formatter)),u.interpolator=new eu(this.options),u.utils={hasLoadedNamespace:this.hasLoadedNamespace.bind(this)},u.backendConnector=new ou(r(this.modules.backend),u.resourceStore,u,this.options),u.backendConnector.on("*",(h,...m)=>{this.emit(h,...m)}),this.modules.languageDetector&&(u.languageDetector=r(this.modules.languageDetector),u.languageDetector.init&&u.languageDetector.init(u,this.options.detection,this.options)),this.modules.i18nFormat&&(u.i18nFormat=r(this.modules.i18nFormat),u.i18nFormat.init&&u.i18nFormat.init(this)),this.translator=new Ot(this.services,this.options),this.translator.on("*",(h,...m)=>{this.emit(h,...m)}),this.modules.external.forEach(h=>{h.init&&h.init(this)})}if(this.format=this.options.interpolation.format,n||(n=bt),this.options.fallbackLng&&!this.services.languageDetector&&!this.options.lng){const c=this.services.languageUtils.getFallbackCodes(this.options.fallbackLng);c.length>0&&c[0]!=="dev"&&(this.options.lng=c[0])}!this.services.languageDetector&&!this.options.lng&&this.logger.warn("init: no languageDetector is used and no lng is defined"),["getResource","hasResourceBundle","getResourceBundle","getDataByLanguage"].forEach(c=>{this[c]=(...f)=>this.store[c](...f)}),["addResource","addResources","addResourceBundle","removeResourceBundle"].forEach(c=>{this[c]=(...f)=>(this.store[c](...f),this)});const l=Je(),d=()=>{const c=(f,u)=>{this.isInitializing=!1,this.isInitialized&&!this.initializedStoreOnce&&this.logger.warn("init: i18next is already initialized. You should call init just once!"),this.isInitialized=!0,this.options.isClone||this.logger.log("initialized",this.options),this.emit("initialized",this.options),l.resolve(u),n(f,u)};if(this.languages&&!this.isInitialized)return c(null,this.t.bind(this));this.changeLanguage(this.options.lng,c)};return this.options.resources||!this.options.initAsync?d():setTimeout(d,0),l}loadResources(e,n=bt){var i,a;let s=n;const r=A(e)?e:this.language;if(typeof e=="function"&&(s=e),!this.options.resources||this.options.partialBundledLanguages){if((r==null?void 0:r.toLowerCase())==="cimode"&&(!this.options.preload||this.options.preload.length===0))return s();const l=[],d=c=>{if(!c||c==="cimode")return;this.services.languageUtils.toResolveHierarchy(c).forEach(u=>{u!=="cimode"&&l.indexOf(u)<0&&l.push(u)})};r?d(r):this.services.languageUtils.getFallbackCodes(this.options.fallbackLng).forEach(f=>d(f)),(a=(i=this.options.preload)==null?void 0:i.forEach)==null||a.call(i,c=>d(c)),this.services.backendConnector.load(l,this.options.ns,c=>{!c&&!this.resolvedLanguage&&this.language&&this.setResolvedLanguage(this.language),s(c)})}else s(null)}reloadResources(e,n,s){const r=Je();return typeof e=="function"&&(s=e,e=void 0),typeof n=="function"&&(s=n,n=void 0),e||(e=this.languages),n||(n=this.options.ns),s||(s=bt),this.services.backendConnector.reload(e,n,i=>{r.resolve(),s(i)}),r}use(e){if(!e)throw new Error("You are passing an undefined module! Please check the object you are passing to i18next.use()");if(!e.type)throw new Error("You are passing a wrong module! Please check the object you are passing to i18next.use()");return e.type==="backend"&&(this.modules.backend=e),(e.type==="logger"||e.log&&e.warn&&e.error)&&(this.modules.logger=e),e.type==="languageDetector"&&(this.modules.languageDetector=e),e.type==="i18nFormat"&&(this.modules.i18nFormat=e),e.type==="postProcessor"&&wo.addPostProcessor(e),e.type==="formatter"&&(this.modules.formatter=e),e.type==="3rdParty"&&this.modules.external.push(e),this}setResolvedLanguage(e){if(!(!e||!this.languages)&&!(["cimode","dev"].indexOf(e)>-1)){for(let n=0;n<this.languages.length;n++){const s=this.languages[n];if(!(["cimode","dev"].indexOf(s)>-1)&&this.store.hasLanguageSomeTranslations(s)){this.resolvedLanguage=s;break}}!this.resolvedLanguage&&this.languages.indexOf(e)<0&&this.store.hasLanguageSomeTranslations(e)&&(this.resolvedLanguage=e,this.languages.unshift(e))}}changeLanguage(e,n){this.isLanguageChangingTo=e;const s=Je();this.emit("languageChanging",e);const r=l=>{this.language=l,this.languages=this.services.languageUtils.toResolveHierarchy(l),this.resolvedLanguage=void 0,this.setResolvedLanguage(l)},i=(l,d)=>{d?this.isLanguageChangingTo===e&&(r(d),this.translator.changeLanguage(d),this.isLanguageChangingTo=void 0,this.emit("languageChanged",d),this.logger.log("languageChanged",d)):this.isLanguageChangingTo=void 0,s.resolve((...c)=>this.t(...c)),n&&n(l,(...c)=>this.t(...c))},a=l=>{var f,u;!e&&!l&&this.services.languageDetector&&(l=[]);const d=A(l)?l:l&&l[0],c=this.store.hasLanguageSomeTranslations(d)?d:this.services.languageUtils.getBestMatchFromCodes(A(l)?[l]:l);c&&(this.language||r(c),this.translator.language||this.translator.changeLanguage(c),(u=(f=this.services.languageDetector)==null?void 0:f.cacheUserLanguage)==null||u.call(f,c)),this.loadResources(c,h=>{i(h,c)})};return!e&&this.services.languageDetector&&!this.services.languageDetector.async?a(this.services.languageDetector.detect()):!e&&this.services.languageDetector&&this.services.languageDetector.async?this.services.languageDetector.detect.length===0?this.services.languageDetector.detect().then(a):this.services.languageDetector.detect(a):a(e),s}getFixedT(e,n,s){const r=(i,a,...l)=>{let d;typeof a!="object"?d=this.options.overloadTranslationOptionHandler([i,a].concat(l)):d={...a},d.lng=d.lng||r.lng,d.lngs=d.lngs||r.lngs,d.ns=d.ns||r.ns,d.keyPrefix!==""&&(d.keyPrefix=d.keyPrefix||s||r.keyPrefix);const c=this.options.keySeparator||".";let f;return d.keyPrefix&&Array.isArray(i)?f=i.map(u=>`${d.keyPrefix}${c}${u}`):f=d.keyPrefix?`${d.keyPrefix}${c}${i}`:i,this.t(f,d)};return A(e)?r.lng=e:r.lngs=e,r.ns=n,r.keyPrefix=s,r}t(...e){var n;return(n=this.translator)==null?void 0:n.translate(...e)}exists(...e){var n;return(n=this.translator)==null?void 0:n.exists(...e)}setDefaultNamespace(e){this.options.defaultNS=e}hasLoadedNamespace(e,n={}){if(!this.isInitialized)return this.logger.warn("hasLoadedNamespace: i18next was not initialized",this.languages),!1;if(!this.languages||!this.languages.length)return this.logger.warn("hasLoadedNamespace: i18n.languages were undefined or empty",this.languages),!1;const s=n.lng||this.resolvedLanguage||this.languages[0],r=this.options?this.options.fallbackLng:!1,i=this.languages[this.languages.length-1];if(s.toLowerCase()==="cimode")return!0;const a=(l,d)=>{const c=this.services.backendConnector.state[`${l}|${d}`];return c===-1||c===0||c===2};if(n.precheck){const l=n.precheck(this,a);if(l!==void 0)return l}return!!(this.hasResourceBundle(s,e)||!this.services.backendConnector.backend||this.options.resources&&!this.options.partialBundledLanguages||a(s,e)&&(!r||a(i,e)))}loadNamespaces(e,n){const s=Je();return this.options.ns?(A(e)&&(e=[e]),e.forEach(r=>{this.options.ns.indexOf(r)<0&&this.options.ns.push(r)}),this.loadResources(r=>{s.resolve(),n&&n(r)}),s):(n&&n(),Promise.resolve())}loadLanguages(e,n){const s=Je();A(e)&&(e=[e]);const r=this.options.preload||[],i=e.filter(a=>r.indexOf(a)<0&&this.services.languageUtils.isSupportedCode(a));return i.length?(this.options.preload=r.concat(i),this.loadResources(a=>{s.resolve(),n&&n(a)}),s):(n&&n(),Promise.resolve())}dir(e){var r,i;if(e||(e=this.resolvedLanguage||(((r=this.languages)==null?void 0:r.length)>0?this.languages[0]:this.language)),!e)return"rtl";const n=["ar","shu","sqr","ssh","xaa","yhd","yud","aao","abh","abv","acm","acq","acw","acx","acy","adf","ads","aeb","aec","afb","ajp","apc","apd","arb","arq","ars","ary","arz","auz","avl","ayh","ayl","ayn","ayp","bbz","pga","he","iw","ps","pbt","pbu","pst","prp","prd","ug","ur","ydd","yds","yih","ji","yi","hbo","men","xmn","fa","jpr","peo","pes","prs","dv","sam","ckb"],s=((i=this.services)==null?void 0:i.languageUtils)||new hs(bs());return n.indexOf(s.getLanguagePartFromCode(e))>-1||e.toLowerCase().indexOf("-arab")>1?"rtl":"ltr"}static createInstance(e={},n){return new it(e,n)}cloneInstance(e={},n=bt){const s=e.forkResourceStore;s&&delete e.forkResourceStore;const r={...this.options,...e,isClone:!0},i=new it(r);if((e.debug!==void 0||e.prefix!==void 0)&&(i.logger=i.logger.clone(e)),["store","services","language"].forEach(l=>{i[l]=this[l]}),i.services={...this.services},i.services.utils={hasLoadedNamespace:i.hasLoadedNamespace.bind(i)},s){const l=Object.keys(this.store.data).reduce((d,c)=>(d[c]={...this.store.data[c]},d[c]=Object.keys(d[c]).reduce((f,u)=>(f[u]={...d[c][u]},f),d[c]),d),{});i.store=new ds(l,r),i.services.resourceStore=i.store}return i.translator=new Ot(i.services,r),i.translator.on("*",(l,...d)=>{i.emit(l,...d)}),i.init(r,n),i.translator.options=r,i.translator.backendConnector.services.utils={hasLoadedNamespace:i.hasLoadedNamespace.bind(i)},i}toJSON(){return{options:this.options,store:this.store,language:this.language,languages:this.languages,resolvedLanguage:this.resolvedLanguage}}}const oe=it.createInstance();oe.createInstance=it.createInstance;oe.createInstance;oe.dir;oe.init;oe.loadResources;oe.reloadResources;oe.use;oe.changeLanguage;oe.getFixedT;oe.t;oe.exists;oe.setDefaultNamespace;oe.hasLoadedNamespace;oe.loadNamespaces;oe.loadLanguages;const{slice:au,forEach:lu}=[];function cu(t){return lu.call(au.call(arguments,1),e=>{if(e)for(const n in e)t[n]===void 0&&(t[n]=e[n])}),t}function du(t){return typeof t!="string"?!1:[/<\s*script.*?>/i,/<\s*\/\s*script\s*>/i,/<\s*img.*?on\w+\s*=/i,/<\s*\w+\s*on\w+\s*=.*?>/i,/javascript\s*:/i,/vbscript\s*:/i,/expression\s*\(/i,/eval\s*\(/i,/alert\s*\(/i,/document\.cookie/i,/document\.write\s*\(/i,/window\.location/i,/innerHTML/i].some(n=>n.test(t))}const vs=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/,uu=function(t,e){const s=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{path:"/"},r=encodeURIComponent(e);let i=`${t}=${r}`;if(s.maxAge>0){const a=s.maxAge-0;if(Number.isNaN(a))throw new Error("maxAge should be a Number");i+=`; Max-Age=${Math.floor(a)}`}if(s.domain){if(!vs.test(s.domain))throw new TypeError("option domain is invalid");i+=`; Domain=${s.domain}`}if(s.path){if(!vs.test(s.path))throw new TypeError("option path is invalid");i+=`; Path=${s.path}`}if(s.expires){if(typeof s.expires.toUTCString!="function")throw new TypeError("option expires is invalid");i+=`; Expires=${s.expires.toUTCString()}`}if(s.httpOnly&&(i+="; HttpOnly"),s.secure&&(i+="; Secure"),s.sameSite)switch(typeof s.sameSite=="string"?s.sameSite.toLowerCase():s.sameSite){case!0:i+="; SameSite=Strict";break;case"lax":i+="; SameSite=Lax";break;case"strict":i+="; SameSite=Strict";break;case"none":i+="; SameSite=None";break;default:throw new TypeError("option sameSite is invalid")}return s.partitioned&&(i+="; Partitioned"),i},ws={create(t,e,n,s){let r=arguments.length>4&&arguments[4]!==void 0?arguments[4]:{path:"/",sameSite:"strict"};n&&(r.expires=new Date,r.expires.setTime(r.expires.getTime()+n*60*1e3)),s&&(r.domain=s),document.cookie=uu(t,encodeURIComponent(e),r)},read(t){const e=`${t}=`,n=document.cookie.split(";");for(let s=0;s<n.length;s++){let r=n[s];for(;r.charAt(0)===" ";)r=r.substring(1,r.length);if(r.indexOf(e)===0)return r.substring(e.length,r.length)}return null},remove(t){this.create(t,"",-1)}};var fu={name:"cookie",lookup(t){let{lookupCookie:e}=t;if(e&&typeof document<"u")return ws.read(e)||void 0},cacheUserLanguage(t,e){let{lookupCookie:n,cookieMinutes:s,cookieDomain:r,cookieOptions:i}=e;n&&typeof document<"u"&&ws.create(n,t,s,r,i)}},hu={name:"querystring",lookup(t){var s;let{lookupQuerystring:e}=t,n;if(typeof window<"u"){let{search:r}=window.location;!window.location.search&&((s=window.location.hash)==null?void 0:s.indexOf("?"))>-1&&(r=window.location.hash.substring(window.location.hash.indexOf("?")));const a=r.substring(1).split("&");for(let l=0;l<a.length;l++){const d=a[l].indexOf("=");d>0&&a[l].substring(0,d)===e&&(n=a[l].substring(d+1))}}return n}};let Ze=null;const js=()=>{if(Ze!==null)return Ze;try{if(Ze=typeof window<"u"&&window.localStorage!==null,!Ze)return!1;const t="i18next.translate.boo";window.localStorage.setItem(t,"foo"),window.localStorage.removeItem(t)}catch{Ze=!1}return Ze};var mu={name:"localStorage",lookup(t){let{lookupLocalStorage:e}=t;if(e&&js())return window.localStorage.getItem(e)||void 0},cacheUserLanguage(t,e){let{lookupLocalStorage:n}=e;n&&js()&&window.localStorage.setItem(n,t)}};let ze=null;const Ns=()=>{if(ze!==null)return ze;try{if(ze=typeof window<"u"&&window.sessionStorage!==null,!ze)return!1;const t="i18next.translate.boo";window.sessionStorage.setItem(t,"foo"),window.sessionStorage.removeItem(t)}catch{ze=!1}return ze};var gu={name:"sessionStorage",lookup(t){let{lookupSessionStorage:e}=t;if(e&&Ns())return window.sessionStorage.getItem(e)||void 0},cacheUserLanguage(t,e){let{lookupSessionStorage:n}=e;n&&Ns()&&window.sessionStorage.setItem(n,t)}},pu={name:"navigator",lookup(t){const e=[];if(typeof navigator<"u"){const{languages:n,userLanguage:s,language:r}=navigator;if(n)for(let i=0;i<n.length;i++)e.push(n[i]);s&&e.push(s),r&&e.push(r)}return e.length>0?e:void 0}},xu={name:"htmlTag",lookup(t){let{htmlTag:e}=t,n;const s=e||(typeof document<"u"?document.documentElement:null);return s&&typeof s.getAttribute=="function"&&(n=s.getAttribute("lang")),n}},bu={name:"path",lookup(t){var r;let{lookupFromPathIndex:e}=t;if(typeof window>"u")return;const n=window.location.pathname.match(/\/([a-zA-Z-]*)/g);return Array.isArray(n)?(r=n[typeof e=="number"?e:0])==null?void 0:r.replace("/",""):void 0}},yu={name:"subdomain",lookup(t){var r,i;let{lookupFromSubdomainIndex:e}=t;const n=typeof e=="number"?e+1:1,s=typeof window<"u"&&((i=(r=window.location)==null?void 0:r.hostname)==null?void 0:i.match(/^(\w{2,5})\.(([a-z0-9-]{1,63}\.[a-z]{2,6})|localhost)/i));if(s)return s[n]}};let jo=!1;try{document.cookie,jo=!0}catch{}const No=["querystring","cookie","localStorage","sessionStorage","navigator","htmlTag"];jo||No.splice(1,1);const vu=()=>({order:No,lookupQuerystring:"lng",lookupCookie:"i18next",lookupLocalStorage:"i18nextLng",lookupSessionStorage:"i18nextLng",caches:["localStorage"],excludeCacheFor:["cimode"],convertDetectedLanguage:t=>t});class Co{constructor(e){let n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};this.type="languageDetector",this.detectors={},this.init(e,n)}init(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{languageUtils:{}},n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},s=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};this.services=e,this.options=cu(n,this.options||{},vu()),typeof this.options.convertDetectedLanguage=="string"&&this.options.convertDetectedLanguage.indexOf("15897")>-1&&(this.options.convertDetectedLanguage=r=>r.replace("-","_")),this.options.lookupFromUrlIndex&&(this.options.lookupFromPathIndex=this.options.lookupFromUrlIndex),this.i18nOptions=s,this.addDetector(fu),this.addDetector(hu),this.addDetector(mu),this.addDetector(gu),this.addDetector(pu),this.addDetector(xu),this.addDetector(bu),this.addDetector(yu)}addDetector(e){return this.detectors[e.name]=e,this}detect(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:this.options.order,n=[];return e.forEach(s=>{if(this.detectors[s]){let r=this.detectors[s].lookup(this.options);r&&typeof r=="string"&&(r=[r]),r&&(n=n.concat(r))}}),n=n.filter(s=>s!=null&&!du(s)).map(s=>this.options.convertDetectedLanguage(s)),this.services&&this.services.languageUtils&&this.services.languageUtils.getBestMatchFromCodes?n:n.length>0?n[0]:null}cacheUserLanguage(e){let n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:this.options.caches;n&&(this.options.excludeCacheFor&&this.options.excludeCacheFor.indexOf(e)>-1||n.forEach(s=>{this.detectors[s]&&this.detectors[s].cacheUserLanguage(e,this.options)}))}}Co.type="languageDetector";const wu={home:"Home",browse:"Browse",bookmarks:"Bookmarks",history:"History",profile:"Profile",admin:"Admin",login:"Login",logout:"Logout",register:"Register"},ju={search:"Search",filter:"Filter",sort:"Sort",save:"Save",cancel:"Cancel",delete:"Delete",edit:"Edit",view:"View",back:"Back",next:"Next",previous:"Previous",submit:"Submit",reset:"Reset"},Nu={loading:"Loading...",error:"Error",success:"Success",noData:"No data available",notFound:"Not found"},Cu={email:"Email",password:"Password",confirmPassword:"Confirm Password",username:"Username",required:"This field is required",invalidEmail:"Invalid email format",passwordMismatch:"Passwords do not match",minLength:"Minimum {{count}} characters required"},Mu={light:"Light",dark:"Dark",system:"System"},Su={en:"English",vi:"Tiếng Việt"},ku={navigation:wu,actions:ju,status:Nu,forms:Cu,theme:Mu,language:Su},Vu="Manga",Hu={author:"Author",artist:"Artist",status:"Status",genres:"Genres",rating:"Rating",chapters:"Chapters",views:"Views",description:"Description",alternativeTitle:"Alternative Title"},Ru={ongoing:"Ongoing",completed:"Completed",hiatus:"Hiatus",cancelled:"Cancelled"},Pu={readNow:"Read Now",addBookmark:"Add to Bookmarks",removeBookmark:"Remove from Bookmarks",continueReading:"Continue Reading",startReading:"Start Reading",rate:"Rate this manga",comment:"Leave a comment"},Ou={placeholder:"Search manga...",noResults:"No manga found",filters:{all:"All",genre:"Genre",status:"Status",sortBy:"Sort by",sortOrder:"Order"},sortOptions:{title:"Title",rating:"Rating",views:"Views",updated:"Last Updated",created:"Date Added"}},Lu={title:"Comments",noComments:"No comments yet",writeComment:"Write a comment...",rating:"Your rating"},Au={title:Vu,details:Hu,status:Ru,actions:Pu,search:Ou,comments:Lu},Tu="Reader",Eu={previousChapter:"Previous Chapter",nextChapter:"Next Chapter",chapterList:"Chapter List",goToPage:"Go to page",pageOf:"Page {{current}} of {{total}}"},Iu={title:"Reading Settings",readingMode:"Reading Mode",readingDirection:"Reading Direction",pageFit:"Page Fit",brightness:"Brightness",backgroundColor:"Background Color"},Fu={single:"Single Page",double:"Double Page",webtoon:"Webtoon"},Du={ltr:"Left to Right",rtl:"Right to Left"},_u={width:"Fit Width",height:"Fit Height",auto:"Auto Fit"},$u={fullscreen:"Fullscreen",exitFullscreen:"Exit Fullscreen",settings:"Settings",bookmark:"Bookmark",share:"Share"},Zu={title:"Keyboard Shortcuts",nextPage:"Next Page",previousPage:"Previous Page",firstPage:"First Page",lastPage:"Last Page",toggleFullscreen:"Toggle Fullscreen",toggleSettings:"Toggle Settings"},zu={title:Tu,navigation:Eu,settings:Iu,modes:Fu,directions:Du,pageFit:_u,controls:$u,shortcuts:Zu},Bu={home:"Trang chủ",browse:"Duyệt",bookmarks:"Đánh dấu",history:"Lịch sử",profile:"Hồ sơ",admin:"Quản trị",login:"Đăng nhập",logout:"Đăng xuất",register:"Đăng ký"},Ku={search:"Tìm kiếm",filter:"Lọc",sort:"Sắp xếp",save:"Lưu",cancel:"Hủy",delete:"Xóa",edit:"Sửa",view:"Xem",back:"Quay lại",next:"Tiếp theo",previous:"Trước đó",submit:"Gửi",reset:"Đặt lại"},Uu={loading:"Đang tải...",error:"Lỗi",success:"Thành công",noData:"Không có dữ liệu",notFound:"Không tìm thấy"},Gu={email:"Email",password:"Mật khẩu",confirmPassword:"Xác nhận mật khẩu",username:"Tên người dùng",required:"Trường này là bắt buộc",invalidEmail:"Định dạng email không hợp lệ",passwordMismatch:"Mật khẩu không khớp",minLength:"Tối thiểu {{count}} ký tự"},Wu={light:"Sáng",dark:"Tối",system:"Hệ thống"},qu={en:"English",vi:"Tiếng Việt"},Yu={navigation:Bu,actions:Ku,status:Uu,forms:Gu,theme:Wu,language:qu},Xu="Truyện tranh",Ju={author:"Tác giả",artist:"Họa sĩ",status:"Trạng thái",genres:"Thể loại",rating:"Đánh giá",chapters:"Chương",views:"Lượt xem",description:"Mô tả",alternativeTitle:"Tên khác"},Qu={ongoing:"Đang tiến hành",completed:"Hoàn thành",hiatus:"Tạm dừng",cancelled:"Đã hủy"},ef={readNow:"Đọc ngay",addBookmark:"Thêm vào đánh dấu",removeBookmark:"Xóa khỏi đánh dấu",continueReading:"Tiếp tục đọc",startReading:"Bắt đầu đọc",rate:"Đánh giá truyện này",comment:"Để lại bình luận"},tf={placeholder:"Tìm kiếm truyện...",noResults:"Không tìm thấy truyện nào",filters:{all:"Tất cả",genre:"Thể loại",status:"Trạng thái",sortBy:"Sắp xếp theo",sortOrder:"Thứ tự"},sortOptions:{title:"Tiêu đề",rating:"Đánh giá",views:"Lượt xem",updated:"Cập nhật gần nhất",created:"Ngày thêm"}},nf={title:"Bình luận",noComments:"Chưa có bình luận nào",writeComment:"Viết bình luận...",rating:"Đánh giá của bạn"},sf={title:Xu,details:Ju,status:Qu,actions:ef,search:tf,comments:nf},rf="Đọc truyện",of={previousChapter:"Chương trước",nextChapter:"Chương sau",chapterList:"Danh sách chương",goToPage:"Đến trang",pageOf:"Trang {{current}} / {{total}}"},af={title:"Cài đặt đọc",readingMode:"Chế độ đọc",readingDirection:"Hướng đọc",pageFit:"Khớp trang",brightness:"Độ sáng",backgroundColor:"Màu nền"},lf={single:"Một trang",double:"Hai trang",webtoon:"Webtoon"},cf={ltr:"Trái sang phải",rtl:"Phải sang trái"},df={width:"Khớp chiều rộng",height:"Khớp chiều cao",auto:"Tự động"},uf={fullscreen:"Toàn màn hình",exitFullscreen:"Thoát toàn màn hình",settings:"Cài đặt",bookmark:"Đánh dấu",share:"Chia sẻ"},ff={title:"Phím tắt",nextPage:"Trang sau",previousPage:"Trang trước",firstPage:"Trang đầu",lastPage:"Trang cuối",toggleFullscreen:"Bật/tắt toàn màn hình",toggleSettings:"Bật/tắt cài đặt"},hf={title:rf,navigation:of,settings:af,modes:lf,directions:cf,pageFit:df,controls:uf,shortcuts:ff},mf={en:{common:ku,manga:Au,reader:zu},vi:{common:Yu,manga:sf,reader:hf}};oe.use(Co).use(oi).init({resources:mf,fallbackLng:"en",debug:!1,defaultNS:"common",ns:["common","manga","reader"],interpolation:{escapeValue:!1},detection:{order:["localStorage","navigator","htmlTag"],caches:["localStorage"]}});function gf(){return o.jsxs(Ro,{client:Go,children:[o.jsx($d,{}),!1]})}Uo.createRoot(document.getElementById("root")).render(o.jsx(x.StrictMode,{children:o.jsx(gf,{})}));
//# sourceMappingURL=index-CxbFxiOV.js.map
