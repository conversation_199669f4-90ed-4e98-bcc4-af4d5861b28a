# BlogTruyen - Manga Reading Website

Modern manga reading website built with React 18, TypeScript, Vite, and a comprehensive theme system.

## 🚀 Tech Stack

- **React 18** - UI Framework with Concurrent Features
- **TypeScript** - Type Safety
- **Vite** - Build Tool & Dev Server
- **Tailwind CSS v3.4.17** - Utility-first CSS Framework
- **shadcn/ui** - Component Library built on Radix UI
- **Zustand** - State Management
- **TanStack Query** - Server State Management
- **React Router v6** - Client-side Routing
- **React i18next** - Internationalization

## 🎨 Theme System

BlogTruyen features a comprehensive theme system with brand colors:

- **Primary**: `#E40066` (Pink/Magenta)
- **Secondary**: `#03CEA4` (Teal/Cyan)
- **Accent**: `#9747FF` (Purple)

### Features:

- 🌓 Dark/Light mode with system detection
- 🎨 Brand color system with gradients
- 📱 Responsive design
- ♿ Accessibility compliant
- 🔧 Customizable theme configuration

Visit `/theme` to see the theme showcase and interactive examples.

## 🏷️ Logo System

BlogTruyen features a professional logo system with multiple variants:

### Logo Variants:

- **Default**: Adapts to current theme
- **Brand**: Uses primary brand color (#E40066)
- **White**: For dark backgrounds
- **Dark**: For light backgrounds

### Usage:

```tsx
import { Logo, LogoIcon } from './components';

// Full logo with text
<Logo variant="brand" size="lg" />

// Icon only
<LogoIcon variant="brand" size={32} />
```

Visit `/logo` to see the complete logo showcase and usage guidelines.

## 📁 Project Structure

```
src/
├── components/           # Reusable UI components
│   ├── ui/              # shadcn/ui components
│   ├── common/          # Common components (ThemeToggle, BrandShowcase)
│   ├── forms/           # Form components
│   └── layout/          # Layout components (MainLayout, AuthLayout, etc.)
├── pages/               # Page components
├── hooks/               # Custom React hooks (useTheme)
├── stores/              # Zustand stores (auth, manga, reader)
├── services/            # API services
├── types/               # TypeScript type definitions
├── utils/               # Utility functions
├── constants/           # Constants and enums
├── i18n/               # Internationalization files
├── styles/             # Theme configuration
└── router/             # Router configuration
```

## 🛠️ Getting Started

### Prerequisites

- Node.js 18+
- npm or yarn

### Installation

1. Clone the repository:

```bash
git clone <repository-url>
cd BlogTruyen
```

2. Install dependencies:

```bash
npm install
```

3. Copy environment variables:

```bash
cp .env.example .env
```

4. Start development server:

```bash
npm run dev
```

The application will be available at `http://localhost:3000`

## 📜 Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production (outputs to `build/` folder)
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint
- `npm run lint:fix` - Fix ESLint errors
- `npm run type-check` - Run TypeScript type checking
- `npm run format` - Format code with Prettier
- `npm run clean` - Clean build artifacts

## 🌟 Features

### Implemented

- ✅ Modern React 18 setup with TypeScript
- ✅ **Complete Theme System** with brand colors
- ✅ **Professional Logo System** with multiple variants
- ✅ **Flexible Layout System** (MainLayout, AuthLayout, ReaderLayout, AdminLayout)
- ✅ Dark/Light theme support with system detection
- ✅ Authentication store with Zustand
- ✅ Internationalization (English/Vietnamese)
- ✅ Component library with shadcn/ui
- ✅ State management with Zustand
- ✅ API integration with TanStack Query
- ✅ Routing with React Router v6
- ✅ Responsive design with mobile-first approach

### Planned

- 🔄 Manga catalog with search and filters
- 🔄 Manga reader interface
- 🔄 User bookmarks and reading history
- 🔄 Comments and rating system
- 🔄 Admin panel for content management
- 🔄 PWA support
- 🔄 Offline reading capabilities

## 🎨 Theme Usage

### Tailwind Classes

```jsx
// Brand colors
<div className="bg-brand-primary text-white">Primary</div>
<div className="bg-brand-secondary text-white">Secondary</div>
<div className="bg-brand-accent text-white">Accent</div>

// Gradients
<div className="bg-gradient-primary">Primary Gradient</div>
<div className="bg-gradient-hero">Hero Gradient</div>

// Shadows
<div className="shadow-primary">Primary Shadow</div>
```

### Theme Hook

```jsx
import { useTheme } from "./hooks/useTheme";

function MyComponent() {
  const { theme, toggleTheme, setTheme } = useTheme();

  return <button onClick={toggleTheme}>Current: {theme}</button>;
}
```

## 🏗️ Layout System

The project uses a flexible layout system to avoid code duplication:

- **MainLayout** - Standard layout with header/footer
- **AuthLayout** - Authentication wrapper with redirect logic
- **ReaderLayout** - Minimal layout for reading pages
- **AdminLayout** - Admin panel with sidebar
- **LayoutWrapper** - Utility wrapper component

### Route Structure

```
/ (MainLayout)
├── / (HomePage)
├── /browse (BrowsePage)
├── /manga/:id (MangaDetailPage)
└── /theme (ThemePage)

/auth (MainLayout + AuthLayout)
├── /auth/login (LoginPage)
└── /auth/register (RegisterPage)

/user (MainLayout + AuthLayout - Protected)
├── /user/bookmarks (BookmarksPage)
├── /user/history (HistoryPage)
└── /user/profile (ProfilePage)

/read (ReaderLayout)
└── /read/:chapterId (ReaderPage)

/admin (AdminLayout - Admin Only)
├── /admin (Dashboard)
├── /admin/manga (Manga Management)
└── /admin/users (User Management)
```

## 🌐 Internationalization

Supports multiple languages:

- English (en)
- Vietnamese (vi)

Language files are located in `src/i18n/locales/`

## 📱 Responsive Design

- Mobile-first approach
- Responsive breakpoints (sm, md, lg, xl, 2xl)
- Touch-friendly interface
- Progressive Web App ready

## 🔧 Configuration

### Environment Variables

```env
VITE_API_BASE_URL=http://localhost:3001/api
VITE_APP_NAME=BlogTruyen
VITE_APP_VERSION=1.0.0
VITE_ENABLE_DEVTOOLS=true
```

### Build Configuration

- Output directory: `build/`
- Source maps enabled
- Code splitting for optimal loading
- Asset optimization

## 📚 Documentation

- `LAYOUT_STRUCTURE.md` - Detailed layout system documentation
- `THEME_SYSTEM.md` - Complete theme system guide
- `LOGO_SYSTEM.md` - Professional logo system documentation

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🙏 Acknowledgments

- [shadcn/ui](https://ui.shadcn.com/) for the component library
- [Radix UI](https://www.radix-ui.com/) for accessible primitives
- [Tailwind CSS](https://tailwindcss.com/) for styling
- [TanStack Query](https://tanstack.com/query) for data fetching
