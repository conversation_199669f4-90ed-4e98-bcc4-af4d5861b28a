# BlogTruyen Logo System

## 🎨 Tổng quan

Logo system của BlogTruyen được thiết kế dựa trên SVG gốc với các biến thể màu sắc và kích thước khác nhau để phù hợp với mọi context sử dụng.

## 🏗️ Components

### 1. Logo Component
**File**: `src/components/common/Logo.tsx`

Logo chính với text "BlogTruyen", hỗ trợ nhiều variants và sizes.

```tsx
interface LogoProps {
  className?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  variant?: 'default' | 'brand' | 'white' | 'dark';
  showText?: boolean;
}
```

### 2. LogoIcon Component  
**File**: `src/components/common/LogoIcon.tsx`

Icon-only version cho compact spaces.

```tsx
interface LogoIconProps {
  className?: string;
  size?: number | string;
  variant?: 'default' | 'brand' | 'white' | 'dark';
}
```

## 🎯 Logo Variants

### Default Variant
- **Text**: `currentColor` (follows parent text color)
- **Icon**: `#FAFCFC` (light fill)
- **Stroke**: `currentColor`
- **Usage**: General purposes, adapts to theme

### Brand Variant
- **Text**: `#E40066` (Primary brand color)
- **Icon**: `#FAFCFC` (light fill)
- **Stroke**: `#E40066` (Primary brand color)
- **Usage**: Marketing materials, branding emphasis

### White Variant
- **Text**: `#FFFFFF`
- **Icon**: `#FFFFFF`
- **Stroke**: `#FFFFFF`
- **Usage**: Dark backgrounds, overlays

### Dark Variant
- **Text**: `#1F1F1F`
- **Icon**: `#FAFCFC` (light fill)
- **Stroke**: `#1F1F1F`
- **Usage**: Light backgrounds, high contrast

## 📏 Logo Sizes

### Predefined Sizes (Logo Component)
- **sm**: `h-6` (24px height)
- **md**: `h-8` (32px height) - Default
- **lg**: `h-12` (48px height)
- **xl**: `h-16` (64px height)

### Custom Sizes (LogoIcon Component)
- Accepts any number or string value
- Default: `32px`

## 🛠️ Usage Examples

### Basic Usage
```tsx
import { Logo, LogoIcon } from '../components';

// Default logo
<Logo />

// Brand variant with large size
<Logo variant="brand" size="lg" />

// Icon only with custom size
<LogoIcon variant="brand" size={40} />

// Logo without text
<Logo showText={false} />
```

### In Header/Navigation
```tsx
<Link to="/" className="flex items-center">
  <Logo size="md" variant="default" />
</Link>
```

### In Footer
```tsx
<Logo size="md" variant="default" />
```

### For Favicons/App Icons
```tsx
<LogoIcon variant="brand" size={32} />
```

## 🎨 Integration với Theme System

Logo system được tích hợp hoàn toàn với theme system:

### Automatic Theme Adaptation
- **Default variant** tự động adapt với current theme
- **Brand variant** giữ nguyên brand colors
- **White/Dark variants** cho specific backgrounds

### CSS Variables Support
```css
/* Logo sử dụng CSS variables khi variant="default" */
color: currentColor; /* Follows theme text color */
```

### Theme-aware Usage
```tsx
// Trong dark mode, có thể sử dụng:
const { actualTheme } = useTheme();

<Logo 
  variant={actualTheme === 'dark' ? 'white' : 'default'} 
  size="lg" 
/>
```

## 📱 Responsive Considerations

### Mobile Optimization
```tsx
// Responsive logo sizes
<Logo 
  size="sm" 
  className="md:hidden" // Small on mobile
/>
<Logo 
  size="md" 
  className="hidden md:block" // Medium on desktop
/>
```

### Compact Spaces
```tsx
// Sử dụng LogoIcon cho navigation items
<LogoIcon variant="brand" size={24} />
```

## ♿ Accessibility

### Screen Reader Support
```tsx
// Logo tự động có proper alt text
<Logo /> // Includes "BlogTruyen" text

// Icon-only cần aria-label
<LogoIcon 
  variant="brand" 
  size={32}
  aria-label="BlogTruyen"
/>
```

### Contrast Ratios
- **Brand variant**: High contrast với #E40066
- **White variant**: Cho dark backgrounds
- **Dark variant**: Cho light backgrounds

## 🎯 Best Practices

### ✅ Do's
- Sử dụng **brand variant** cho marketing materials
- Maintain **adequate clear space** around logo
- Sử dụng **appropriate size** cho context
- Sử dụng **white variant** trên dark backgrounds
- Keep **logo proportions** intact

### ❌ Don'ts
- Không **stretch** hoặc **distort** logo
- Không **thay đổi colors** ngoài provided variants
- Không **place logo** trên busy backgrounds
- Không sử dụng logo **quá nhỏ** (minimum 24px height)
- Không **rotate** hoặc **skew** logo

## 📄 File Structure

```
src/components/common/
├── Logo.tsx          # Main logo component
└── LogoIcon.tsx      # Icon-only component

src/pages/
└── LogoPage.tsx      # Logo showcase page

src/assets/           # Static logo files (if needed)
└── logo.svg
```

## 🚀 Demo & Testing

### Logo Showcase Page
Truy cập `/logo` để xem:
- Tất cả logo variants
- Different sizes
- Usage examples
- Code snippets
- Guidelines

### Interactive Examples
```tsx
// Test different variants
const variants = ['default', 'brand', 'white', 'dark'];
const sizes = ['sm', 'md', 'lg', 'xl'];

{variants.map(variant => 
  sizes.map(size => (
    <Logo key={`${variant}-${size}`} variant={variant} size={size} />
  ))
)}
```

## 🔧 Customization

### Adding New Variants
1. Update `LogoProps` interface
2. Add color logic in `getColors()` function
3. Update documentation

### Custom Styling
```tsx
// Additional styling với className
<Logo 
  variant="brand" 
  size="lg"
  className="drop-shadow-lg hover:scale-105 transition-transform"
/>
```

### SVG Optimization
- Logo SVG được optimize cho web
- Supports scaling without quality loss
- Minimal file size impact

## 📚 Technical Details

### SVG Structure
- **Text portion**: "vuki" text với custom font
- **Icon portion**: Geometric design với mask
- **Scalable**: Vector-based, infinite scaling
- **Accessible**: Proper semantic structure

### Performance
- **Inline SVG**: No additional HTTP requests
- **Tree-shakable**: Only used variants included
- **Optimized**: Minimal DOM impact
- **Cached**: Component-level optimization

## 🎨 Brand Guidelines

### Logo Placement
- **Minimum clear space**: 0.5x logo height on all sides
- **Minimum size**: 24px height
- **Maximum size**: No limit, but consider context

### Color Usage
- **Primary**: Brand variant cho official materials
- **Secondary**: Default variant cho general use
- **Contrast**: White/Dark variants cho accessibility

### Typography Pairing
- Logo text matches với brand typography
- Consistent với overall design system
- Readable at all sizes

## 📖 Resources

- **Logo Page**: `/logo` - Interactive showcase
- **Theme Page**: `/theme` - Color system integration
- **Figma**: [Design files] (if available)
- **Brand Guidelines**: [Brand book] (if available)
